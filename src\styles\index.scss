/* 全局样式入口文件 */

// 导入变量和混合器
@import './variables.scss';
@import './mixins.scss';

// 导入通用样式
@import './common.scss';

// 导入组件样式
@import './components/table.scss';
@import './components/form.scss';
@import './components/dialog.scss';
@import './components/button.scss';
@import './components/upload.scss';

/* 全局样式覆盖 */

// 根元素样式
:root {
  // Element Plus 变量覆盖
  --el-color-primary: #{$primary-color};
  --el-color-success: #{$success-color};
  --el-color-warning: #{$warning-color};
  --el-color-danger: #{$danger-color};
  --el-color-info: #{$info-color};
  
  --el-text-color-primary: #{$text-primary};
  --el-text-color-regular: #{$text-regular};
  --el-text-color-secondary: #{$text-secondary};
  --el-text-color-placeholder: #{$text-placeholder};
  
  --el-border-color: #{$border-color};
  --el-border-color-light: #{$border-color-light};
  --el-border-color-lighter: #{$border-color-lighter};
  --el-border-color-extra-light: #{$border-color-extra-light};
  
  --el-background-color-base: #{$background-color-base};
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

// 禁用文本选择
.no-select {
  user-select: none;
} 