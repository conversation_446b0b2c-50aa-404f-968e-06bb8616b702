import { useUserStore } from '@/store/modules/user'

// 角色常量定义
export const ROLE_IDS = {
  PROJECT_LEADER: 1,    // 项目领导
  PLANNER: 2,          // 计划员
  PROJECT_MANAGER: 3,   // 项目经理
  PROJECT_MEMBER: 4     // 项目成员
}

// 角色名称映射
export const ROLE_NAMES = {
  [ROLE_IDS.PROJECT_LEADER]: '项目领导',
  [ROLE_IDS.PLANNER]: '计划员',
  [ROLE_IDS.PROJECT_MANAGER]: '项目经理',
  [ROLE_IDS.PROJECT_MEMBER]: '项目成员'
}

/**
 * 权限检查工具类
 */
export class PermissionChecker {
  constructor() {
    this.userStore = useUserStore()
  }

  // 获取当前用户角色ID
  getCurrentRoleId() {
    return this.userStore.userRole
  }

  // 获取当前用户角色名称
  getCurrentRoleName() {
    const roleId = this.getCurrentRoleId()
    return ROLE_NAMES[roleId] || '未知角色'
  }

  // 检查是否为项目领导
  isProjectLeader() {
    return this.getCurrentRoleId() === ROLE_IDS.PROJECT_LEADER
  }

  // 检查是否为计划员
  isPlanner() {
    return this.getCurrentRoleId() === ROLE_IDS.PLANNER
  }

  // 检查是否为项目经理
  isProjectManager() {
    return this.getCurrentRoleId() === ROLE_IDS.PROJECT_MANAGER
  }

  // 检查是否为项目成员
  isProjectMember() {
    return this.getCurrentRoleId() === ROLE_IDS.PROJECT_MEMBER
  }

  // 检查是否有特定权限
  hasRole(roleId) {
    return this.getCurrentRoleId() === roleId
  }

  // 检查是否有多个角色中的任意一个
  hasAnyRole(roleIds) {
    const currentRoleId = this.getCurrentRoleId()
    return roleIds.includes(currentRoleId)
  }

  // 检查是否有管理权限（项目领导或计划员）
  hasManagementPermission() {
    return this.hasAnyRole([ROLE_IDS.PROJECT_LEADER, ROLE_IDS.PLANNER])
  }

  // 检查是否可以编辑项目（计划员）
  canEditProject() {
    return this.isPlanner()
  }

  // 检查是否可以关注项目（项目领导）
  canFollowProject() {
    return this.isProjectLeader()
  }

  // 检查是否可以生成AI任务（计划员）
  canGenerateAiTask() {
    return this.isPlanner()
  }
}

// 创建全局权限检查器实例
export const permissionChecker = new PermissionChecker()

// 导出便捷的权限检查函数
export const hasRole = (roleId) => permissionChecker.hasRole(roleId)
export const hasAnyRole = (roleIds) => permissionChecker.hasAnyRole(roleIds)
export const isProjectLeader = () => permissionChecker.isProjectLeader()
export const isPlanner = () => permissionChecker.isPlanner()
export const isProjectManager = () => permissionChecker.isProjectManager()
export const isProjectMember = () => permissionChecker.isProjectMember()
export const canEditProject = () => permissionChecker.canEditProject()
export const canFollowProject = () => permissionChecker.canFollowProject()
export const canGenerateAiTask = () => permissionChecker.canGenerateAiTask()
