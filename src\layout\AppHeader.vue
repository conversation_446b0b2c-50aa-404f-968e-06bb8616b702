<template>
  <div class="app-header">
    <div class="header-title">
      <el-icon class="logo-icon" color="#5B7BFA">
        <component :is="currentPageIcon"></component>
      </el-icon>
      <span class="title-text">{{ currentPageTitle }}</span>
    </div>
    <div class="header-actions">
      <div class="divider"></div>
      <el-dropdown trigger="click" @command="handleCommand" @visible-change="handleDropdownVisibleChange">
        <div class="user-info">
          <el-avatar :size="32" class="avatar" :src="userAvatar" />
          <div class="user-details">
            <span class="user-name">{{ userName }}</span>
            <span class="user-role">{{ userRole }}</span>
          </div>
          <el-icon class="dropdown-arrow" :class="{ 'is-reverse': dropdownVisible }"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown-menu">
            <el-dropdown-item command="logout" class="dropdown-item logout-item">
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Calendar, DataBoard, Briefcase, User, ArrowDown, SwitchButton } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 下拉菜单显示状态
const dropdownVisible = ref(false)

// 用户信息
const userName = computed(() => {
  return userStore.userInfo.realName || '未登录'
})
const userRole = computed(() => {
  const roleId = userStore.userRole // 使用userStore中的roleId

  // 角色ID到角色名称的映射（基于实际API数据）
  const roleMapping = {
    1: '项目领导',
    2: '计划员',
    3: '项目经理',
    4: '项目成员'
  }

  if (roleId && roleMapping[roleId]) {
    return roleMapping[roleId]
  }

  return '未分配角色'
})
const userAvatar = computed(() => 
  userStore.userInfo.avatar || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
)

// 下拉菜单显示状态变化处理
const handleDropdownVisibleChange = (visible) => {
  dropdownVisible.value = visible
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'logout-confirm-dialog'
    }).then(() => {
      userStore.logout()
      router.push('/login')
    }).catch(() => {})
  }
}

// 根据当前路由路径计算页面标题
const currentPageTitle = computed(() => {
  if (route.meta && route.meta.title) {
    return route.meta.title
  }

  // 如果路由没有定义标题，根据路径推断
  const path = route.path
  if (path.includes('/workbench')) {
    return '工作台'
  } else if (path.includes('/projects/all')) {
    return '全部项目'
  } else if (path.includes('/projects/shenzhen')) {
    return '深圳项目'
  } else if (path.includes('/projects/wuhan')) {
    return '武汉项目'
  } else if (path.includes('/projects')) {
    return '项目管理'
  } else if (path.includes('/users')) {
    return '用户管理'
  }
  
  return '交付项目管理系统'
})

// 根据当前路由计算对应的图标
const currentPageIcon = computed(() => {
  const path = route.path
  if (path.includes('/workbench')) {
    return DataBoard
  } else if (path.includes('/projects')) {
    return Briefcase
  } else if (path.includes('/users')) {
    return User
  }
  
  return Calendar // 默认图标
})
</script>

<style scoped>
.app-header {
  height: 70px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  margin: 10px 0 10px 0;
}

.header-title {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  margin-right: 12px;
}

.title-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.divider {
  height: 24px;
  width: 1px;
  background-color: #EEEEEE;
  margin: 0 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: rgba(91, 123, 250, 0.05);
  border: 1px solid transparent;
}

.user-info:hover {
  background-color: rgba(91, 123, 250, 0.1);
  border-color: rgba(91, 123, 250, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(91, 123, 250, 0.15);
}

.avatar {
  margin-right: 12px;
  border: 2px solid rgba(91, 123, 250, 0.2);
  transition: all 0.3s ease;
}

.user-info:hover .avatar {
  border-color: rgba(91, 123, 250, 0.4);
  transform: scale(1.05);
}

.user-details {
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #666666;
  margin-top: 2px;
  padding: 2px 6px;
  background-color: rgba(91, 123, 250, 0.1);
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

/* 下拉箭头样式 */
.dropdown-arrow {
  transition: transform 0.3s ease;
  color: #666666;
}

.dropdown-arrow.is-reverse {
  transform: rotate(180deg);
}

.user-info:hover .dropdown-arrow {
  color: #5B7BFA;
}

/* 下拉菜单样式 */
:deep(.user-dropdown-menu) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.06);
  padding: 8px 0;
  min-width: 180px;
}

:deep(.dropdown-item) {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333333;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 2px 8px;
}

:deep(.dropdown-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
  color: #666666;
  transition: color 0.3s ease;
}

:deep(.dropdown-item:hover) {
  background-color: rgba(91, 123, 250, 0.08);
  color: #5B7BFA;
}

:deep(.dropdown-item:hover .el-icon) {
  color: #5B7BFA;
}

:deep(.logout-item) {
  color: #F56C6C;
}

:deep(.logout-item .el-icon) {
  color: #F56C6C;
}

:deep(.logout-item:hover) {
  background-color: rgba(245, 108, 108, 0.08);
  color: #F56C6C;
}

:deep(.logout-item:hover .el-icon) {
  color: #F56C6C;
}

/* 退出确认对话框样式 */
:deep(.logout-confirm-dialog) {
  border-radius: 12px;
}

:deep(.logout-confirm-dialog .el-message-box__header) {
  padding: 20px 20px 10px;
}

:deep(.logout-confirm-dialog .el-message-box__content) {
  padding: 10px 20px 20px;
}

:deep(.logout-confirm-dialog .el-message-box__btns) {
  padding: 10px 20px 20px;
}
</style> 