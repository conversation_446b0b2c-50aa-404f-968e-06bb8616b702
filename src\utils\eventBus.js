/**
 * 全局事件总线
 * 用于组件间通信，特别是跨页面的状态同步
 */

class EventBus {
  constructor() {
    this.events = {};
  }

  // 监听事件
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // 移除事件监听
  off(event, callback) {
    if (!this.events[event]) return;
    
    if (callback) {
      // 移除特定的回调函数
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    } else {
      // 移除所有回调函数
      delete this.events[event];
    }
  }

  // 触发事件
  emit(event, ...args) {
    if (!this.events[event]) return;
    
    this.events[event].forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`事件 ${event} 的回调函数执行出错:`, error);
      }
    });
  }

  // 只监听一次的事件
  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  // 清除所有事件监听
  clear() {
    this.events = {};
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus();

// 定义事件常量，避免字符串拼写错误
export const EVENTS = {
  // 日报相关事件
  DAILY_REPORT_SUBMITTED: 'daily_report_submitted',
  DAILY_REPORT_UPDATED: 'daily_report_updated',
  
  // 项目相关事件
  PROJECT_STATUS_CHANGED: 'project_status_changed',
  PROJECT_DATA_UPDATED: 'project_data_updated',
  
  // 工作台数据刷新事件
  WORKBENCH_REFRESH_NEEDED: 'workbench_refresh_needed'
};

export default eventBus;
