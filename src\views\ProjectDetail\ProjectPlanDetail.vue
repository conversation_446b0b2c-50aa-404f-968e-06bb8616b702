<template>
  <div class="plan-detail-content">
    <!-- 参数错误提示 -->
    <div v-if="!hasValidParams" class="error-container">
      <el-result
        icon="warning"
        title="参数错误"
        sub-title="缺少必要的项目ID或计划ID参数，无法加载计划详情"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回项目详情</el-button>
        </template>
      </el-result>
    </div>

    <!-- 正常内容 -->
    <div v-else class="main-container">
      <!-- 返回按钮和标题 -->
      <div class="back-section">
        <el-button class="back-icon-button" @click="goBack" icon="ArrowLeft" text></el-button>
        <h2>{{ projectName }}</h2>
      </div>
      
      <!-- 标签页 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basicInfo">
            <div class="info-panel">
              <el-descriptions :column="3" border class="plan-descriptions" v-loading="planDetailLoading">
                <el-descriptions-item label="前置工作项" :span="1">{{ planData.preSeriNum || '--' }}</el-descriptions-item>
                <el-descriptions-item label="责任人" :span="1">{{ planData.ownerName || '--' }}</el-descriptions-item>
                <el-descriptions-item label="执行人" :span="1">{{ planData.executorName || '--' }}</el-descriptions-item>

                <el-descriptions-item label="任务状态" :span="1">{{ planStatusText }}</el-descriptions-item>
                <el-descriptions-item label="计划版本" :span="1">{{ planData.version || '--' }}</el-descriptions-item>
                <el-descriptions-item label="审核状态" :span="1">{{ reviewStatusText }}</el-descriptions-item>
                
                <el-descriptions-item label="计划开始时间" :span="1">{{ planData.planStartTime || '--' }}</el-descriptions-item>
                <el-descriptions-item label="计划结束时间" :span="1">{{ planData.planEndTime || '--' }}</el-descriptions-item>
                <el-descriptions-item label="计划工期" :span="1">{{ planData.planDuration ? `${planData.planDuration}天` : '--' }}</el-descriptions-item>
                
                <el-descriptions-item label="实际开始时间" :span="1">{{ planData.actualStartTime || '--' }}</el-descriptions-item>
                <el-descriptions-item label="实际结束时间" :span="1">{{ planData.actualEndTime || '--' }}</el-descriptions-item>
                <el-descriptions-item label="实际工期" :span="1">{{ planData.actualDuration ? `${planData.actualDuration}天` : '--' }}</el-descriptions-item>
              </el-descriptions>
            </div>
            
            <!-- 动态信息区域 -->
            <div class="dynamic-section">
              <div class="dynamic-header">
                <h3>最新动态</h3>
                <div class="filter-buttons">
                  <el-button
                    :type="dynamicFilter === '' ? 'primary' : ''"
                    @click="handleDynamicFilterChange('')"
                    :loading="dynamicsLoading || dynamicTypesLoading"
                  >
                    全部
                  </el-button>
                  <el-button
                    v-for="(label, value) in dynamicTypes"
                    :key="value"
                    :type="dynamicFilter === value ? 'primary' : ''"
                    @click="handleDynamicFilterChange(value)"
                    :loading="dynamicsLoading || dynamicTypesLoading"
                  >
                    {{ label }}
                  </el-button>
                </div>
              </div>

              <div class="dynamic-timeline" v-loading="dynamicsLoading">
                <el-timeline v-if="dynamicsList.length > 0" class="custom-timeline">
                  <el-timeline-item
                    v-for="(activity, index) in dynamicsList"
                    :key="index"
                    :timestamp="formatDateTime(activity.dynamicTime)"
                    placement="top"
                    :hollow="true"
                    :size="'large'"
                    :type="getTimelineType(activity.actionSubType)"
                  >
                    <div class="timeline-card">
                      <div class="timeline-header">
                        <div class="timeline-user-info">
                          <div class="user-avatar">
                            <i class="el-icon-user"></i>
                          </div>
                          <div class="user-details">
                            <div class="timeline-user">{{ activity.operatorName }}</div>
                            <div class="timeline-time">{{ formatTime(activity.dynamicTime) }}</div>
                          </div>
                        </div>
                        <div class="timeline-action">
                          <span v-if="activity.actionSubType"
                                :class="['action-sub-type', getActionSubTypeClass(activity.actionSubType)]">
                            {{ getActionSubTypeDisplay(activity.actionSubType) }}
                          </span>
                        </div>
                      </div>
                      <div class="timeline-content" v-if="activity.dynamicContent">
                        <div class="timeline-desc">
                          {{ activity.dynamicContent }}
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                
                <!-- 空状态 -->
                <el-empty v-else description="暂无动态信息" :image-size="80" />
                
                <!-- 分页 -->
                <div class="pagination-container" v-if="dynamicsTotal > 0">
                  <el-pagination
                    v-model:current-page="dynamicsParams.pageNum"
                    v-model:page-size="dynamicsParams.pageSize"
                    :page-sizes="[10, 20, 50]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="dynamicsTotal"
                    @size-change="handleDynamicsSizeChange"
                    @current-change="handleDynamicsCurrentChange"
                    background
                    small
                  />
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="图片" name="images">
            <div class="table-container">
              <el-table
                :data="paginatedImageFiles"
                border
                style="width: 100%; border-radius: 8px; overflow: hidden;"
                :header-cell-style="{ background: '#f1f5f9', color: '#606266', padding: '6px 0' }"
                :cell-style="{ padding: '4px 0' }"
                :row-style="{ height: '32px' }"
                class="rounded-table"
                v-loading="attachmentsLoading"
                element-loading-text="正在加载附件列表..."
              >
                <el-table-column type="selection" width="45"></el-table-column>
                <el-table-column label="序号" type="index" width="70"></el-table-column>
                <el-table-column prop="fileName" label="文件名称"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
                <el-table-column prop="creator" label="创建人" width="100"></el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="scope">
                    <el-button type="primary" link @click="viewImage(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <!-- 分页 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="attachmentsTotal"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  background
                  small
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { getPlanDetail, getPlanDynamics, getPlanDetailMock, getProjectPlanTree } from '@/api/projectPlan';
import { getPlanStatusApi, getReviewStatusApi, getPlanDynamicTypesApi, getPlanActionSubTypesApi } from '@/api/enums';
import { getProjectAttachments } from '@/api/projectAttachment';

const route = useRoute();
const router = useRouter();
const activeTab = ref('basicInfo');
const dynamicFilter = ref(''); // 空字符串表示全部

// 获取路由参数
const projectId = route.params.projectId;
const planId = route.params.planId;
const projectName = route.query.projectName || '项目计划详情';

// 检查必要参数
const hasValidParams = projectId && planId;

// 计算属性：将码值转换为显示文本
const planStatusText = computed(() => {
  const status = planData.value.planStatus;

  // 如果状态为空或未定义，返回默认值
  if (status === null || status === undefined || status === '') {
    return '未开始';
  }

  // 尝试多种格式的键查找
  if (planStatusEnum.value) {
    // 先尝试原始值（可能是数字或字符串）
    if (planStatusEnum.value[status] !== undefined) {
      return planStatusEnum.value[status];
    }

    // 再尝试字符串格式
    const statusStr = String(status);
    if (planStatusEnum.value[statusStr] !== undefined) {
      return planStatusEnum.value[statusStr];
    }

    // 最后尝试数字格式
    const statusNum = Number(status);
    if (!isNaN(statusNum) && planStatusEnum.value[statusNum] !== undefined) {
      return planStatusEnum.value[statusNum];
    }
  }

  // 使用默认映射
  const defaultStatusMap = {
    0: '未开始',
    1: '进行中',
    2: '已完成',
    3: '逾期',
    4: '逾期完成',
    '0': '未开始',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期',
    '4': '逾期完成'
  };

  return defaultStatusMap[status] || '未开始';
});

const reviewStatusText = computed(() => {
  const status = planData.value.reviewStatus;
  return reviewStatusEnum.value[status] || status || '--';
});

// 动态类型转换函数
const getDynamicTypeText = (type) => {
  return planDynamicTypesEnum.value[type] || type || '--';
};



// 返回上一页
function goBack() {
  router.push({
    path: `/project/${projectId}`,
    query: { tab: 'plan' }
  });
}

// 计划详情数据
const planData = ref({});
const planDetailLoading = ref(false);

// 枚举数据
const planStatusEnum = ref({});
const reviewStatusEnum = ref({});
const planDynamicTypesEnum = ref({});
const planActionSubTypesEnum = ref({});
const enumsLoading = ref(false);

// 动态类型筛选相关
const dynamicTypes = computed(() => planDynamicTypesEnum.value);
const dynamicTypesLoading = computed(() => enumsLoading.value);

// 动态信息相关
const dynamicsList = ref([]);
const dynamicsTotal = ref(0);
const dynamicsLoading = ref(false);
const dynamicsParams = ref({
  pageNum: 1,
  pageSize: 10,
  planId: planId, // 保持字符串格式，避免大数字精度丢失
  projectId: projectId,
  dynamicType: 'all'
});

// 获取枚举数据
async function loadEnumData() {
  enumsLoading.value = true;
  try {
    // 并行获取所有枚举数据
    const [planStatusRes, reviewStatusRes, planDynamicTypesRes, planActionSubTypesRes] = await Promise.all([
      getPlanStatusApi().catch(() => null),
      getReviewStatusApi().catch(() => null),
      getPlanDynamicTypesApi().catch(() => null),
      getPlanActionSubTypesApi().catch(() => null)
    ]);

    // 处理计划状态枚举
    if (planStatusRes && planStatusRes.code === 200 && planStatusRes.data) {
      planStatusEnum.value = planStatusRes.data;
    } else {
      // 使用默认映射
      planStatusEnum.value = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '逾期',
        4: '逾期完成'
      };
    }

    // 处理审核状态枚举
    if (reviewStatusRes && reviewStatusRes.code === 200 && reviewStatusRes.data) {
      reviewStatusEnum.value = reviewStatusRes.data;

    } else {
      // 使用默认映射
      reviewStatusEnum.value = {
        '0': '待审核',
        '1': '审核通过',
        '2': '审核拒绝'
      };

    }

    // 处理计划动态类型枚举
    if (planDynamicTypesRes && (planDynamicTypesRes.code === 200 || planDynamicTypesRes.code === 0) && planDynamicTypesRes.data) {
      planDynamicTypesEnum.value = planDynamicTypesRes.data;
    } else {
      // 使用默认映射（与API返回格式一致）
      planDynamicTypesEnum.value = {
        "1": "操作",
        "2": "日报"
      };
    }

    // 处理计划操作子类型枚举
    if (planActionSubTypesRes && (planActionSubTypesRes.code === 200 || planActionSubTypesRes.code === 0) && planActionSubTypesRes.data) {
      planActionSubTypesEnum.value = planActionSubTypesRes.data;
    } else {
      // 使用默认映射
      planActionSubTypesEnum.value = {
        "1": "创建计划",
        "2": "修改计划",
        "3": "删除计划",
        "4": "变更计划",
        "6": "提交日报",
        "7": "修改日报"
      };
    }

  } catch (error) {
    console.error('❌ 加载枚举数据失败:', error);
    ElMessage.warning('枚举数据加载失败，使用默认映射');
  } finally {
    enumsLoading.value = false;
  }
}

// 获取计划详情
async function getPlanDetailData() {
  // 如果没有有效参数，直接返回
  if (!hasValidParams) {
    return;
  }

  planDetailLoading.value = true;

  try {
    let response;
    let usedMockData = false;

    try {
      // 首先尝试真实API
      response = await getPlanDetail({
        projectId: projectId,
        planId: planId // 保持字符串格式，避免大数字精度丢失
      });

      // 检查真实API的响应，如果返回错误则使用Mock数据
      if (response.code !== 0 && response.code !== 200) {
        throw new Error(response.msg || '真实API返回错误');
      }
    } catch (realApiError) {
      usedMockData = true;
      // 如果真实API失败，使用Mock数据
      response = await getPlanDetailMock({
        projectId: projectId,
        planId: planId // 保持字符串格式
      });
    }

    if (response.code === 0 || response.code === 200) {
      planData.value = response.data || {};
    } else {
      const errorMsg = response.msg || '获取计划详情失败';
      ElMessage.error(`${errorMsg} (项目ID: ${projectId}, 计划ID: ${planId})`);
    }
  } catch (error) {
    const errorMsg = error.message || '获取计划详情失败';
    ElMessage.error(`${errorMsg} (项目ID: ${projectId}, 计划ID: ${planId})`);
  } finally {
    planDetailLoading.value = false;
  }
}

// 获取动态信息
async function getDynamicsData() {
  dynamicsLoading.value = true;
  try {
    const params = {
      ...dynamicsParams.value,
      // 空字符串或null表示获取全部动态，否则传入具体的动态类型值
      dynamicType: dynamicFilter.value || undefined
    };

    const response = await getPlanDynamics(params);

    if (response.code === 0 || response.code === 200) {
      dynamicsList.value = response.data || [];
      dynamicsTotal.value = response.total || 0;
    } else {
      ElMessage.error(response.msg || '获取动态信息失败');
    }
  } catch (error) {
    ElMessage.error('获取动态信息失败');
  } finally {
    dynamicsLoading.value = false;
  }
}

// 处理动态过滤条件变化
function handleDynamicFilterChange(type) {
  dynamicFilter.value = type;
  dynamicsParams.value.pageNum = 1;
  getDynamicsData();
}

// 处理动态分页大小变化
function handleDynamicsSizeChange(val) {
  dynamicsParams.value.pageSize = val;
  dynamicsParams.value.pageNum = 1;
  getDynamicsData();
}

// 处理动态当前页变化
function handleDynamicsCurrentChange(val) {
  dynamicsParams.value.pageNum = val;
  getDynamicsData();
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleDateString('zh-CN');
}

// 格式化时间
function formatTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
}



// 获取操作子类型显示文本
function getActionSubTypeDisplay(actionSubType) {
  if (!actionSubType) return '';
  return planActionSubTypesEnum.value[actionSubType] || actionSubType;
}

// 获取操作子类型样式类
function getActionSubTypeClass(actionSubType) {
  const typeMap = {
    '1': 'action-create',   // 创建计划 - 绿色
    '2': 'action-modify',   // 修改计划 - 蓝色
    '3': 'action-delete',   // 删除计划 - 红色
    '4': 'action-change',   // 变更计划 - 橙色
    '6': 'action-report',   // 提交日报 - 紫色
    '7': 'action-edit-report' // 修改日报 - 青色
  };
  return typeMap[actionSubType] || 'action-default';
}

// 获取时间线节点类型
function getTimelineType(actionSubType) {
  const typeMap = {
    '1': 'success',   // 创建计划 - 绿色
    '2': 'primary',   // 修改计划 - 蓝色
    '3': 'danger',    // 删除计划 - 红色
    '4': 'warning',   // 变更计划 - 橙色
    '6': 'info',      // 提交日报 - 灰色
    '7': 'info'       // 修改日报 - 灰色
  };
  return typeMap[actionSubType] || 'primary';
}

// 附件相关数据
const attachmentsLoading = ref(false);
const imageFiles = ref([]);
const attachmentsTotal = ref(0);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 分页后的数据
const paginatedImageFiles = computed(() => {
  return imageFiles.value;
});

// 处理每页显示数量变化
function handleSizeChange(val) {
  pageSize.value = val;
  currentPage.value = 1;
  loadAttachments();
}

// 处理当前页变化
function handleCurrentChange(val) {
  currentPage.value = val;
  loadAttachments();
}

// 加载附件数据
const loadAttachments = async () => {
  if (!planId) return;

  attachmentsLoading.value = true;
  try {
    const requestData = {
      planId: planId,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await getProjectAttachments(requestData);

    if (response && (response.code === 200 || response.code === 0)) {
      if (response.data) {
        let attachments = [];
        // 检查数据结构：可能是直接数组，也可能有records字段
        if (Array.isArray(response.data)) {
          attachments = response.data;
          attachmentsTotal.value = response.data.length;
        } else if (response.data.records && Array.isArray(response.data.records)) {
          attachments = response.data.records;
          attachmentsTotal.value = response.data.total || response.data.records.length;
        }

        // 处理附件数据，映射字段
        imageFiles.value = attachments.map(attachment => ({
          id: attachment.id,
          fileName: attachment.fileName || attachment.objectName || '未命名文件',
          createTime: attachment.uploadedTime || attachment.uploadTime || attachment.createTime || '--',
          creator: attachment.uploaderName || attachment.creator || '--',
          fileUrl: attachment.url || attachment.fileUrl,
          fileSize: attachment.fileSize
        }));
      } else {
        imageFiles.value = [];
        attachmentsTotal.value = 0;
      }
    } else {
      imageFiles.value = [];
      attachmentsTotal.value = 0;
      ElMessage.warning('获取附件列表失败');
    }
  } catch (error) {
    console.error('加载附件列表失败:', error);
    ElMessage.error('加载附件列表失败');
    imageFiles.value = [];
    attachmentsTotal.value = 0;
  } finally {
    attachmentsLoading.value = false;
  }
};

// 查看图片
function viewImage(file) {
  if (file.fileUrl) {
    // 在新窗口打开图片
    window.open(file.fileUrl, '_blank');
  } else {
    ElMessage.warning('文件URL不存在，无法预览');
  }
}

onMounted(async () => {
  // 先加载枚举数据，再获取计划详情和动态信息
  await loadEnumData();
  getPlanDetailData();
  getDynamicsData();
  loadAttachments();
});
</script>

<style scoped>
.plan-detail-content {
  padding: 0;
  height: 100%;
  overflow: auto;
  background-color: #f5f7fa;
  width: 100%;
  border-radius: 8px;
}

.main-container {
  background-color: #ffffff;
  border-radius: 8px;
  margin: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.back-section {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.back-icon-button {
  margin-right: 8px;
  font-size: 16px;
  color: #409EFF;
  padding: 0;
}

.back-section h2 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #303133;
}

.tabs-section {
  padding: 12px 16px 16px;
}

/* 调整标签页样式，减少上方空间 */
:deep(.el-tabs__header) {
  margin-bottom: 12px;
}

:deep(.el-tabs__nav-wrap) {
  padding-bottom: 0;
}

:deep(.el-tabs__item) {
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

.info-panel {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  overflow: hidden;
}

.dynamic-section {
  margin-top: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.dynamic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;
}

.dynamic-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  position: relative;
  padding-left: 8px;
}

.dynamic-header h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: #409eff;
  border-radius: 1px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
}

.filter-buttons .el-button {
  border-radius: 4px;
  font-size: 13px;
  padding: 6px 12px;
}

.dynamic-timeline {
  background-color: #fff;
  padding: 20px;
  min-height: 200px;
}

/* 自定义时间线样式 */
.custom-timeline {
  padding-left: 0;
}

.custom-timeline :deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
  position: relative;
}

.custom-timeline :deep(.el-timeline-item__node) {
  width: 12px;
  height: 12px;
  border-width: 2px;
}

.custom-timeline :deep(.el-timeline-item__timestamp) {
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 6px;
}

/* 时间线卡片样式 */
.timeline-card {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 6px;
  transition: border-color 0.2s ease;
}

.timeline-card:hover {
  border-color: #d1d5db;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.timeline-user {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  margin: 0;
}

.timeline-time {
  color: #9ca3af;
  font-size: 12px;
}

.timeline-action {
  display: flex;
  align-items: center;
}

.timeline-content {
  margin-top: 8px;
}

.timeline-desc {
  background: #f9fafb;
  padding: 10px 12px;
  border-radius: 4px;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  border-left: 3px solid #e5e7eb;
  margin: 0;
}

.action-sub-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  background-color: #ffffff;
}

/* 创建计划 - 绿色系 */
.action-create {
  color: #059669;
  border-color: #d1fae5;
  background-color: #f0fdf4;
}

/* 修改计划 - 蓝色系 */
.action-modify {
  color: #2563eb;
  border-color: #dbeafe;
  background-color: #eff6ff;
}

/* 删除计划 - 红色系 */
.action-delete {
  color: #dc2626;
  border-color: #fecaca;
  background-color: #fef2f2;
}

/* 变更计划 - 橙色系 */
.action-change {
  color: #d97706;
  border-color: #fed7aa;
  background-color: #fffbeb;
}

/* 提交日报 - 紫色系 */
.action-report {
  color: #7c3aed;
  border-color: #e9d5ff;
  background-color: #faf5ff;
}

/* 修改日报 - 青色系 */
.action-edit-report {
  color: #0891b2;
  border-color: #a7f3d0;
  background-color: #f0fdfa;
}

/* 默认样式 - 灰色系 */
.action-default {
  color: #6b7280;
  border-color: #e5e7eb;
  background-color: #f9fafb;
}

.timeline-desc {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: pre-line;
  font-size: 13px;
  color: #606266;
}



/* 表格容器样式 */
.table-container {
  margin-top: 0;
  border-radius: 8px !important;
  overflow: hidden !important;
  position: relative;
}

/* 自定义滚动条样式 */
/* 整个滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 8px;
}

/* 鼠标悬停在滑块上 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}

/* 调整描述列表的宽度比例 */
.plan-descriptions {
  width: 100%;
}

:deep(.plan-descriptions .el-descriptions__body) {
  width: 100%;
}

:deep(.plan-descriptions .el-descriptions__table) {
  width: 100%;
  table-layout: fixed;
}

:deep(.plan-descriptions .el-descriptions__cell) {
  width: 33.333%;
}

:deep(.plan-descriptions .el-descriptions__label) {
  width: 25%;
  text-align: left;
  font-weight: normal;
  background-color: #f5f7fa;
  padding: 8px 12px;
  font-size: 13px;
  color: #606266;
}

:deep(.plan-descriptions .el-descriptions__content) {
  width: 75%;
  padding: 8px 12px;
  font-weight: normal;
  font-size: 13px;
  color: #303133;
  background-color: #fff;
}

/* 设置三列等宽 */
:deep(.plan-descriptions .el-descriptions__table tr td) {
  width: 33.333%;
}

:deep(.plan-descriptions .el-descriptions__table tr th) {
  width: 33.333%;
}

/* 确保每列内部的标签和内容比例合适 */
:deep(.plan-descriptions .el-descriptions__table tr th .el-descriptions__label) {
  width: 25%;
  display: inline-block;
  text-align: left;
}

:deep(.plan-descriptions .el-descriptions__table tr td .el-descriptions__content) {
  width: 75%;
  display: inline-block;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409EFF;
}

:deep(.el-pagination.is-background .el-pager li) {
  margin: 0 3px;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

:deep(.el-pagination .el-select .el-input) {
  width: 100px;
  margin: 0 5px;
}

:deep(.el-pagination__sizes) {
  margin-right: 12px;
}

:deep(.filter-buttons .el-button) {
  padding: 5px 12px;
  font-size: 12px;
  height: 28px;
  line-height: 1;
}

:deep(.el-timeline-item__node) {
  background-color: #409EFF;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

/* 完全重写表格圆角样式 */
:deep(.rounded-table) {
  border-radius: 8px !important;
  overflow: hidden !important;
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #f1f5f9 !important;
  color: #606266;
  font-weight: 500;
  height: 40px;
  padding: 8px 0;
  font-size: 13px;
  border-right: 1px solid #cbd5e1;
}

:deep(.el-table td) {
  padding: 6px 0;
  height: 40px;
  font-size: 13px;
  border-right: 1px solid #cbd5e1 !important;
  border-bottom: 1px solid #cbd5e1 !important;
}

/* 移除斑马纹样式 */

/* 确保Element Plus的滚动条组件不影响圆角 */
:deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
  margin-bottom: 0 !important;
}

/* 错误页面样式 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 20px;
}

.error-container .el-result {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 40px;
}
</style>