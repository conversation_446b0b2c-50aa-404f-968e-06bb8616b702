import request from '@/utils/request'

// 查询计划详情
export function getPlanDetail(data) {
  return request({
    url: '/plans/detail',
    method: 'post',
    data
  })
}

// 查询计划动态信息（支持类型筛选）
export function getPlanDynamics(data) {
  return request({
    url: '/plans/dynamics',
    method: 'post',
    data
  })
}

// 查询项目下的计划树结构
export function getProjectPlanTree(projectId) {
  return request({
    url: `/project-plan/planTree/${projectId}`,
    method: 'get'
  })
}

// Mock数据 - 项目计划树结构
export function getProjectPlanTreeMock(projectId) {
  return new Promise(resolve => {
    setTimeout(() => {
      const today = new Date().toISOString().split('T')[0]; // 今日日期
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 昨日
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 明日

      resolve({
        code: 200,
        msg: '查询成功',
        data: [
          {
            id: 1,
            planName: '项目启动阶段',
            ownerName: '张三',
            planStartTime: `${yesterday} 09:00:00`,
            planEndTime: `${tomorrow} 18:00:00`,
            planStatus: '进行中',
            children: [
              {
                id: 11,
                planName: '今日需求调研',
                ownerName: '李四',
                planStartTime: `${today} 09:00:00`,
                planEndTime: `${today} 12:00:00`,
                planStatus: '进行中',
                children: []
              },
              {
                id: 12,
                planName: '今日方案设计',
                ownerName: '王五',
                planStartTime: `${today} 14:00:00`,
                planEndTime: `${today} 18:00:00`,
                planStatus: '进行中',
                children: []
              },
              {
                id: 13,
                planName: '历史任务（不应显示）',
                ownerName: '赵六',
                planStartTime: '2024-01-01 09:00:00',
                planEndTime: '2024-01-05 18:00:00',
                planStatus: '已完成',
                children: []
              }
            ]
          },
          {
            id: 2,
            planName: '跨日开发任务',
            ownerName: '钱七',
            planStartTime: `${yesterday} 09:00:00`,
            planEndTime: `${tomorrow} 18:00:00`,
            planStatus: '进行中',
            children: [
              {
                id: 21,
                planName: '今日前端开发',
                ownerName: '孙八',
                planStartTime: `${today} 09:00:00`,
                planEndTime: `${today} 17:00:00`,
                planStatus: '进行中',
                children: []
              },
              {
                id: 22,
                planName: '未来任务（不应显示）',
                ownerName: '周九',
                planStartTime: `${tomorrow} 09:00:00`,
                planEndTime: `${tomorrow} 18:00:00`,
                planStatus: '未开始',
                children: []
              }
            ]
          }
        ],
        success: true
      });
    }, 500);
  });
}

// 获取计划状态枚举
export function getPlanStatusEnum() {
  return request({
    url: '/enums/plan-status',
    method: 'get'
  })
}

// 更新计划信息
export function updatePlan(data) {
  return request({
    url: '/project-plan/update',
    method: 'put',
    data
  })
}

// Mock数据 - 更新计划信息
export function updatePlanMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {

      resolve({
        code: 200,
        msg: '更新成功',
        data: true,
        success: true
      });
    }, 500);
  });
}

// Mock数据 - 计划详情
export function getPlanDetailMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      const { projectId, planId } = data;

      // 如果planId为空或无效，返回错误
      if (!planId || planId === 'undefined' || planId === 'null') {
        resolve({
          code: 20001,
          msg: '计划ID无效',
          success: false
        });
        return;
      }

      resolve({
        code: 0,
        msg: '操作成功',
        data: {
          planId: planId, // 保持原始格式
          planName: `计划详情 - ID: ${planId}`,
          prePlan: '前置计划名称',
          ownerName: '张三',
          reviewStatus: '已审核',
          planStatus: '进行中',
          planStartTime: '2024-07-01 09:00:00',
          planEndTime: '2024-07-31 18:00:00',
          planDuration: 30,
          actualStartTime: '2024-07-02 09:30:00',
          actualEndTime: '',
          actualDuration: 28,
          seriNum: '1.2.1',
          preSeriNum: '1.2',
          // 添加更多详细信息
          projectId: projectId,
          changeRemark: '这是Mock数据，用于开发测试',
          reviewOpinion: '审核通过，可以继续执行'
        },
        success: true
      });
    }, 500);
  });
}

// Mock数据 - 计划状态枚举
export function getPlanStatusEnumMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          '0': '未开始',
          '1': '进行中',
          '2': '已完成',
          '3': '逾期',
          '4': '逾期完成'
        },
        success: true
      });
    }, 100);
  });
}