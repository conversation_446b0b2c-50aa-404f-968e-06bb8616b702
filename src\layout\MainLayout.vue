<template>
  <div class="main-layout">
    <AppSidebar ref="sidebarRef" />
    <div class="right-container">
      <AppHeader />
      <div class="main-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'

const sidebarRef = ref(null)

// 提供刷新侧边栏项目列表的方法
const refreshSidebarProjects = () => {
  if (sidebarRef.value) {
    sidebarRef.value.refreshProjects()
  }
}

// 通过provide向子组件提供刷新方法
provide('refreshSidebarProjects', refreshSidebarProjects)
</script>

<style scoped>
.main-layout {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #e1e8fd 0%, #f0f4ff 100%); /* 添加渐变背景 */
  display: flex;
  padding: 0;
  box-sizing: border-box;
  gap: 16px; /* 恢复间隙，解决边缘冲突 */
}

.right-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  padding-right: 10px;
}

.main-content {
  flex-grow: 1;
  margin: 0 0 10px 0;
  overflow-y: auto;
  height: calc(100vh - 90px);
  padding: 0;
}
</style> 