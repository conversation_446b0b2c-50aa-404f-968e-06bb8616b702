import request from '@/utils/request'

// 查询项目列表
export function getProjectList(params) {
  return request({
    url: '/project/list',
    method: 'get',
    params
  })
}

// 通过ID获取项目详情
export function getProjectById(projectId) {
  return request({
    url: `/project/detail/${projectId}`,
    method: 'get'
  })
}

// 编辑项目
export function editProject(data) {
  return request({
    url: '/project/edit',
    method: 'post',
    data
  })
}

// 关注项目
export function followProject(projectId) {
  return request({
    url: `/project/follow/${projectId}`,
    method: 'post'
  })
}

// 取消关注项目
export function unfollowProject(projectId) {
  return request({
    url: `/project/follow/${projectId}`,
    method: 'delete'
  })
}

// 查询项目经理和成员的项目列表（不分页）
export function getAllUserProjects(params) {
  return request({
    url: '/project/list-all',
    method: 'get',
    params
  })
}

// TODO: 替换为真实API - Mock数据用于开发测试
export function getAllUserProjectsMock(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockProjects = [
        {
          id: 'proj001',
          projectName: '电商平台开发项目',
          projectShortName: '电商平台',
          projectManager: '张三',
          status: '进行中'
        },
        {
          id: 'proj002', 
          projectName: '移动应用开发',
          projectShortName: '移动应用',
          projectManager: '李四',
          status: '进行中'
        },
        {
          id: 'proj003',
          projectName: '数据分析系统',
          projectShortName: '数据分析',
          projectManager: '王五',
          status: '已完成'
        }
      ]
      
      resolve({
        code: 200,
        data: mockProjects,
        message: 'success'
      })
    }, 500)
  })
}

// 查询项目日报列表
export function getProjectDailyList(params) {
  return request({
    url: '/project/daily/list',
    method: 'get',
    params
  })
}

// 查询项目日报详情
export function getProjectDailyDetail(id) {
  return request({
    url: `/project/daily/detail/${id}`,
    method: 'get'
  })
}

// 保存项目日报（新增或编辑）
export function saveProjectDaily(data) {
  return request({
    url: '/project/daily/save',
    method: 'post',
    data
  })
} 

// 获取项目总览
export function getProjectOverview() {
  return request({
    url: '/project/overview',
    method: 'get'
  })
} 

// 获取我负责的项目概览
export function getMyProjects(params) {
  return request({
    url: '/project/my-overview',
    method: 'get',
    params
  })
}

// 获取项目版本号
export function getProjectVersion(projectId) {
  return request({
    url: `/project/version/${projectId}`,
    method: 'get'
  })
}

// AI生成项目计划
export function generateProjectPlan(projectId, file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: `/project/ai/generate?projectId=${projectId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时，AI生成可能需要较长时间
  })
}

// TODO: 替换为真实API - Mock数据用于开发测试
export function generateProjectPlanMock(projectId, file) {
  return new Promise((resolve, reject) => {
    // 模拟文件验证
    if (!file) {
      reject(new Error('文件不能为空'))
      return
    }

    // 模拟文件大小检查
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      reject(new Error('文件大小不能超过50MB'))
      return
    }

    // 模拟文件格式检查
    const allowedTypes = ['.doc', '.docx', '.pdf']
    const fileName = file.name.toLowerCase()
    const isValidType = allowedTypes.some(type => fileName.endsWith(type))

    if (!isValidType) {
      reject(new Error('只支持.doc、.docx、.pdf格式的文件'))
      return
    }

    // 模拟AI生成过程（3-5秒随机延迟）
    const delay = Math.random() * 2000 + 3000 // 3-5秒

    setTimeout(() => {
      // 90%成功率，10%失败率用于测试错误处理
      if (Math.random() > 0.1) {
        resolve({
          code: 200,
          message: 'AI生成成功',
          data: {
            projectId: projectId,
            fileName: file.name,
            generatedVersion: 'v0.1',
            taskCount: Math.floor(Math.random() * 20) + 10 // 10-30个任务
          }
        })
      } else {
        reject(new Error('AI生成服务暂时不可用，请稍后重试'))
      }
    }, delay)
  })
}

// TODO: 替换为真实API - Mock数据用于开发测试
export function getMyProjectsMock(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const { pageNum = 1, pageSize = 10, projectName = '', projectState = '' } = params;
      
      const mockProjects = [
        {
          id: '5F60313F5CF725146898A972',
          projectName: '2025CJ02292025年港珠澳大桥口岸"一站式"制卡定制服务',
          projectState: '在建',
          startDate: '2025-04-11',
          endDate: '2025-05-11',
          progress: 60,
          taskCount: 20,
          completedTaskCount: 12,
          aiGenerationStatus: '0', // 未开始
          reviewStatus: '0' // 草稿状态 - 可以点击
        },
        {
          id: '5F60313F5CF725146898A973',
          projectName: '2025CJ0138九洲边检站工作人员通道配件采购',
          projectState: '在建',
          startDate: '2025-04-11',
          endDate: '2025-05-11',
          progress: 60,
          taskCount: 18,
          completedTaskCount: 11,
          aiGenerationStatus: '1', // 生成中
          reviewStatus: '0' // 草稿状态 - 可以点击查看进度
        },
        {
          id: '5F60313F5CF725146898A974',
          projectName: '2024CJ0077拱北口岸旧联检楼边检快捷通道和相关配套设施设备安装应急工程',
          projectState: '在建',
          startDate: '2025-04-11',
          endDate: '2025-05-11',
          progress: 60,
          taskCount: 25,
          completedTaskCount: 15,
          aiGenerationStatus: '2', // 已完成
          reviewStatus: '1' // 待审核状态 - 不可点击
        },
        {
          id: '5F60313F5CF725146898A975',
          projectName: '2024CJ0314湾仔出入境边防检查站2024年查验设备维修保养服务采购项目',
          projectState: '在建',
          startDate: '2025-04-11',
          endDate: '2025-05-11',
          progress: 60,
          taskCount: 22,
          completedTaskCount: 13,
          aiGenerationStatus: '3', // 失败
          reviewStatus: '2' // 生效中状态 - 不可点击
        },
        {
          id: '5F60313F5CF725146898A976',
          projectName: '横琴新区澳门机动车入出横琴综合管理系统-一站式升级改造工程项目',
          projectState: '在建',
          startDate: '2025-04-11',
          endDate: '2025-05-11',
          progress: 60,
          taskCount: 30,
          completedTaskCount: 18,
          aiGenerationStatus: '0', // 未开始
          reviewStatus: '3' // 已驳回状态 - 不可点击
        }
      ];

      // 模拟搜索和分页
      let filteredData = mockProjects;
      if (projectName) {
        filteredData = mockProjects.filter(item => 
          item.projectName.includes(projectName)
        );
      }
      if (projectState) {
        filteredData = filteredData.filter(item => 
          item.projectState === projectState
        );
      }

      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const rows = filteredData.slice(start, end);

      resolve({
        code: 200,
        data: rows,
        total: filteredData.length,
        pageNum: pageNum,
        pageSize: pageSize,
        message: 'success'
      });
    }, 300);
  });
}

// ================= 工作台相关接口 =================

// 获取我关注项目的汇总信息
export function getFollowedProjectsSummary() {
  return request({
    url: '/workbench/followed/summary',
    method: 'get'
  })
}

// 获取我关注项目的项目信息列表
export function getFollowedProjectsList(params) {
  return request({
    url: '/workbench/followed/list',
    method: 'get',
    params
  })
}

// 获取工作台关注项目汇总详情列表
export function getWorkbenchFollowedSummaryList() {
  return request({
    url: '/workbench/followed/summary/list',
    method: 'get'
  })
}

// 获取工作台关注项目的具体任务详情
export function getWorkbenchFollowedProjectTasks(projectId, type) {
  return request({
    url: `/workbench/followed/project/${projectId}/${type}`,
    method: 'get'
  })
}

// 获取工作台计划筛选类型枚举
export function getWorkbenchPlanFilterTypes() {
  return request({
    url: '/enums/workbench-plan-filter-types',
    method: 'get'
  })
}

// 查询指定项目的逾期计划列表
export function getProjectOverduePlans(projectId, params) {
  return request({
    url: `/workbench/project/${projectId}/overdue-plans`,
    method: 'get',
    params
  })
}