@import "../variables.scss";
@import "../mixins.scss";

// Element Plus 表格全局样式覆盖
.el-table {
  --el-table-border-color: #{$table-border-color};
  --el-table-header-bg-color: #{$table-header-bg};
  --el-table-row-hover-bg-color: #{$table-row-hover-bg};
  border-radius: 0;
  width: 100% !important;
  table-layout: fixed;
  font-size: $font-size-base;
  color: #333;

  .el-table__header-wrapper th {
    background-color: $table-header-bg;
    color: #333;
    font-weight: 500;
    height: 30px;
    padding: 0;
    border-bottom: 1px solid $border-color-light;
  }

  .el-table__header-wrapper th.el-table__cell {
    background-color: $table-header-bg;
  }
  
  .el-table__header-wrapper th.el-table__cell .cell {
    font-weight: 500;
    color: #333;
    padding: 0 4px;
  }

  .el-table__row {
    height: 30px !important;
  }

  .el-table__cell {
    padding: 0 !important;
    height: 30px !important;
    line-height: 30px !important;
  }

  .cell {
    padding: 0 4px !important;
    line-height: 30px !important;
  }

  // 移除斑马纹样式，改用层级背景色
  
  // 表格边框样式优化
  &.el-table--border .el-table__inner-wrapper::after {
    background-color: $table-border-color;
  }
  
  &.el-table--border .el-table__cell {
    border-right: 1px solid $table-border-color;
  }

  // 确保表格内部的小按钮样式正确
  .el-button--small {
    padding: 5px 11px;
    font-size: 12px;
    border-radius: 3px;
    height: 24px;
  }

  // 解决表格固定列阴影问题
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: $table-border-color;
    opacity: 0.5;
  }

  // 左侧固定列样式 - 强制移除所有背景色
  .el-table__fixed-left {
    height: 100% !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
    background: transparent !important;
  }

  // 强制移除左侧固定列所有子元素的背景色（但保留斑马纹）
  .el-table__fixed-left .el-table__fixed-header-wrapper,
  .el-table__fixed-left .el-table__fixed-body-wrapper {
    background: transparent !important;
    background-color: transparent !important;
  }

  // 保留表头背景色
  .el-table__fixed-left th {
    background-color: $table-header-bg !important;
  }

  // 右侧固定列样式 - 强制移除所有背景色
  .el-table__fixed-right {
    height: 100% !important;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
    background: transparent !important;
  }

  // 强制移除右侧固定列所有子元素的背景色（但保留斑马纹）
  .el-table__fixed-right .el-table__fixed-header-wrapper,
  .el-table__fixed-right .el-table__fixed-body-wrapper {
    background: transparent !important;
    background-color: transparent !important;
  }

  // 保留表头背景色
  .el-table__fixed-right th {
    background-color: $table-header-bg !important;
  }
}

// 表格外层容器样式修复
.el-table__inner-wrapper {
  height: auto !important;
}

.el-table__body-wrapper {
  overflow-y: auto !important;
}

// 表格中的序号列样式 - 移除强制白色背景，让其继承表格样式

.serial-cell {
  display: flex;
  align-items: center;
  height: 32px;
  padding-left: 8px;
  font-weight: 500;
  color: $text-regular;
  /* 移除强制白色背景 */
}

// 工作项名称列样式和可编辑单元格样式 - 移除强制白色背景

// 移除左侧固定列强制白色背景

// 移除左侧固定列cell元素强制白色背景

// 移除左侧固定列输入框等元素强制白色背景

// 移除左侧固定列悬停和斑马纹强制背景色

// 操作列样式
.operation-column {
  display: flex;
  justify-content: center;
  gap: 4px;

  .el-button {
    padding: 2px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    line-height: 1;
  }

  .el-icon {
    margin-right: 2px;
    font-size: 14px;
  }

  .op-btn {
    padding: 0 4px !important;
    margin: 0 !important;
    height: 24px !important;
    line-height: 1 !important;
    
    .el-icon {
      margin-right: 2px !important;
      font-size: 14px !important;
    }

    & + .op-btn {
      margin-left: 0 !important;
    }
  }
}

// 树形表格展开图标样式
.custom-triangle-icon {
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 7px solid #c0c4cc;
  transition: transform 0.2s ease, border-color 0.2s ease;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
  display: inline-block;

  &.expanded {
    transform: rotate(90deg);
    border-left-color: $primary-color;
  }
}

// 表格状态列样式
.status-column {
  .cell {
    padding: 0 2px !important;
  }

  .el-select {
    max-width: 90px;
    width: 90px;
    margin: 0 auto;
  }

  .el-select .el-input__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    padding: 0 8px;
  }

  .el-select .el-input__inner {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }
}

// 表格容器样式
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid $border-color;
  border-radius: 4px;
  background-color: $background-color-white;
  margin: 0 5px 5px 5px;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

// 表格分页样式
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 10px;
  border-top: 1px solid $border-color-lighter;
  background-color: $background-color-white;
  height: 32px;
  min-height: 32px;
  box-sizing: content-box;

  .pagination-total {
    margin-right: 16px;
    font-size: 14px;
    color: $text-regular;
  }

  .pagination-goto {
    display: flex;
    align-items: center;
    margin-left: 16px;
    font-size: 14px;
    color: $text-regular;
  }

  .el-pagination {
    padding: 0;
    height: 32px;
    line-height: 32px;
    
    .el-pagination__total {
      font-weight: normal;
    }
    
    button {
      min-width: 32px;
      height: 32px;
    }
    
    .el-pager li {
      min-width: 32px;
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      font-weight: normal;
      
      &.is-active {
        background-color: $primary-color;
        color: $background-color-white;
        font-weight: bold;
      }
    }
  }
} 