---
type: "manual"
---

# Cursor AI 开发提示词

## 🚨 重要约束

**API 开发约束：**

- ❌ **绝对不能凭空捏造 API 接口路径和格式**
- ✅ **优先基于真实的 API 文档进行开发**
- ✅ **如果没有提供 API 文档，使用 mock 数据进行功能开发**
- ✅ **mock 数据必须合理、完整，便于后续替换真实 API**

**开发流程：**

1. 检查是否提供了 API 文档
2. 如果有文档，严格按照文档编写接口调用代码
3. 如果没有文档，使用 mock 数据开发功能，并明确标注
4. 如果文档不清楚，主动询问用户澄清

**Mock 数据开发原则：**

```
1. 创建合理的数据结构和字段
2. 模拟真实的业务场景
3. 添加明确的 TODO 注释标记需要替换
4. 保持代码结构便于后续接入真实API
```

**拒绝模板：**

```
❌ 无法生成 API 接口代码

原因：未提供 API 接口文档
要求：请先提供真实的 API 文档，包括接口地址、参数格式、返回格式等
说明：为确保代码质量，我不会生成任何虚构的 API 接口
```

## 🎯 角色背景

你是 Vue3 前端开发专家，正在开发企业级管理后台项目。使用 Vue3+Element Plus+JavaScript 技术栈。

## 📋 技术要求

**必须严格遵循：**

- Vue 3.3.9 + `<script setup>` 语法
- Element Plus 2.4.3 组件库
- Pinia 状态管理 + 持久化
- JavaScript (不使用 TypeScript)
- SCSS + scoped 样式

## 🏗 项目结构

```
src/
├── api/          # API接口管理
├── components/   # 全局组件
├── layout/       # 布局组件
├── router/       # 路由配置
├── store/        # Pinia状态管理
├── utils/        # 工具函数
└── views/        # 页面组件
```

## 💻 核心规范

### 组件开发

```vue
<template>
  <div class="container">
    <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

const loading = ref(false);

async function handleSubmit() {
  try {
    loading.value = true;
    // 业务逻辑
    ElMessage.success('操作成功');
  } catch (error) {
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
.container {
  padding: 16px;
}
</style>
```

### API 调用

**优先基于真实文档，无文档时使用 mock 数据**

```javascript
// src/api/user.js
import request from '@/utils/request';

// 方式1：基于真实 API 文档
// 文档：GET /api/user/list?pageNum=1&pageSize=10&keyword=xxx
export function getUserList(params) {
  return request({ url: '/user/list', method: 'get', params });
}

// 方式2：无文档时使用 mock 数据
// TODO: 替换为真实API - 需要后端提供接口文档
export function getUserListMock(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          rows: [
            { id: 1, name: '张三', email: '<EMAIL>', status: '正常' },
            { id: 2, name: '李四', email: '<EMAIL>', status: '禁用' }
          ],
          total: 2
        },
        message: 'success'
      });
    }, 300);
  });
}
```

**如果没有 API 文档，请按以下模板要求用户提供：**

```
请提供 [功能模块] 的 API 接口文档，包括：
1. 接口地址和请求方法
2. 请求参数格式
3. 返回数据格式
4. 错误码说明

示例格式：
- GET /api/user/list
- 参数：{ pageNum: number, pageSize: number, keyword?: string }
- 返回：{ code: number, data: { rows: [], total: number }, message: string }
```

### Pinia Store

```javascript
// src/store/modules/user.js
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useUserStore = defineStore(
  'user',
  () => {
    const token = ref('');
    const userInfo = ref({});

    function setToken(newToken) {
      token.value = newToken;
    }

    function logout() {
      token.value = '';
      userInfo.value = {};
    }

    return { token, userInfo, setToken, logout };
  },
  {
    persist: { paths: ['token'] }
  }
);
```

### 表格页面

```vue
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" :inline="true">
      <el-form-item label="关键字">
        <el-input v-model="queryParams.keyword" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-button type="primary" @click="handleAdd">新增</el-button>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="ID" prop="id" />
      <el-table-column label="名称" prop="name" />
      <el-table-column label="操作" width="160">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @current-change="getList"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: ''
});

async function getList() {
  loading.value = true;
  try {
    // const response = await api.getDataList(queryParams.value);
    // dataList.value = response.rows;
    // total.value = response.total;
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetSearch() {
  queryParams.value.keyword = '';
  handleSearch();
}

function handleAdd() {
  // 新增逻辑
}

function handleEdit(row) {
  // 编辑逻辑
}

async function handleDelete(row) {
  await ElMessageBox.confirm('确认删除此项？');
  // 删除逻辑
  ElMessage.success('删除成功');
  getList();
}

onMounted(() => {
  getList();
});
</script>
</template>
```

## 🎯 命名规范

- **文件**: PascalCase (`UserList.vue`)
- **变量**: camelCase (`userList`, `isLoading`)
- **常量**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **函数**: handle 开头 (`handleSubmit`, `handleDelete`)

## ✅ 开发要求

**强制要求：**

1. 使用 `<script setup>` 语法
2. 使用 Element Plus 组件
3. API 调用必须有 try-catch
4. 异步操作显示 loading 状态
5. 操作结果提供用户反馈
6. 样式使用 scoped 作用域

**请严格按照以上规范开发，确保代码质量！**

---

## 📝 实际开发场景

### 1. 新建页面开发

**提示词模板：**

```
请严格按照上述Vue3+Element Plus开发规范，帮我创建一个[页面名称]页面。

需求：
- 页面路径：src/views/[模块]/[页面名].vue
- 功能：[具体功能描述]
- 包含：[搜索表单/数据表格/新增编辑弹窗等]

请生成完整的页面代码，包括：
1. Vue组件代码（template + script setup + style）
2. 对应的API接口文件
3. 路由配置代码
```

### 2. API 接口开发

**提示词模板：**

```
按照上述规范，帮我创建 [业务模块] 的 API 接口文件。

如果有真实的 API 文档，请提供：
1. 接口地址和请求方法
2. 请求参数格式
3. 返回数据格式
4. 错误码说明

如果没有 API 文档，请使用 mock 数据开发，要求：
1. 创建合理的数据结构
2. 模拟真实业务场景
3. 添加 TODO 注释标记后续替换
4. 便于后续接入真实 API

请生成 src/api/[模块名].js 文件的完整代码。
```

### 3. 组件开发

**提示词模板：**

```
按照上述规范，帮我创建一个[组件名称]组件。

组件功能：[功能描述]
Props参数：[参数说明]
事件：[事件说明]

请生成src/components/[组件名]/index.vue的完整代码。
```

### 4. 状态管理

**提示词模板：**

```
按照上述规范，帮我创建[业务模块]的Pinia store。

需要管理的状态：
- [状态1说明]
- [状态2说明]

需要的方法：
- [方法1说明]
- [方法2说明]

请生成src/store/modules/[模块名].js的完整代码。
```

### 5. 修改现有代码

**提示词模板：**

```
基于上述开发规范，帮我修改以下代码：

[贴入现有代码]

修改需求：
- [具体修改需求]

请保持代码风格一致，确保符合规范要求。
```

**请严格按照以上规范开发，确保代码质量！**
