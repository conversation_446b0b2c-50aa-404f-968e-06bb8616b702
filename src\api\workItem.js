import request from '@/utils/request'

// 查询指定项目下的计划树结构（正式表）
export function getWorkItemTree(projectId) {
  return request({
    url: `/project-plan/planTree/${projectId}`,
    method: 'get'
  })
}

// 查询指定项目下的计划树结构（草稿表）
export function getWorkItemDraftTree(projectId) {
  return request({
    url: `/project-plan/draft/planTree/${projectId}`,
    method: 'get'
  })
}

// 更新工作项信息
export function updateWorkItem(data) {
  return request({
    url: '/project-work-items/update',
    method: 'put',
    data
  })
}

// 新增工作项草稿
export function createWorkItemDraft(data) {
  return request({
    url: '/project-work-items/draft/create',
    method: 'post',
    data
  })
}

// 编辑工作项草稿
export function updateWorkItemDraft(data) {
  return request({
    url: '/project-work-items/draft/update',
    method: 'put',
    data
  })
}

// 删除工作项草稿（标记删除）
export function deleteWorkItemDraft(data) {
  return request({
    url: '/project-work-items/draft/delete',
    method: 'delete',
    data
  })
}

// 提交项目工作项审核
export function submitWorkItemReview(projectId) {
  return request({
    url: `/project-work-items/draft/submit/${projectId}`,
    method: 'post'
  })
}

// 批量审核项目工作项
export function batchReviewWorkItems(data) {
  return request({
    url: '/project-work-items/draft/batchReview',
    method: 'post',
    data
  })
}

// 获取项目工作项审核列表
export function getWorkItemReviewList(projectId) {
  return request({
    url: `/project-work-items/draft/reviewList/${projectId}`,
    method: 'get'
  })
}

// 查询工作项详情
export function getWorkItemDetail(data) {
  return request({
    url: '/work-items/detail',
    method: 'get',
    params: data
  })
}

// 查询工作项动态信息
export function getWorkItemDynamics(data) {
  return request({
    url: '/work-items/dynamics',
    method: 'post',
    data
  })
} 