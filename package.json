{"name": "pmsystem", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "element-plus": "^2.10.4", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "vue": "^3.3.9", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "sass": "^1.69.5", "vite": "^5.0.10"}}