# 外部Token登录功能使用说明

## 功能概述

本系统支持从其他项目携带Token跳转完成登录，实现单点登录(SSO)的效果。用户在其他系统中已经登录后，可以直接跳转到本系统而无需重新输入用户名密码。

## 使用方式

### 1. 基本跳转格式

```javascript
// 其他项目中的跳转代码示例
if (token) {
  const childSystemURL = "http://your-project-domain.com"; // 替换为实际的项目URL
  window.location.href = `${childSystemURL}?token=${encodeURIComponent(token)}`;
}
```

### 2. 带目标页面的跳转

```javascript
// 跳转到指定页面
const childSystemURL = "http://your-project-domain.com/projects";
const targetURL = `${childSystemURL}?token=${encodeURIComponent(token)}`;
window.location.href = targetURL;
```

### 3. 在新窗口中打开

```javascript
// 在新窗口中打开
const childSystemURL = "http://your-project-domain.com";
const targetURL = `${childSystemURL}?token=${encodeURIComponent(token)}`;
window.open(targetURL, '_blank');
```

## 技术实现

### 路由配置

系统新增了 `/token-login` 路由来处理外部Token登录：

```javascript
{
  path: '/token-login',
  name: 'TokenLogin',
  component: () => import('../views/Login/TokenLogin.vue'),
  meta: { title: '外部登录', noAuth: true }
}
```

### 自动重定向逻辑

路由守卫会自动检测URL中的token参数：

1. 如果访问任何页面时URL中包含`token`参数
2. 系统会自动重定向到`/token-login`页面
3. 同时保留原始目标页面路径用于登录成功后跳转

### Token验证流程

1. **获取Token**: 从URL参数中提取token
2. **解码Token**: 使用`decodeURIComponent`解码token
3. **验证Token**: 调用后端API验证token有效性
4. **获取用户信息**: 使用token获取当前用户信息
5. **设置登录状态**: 将token和用户信息保存到store
6. **自动跳转**: 登录成功后自动跳转到目标页面

## 用户体验

### 登录过程展示

1. **加载状态**: 显示"正在验证Token..."和加载动画
2. **成功状态**: 显示成功图标和倒计时跳转提示
3. **失败状态**: 显示错误信息和返回登录页按钮

### 错误处理

系统会根据不同的错误类型显示相应的提示信息：

- **Token过期**: "Token已过期或无效，请重新登录"
- **权限不足**: "您没有访问权限，请联系管理员"
- **网络错误**: "网络连接失败，请检查网络后重试"
- **其他错误**: 显示具体的错误信息

## 安全考虑

### Token处理

1. **URL编码**: Token在传输时使用`encodeURIComponent`编码
2. **自动清理**: 验证失败时自动清除无效token
3. **过期处理**: 支持token过期检测和提示

### 权限验证

1. **双重验证**: 先验证token，再获取用户信息确认
2. **权限检查**: 登录成功后仍需通过系统权限验证
3. **安全跳转**: 只允许跳转到系统内部页面

## API接口

### 新增接口

```javascript
// 外部token验证接口（可选）
POST /jwt/validate-external
{
  "token": "your-jwt-token"
}
```

### 现有接口

```javascript
// 获取当前用户信息（用于验证token）
GET /users/me
Headers: {
  "Authorization": "Bearer your-jwt-token"
}
```

## 配置说明

### 环境配置

在其他项目中需要配置本系统的URL：

```javascript
// 开发环境
const childSystemURL = "http://localhost:3011";

// 生产环境
const childSystemURL = "https://your-production-domain.com";
```

### 跳转目标

支持跳转到系统内的任何页面：

- `/workbench` - 工作台（默认）
- `/projects` - 项目管理
- `/users` - 用户管理
- `/project-detail/123` - 具体项目详情

## 使用示例

### 完整的跳转代码

```javascript
// 在其他项目中实现跳转
function jumpToProjectSystem(token, targetPage = '') {
  if (!token) {
    console.error('Token不能为空');
    return;
  }
  
  const baseURL = "http://your-project-domain.com";
  const targetURL = targetPage ? `${baseURL}${targetPage}` : baseURL;
  const finalURL = `${targetURL}?token=${encodeURIComponent(token)}`;
  
  // 在当前窗口跳转
  window.location.href = finalURL;
  
  // 或在新窗口打开
  // window.open(finalURL, '_blank');
}

// 使用示例
jumpToProjectSystem(userToken, '/projects'); // 跳转到项目管理页面
jumpToProjectSystem(userToken); // 跳转到工作台
```

## 注意事项

1. **Token格式**: 确保token是有效的JWT格式
2. **URL编码**: 必须使用`encodeURIComponent`对token进行编码
3. **网络环境**: 确保两个系统之间网络连通
4. **权限同步**: 确保用户在两个系统中都有相应权限
5. **Token时效**: 注意token的有效期，过期需要重新获取

## 故障排除

### 常见问题

1. **跳转后显示登录页**: 检查token是否有效，是否正确编码
2. **权限错误**: 确认用户在目标系统中有相应权限
3. **网络超时**: 检查网络连接和API接口可用性
4. **页面空白**: 检查浏览器控制台错误信息

### 调试方法

1. 打开浏览器开发者工具
2. 查看Network标签页的API请求
3. 查看Console标签页的错误信息
4. 检查Application标签页的localStorage存储
