import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePermissionStore } from './permission'
import { getAllRoles } from '@/api/user'

// 角色常量定义
export const ROLE_IDS = {
  PROJECT_LEADER: 1,    // 项目领导
  PLANNER: 2,          // 计划员
  PROJECT_MANAGER: 3,   // 项目经理
  PROJECT_MEMBER: 4     // 项目成员
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const token = ref('')
    const userInfo = ref({})
    const allRoles = ref([])

    // 计算属性
    const userRole = computed(() => {
      return userInfo.value.roleId || null
    })

    const userRoles = computed(() => {
      return userInfo.value.roles || []
    })

    // 基于角色ID的权限判断
    const isProjectLeader = computed(() => {
      return userRole.value === ROLE_IDS.PROJECT_LEADER
    })

    const isPlanner = computed(() => {
      return userRole.value === ROLE_IDS.PLANNER
    })

    const isProjectManager = computed(() => {
      return userRole.value === ROLE_IDS.PROJECT_MANAGER
    })

    const isProjectMember = computed(() => {
      return userRole.value === ROLE_IDS.PROJECT_MEMBER
    })

    // 判断用户是否为领导角色（兼容旧的命名）
    const isLeaderRole = computed(() => {
      return isProjectLeader.value
    })

    // 获取项目执行情况标题
    const projectExecutionTitle = computed(() => {
      return isLeaderRole.value ? '关注项目执行情况' : '项目执行情况'
    })

    // 方法
    const setToken = (newToken) => {
      token.value = newToken
    }

    const setUserInfo = (info) => {
      userInfo.value = info

      // 当用户信息更新时，直接设置权限数据
      if (info.permissions && Array.isArray(info.permissions)) {
        const permissionStore = usePermissionStore()
        // 直接设置用户的菜单权限ID，不需要通过roleId查询
        permissionStore.setUserMenuPermissions(info.permissions)
        // 仍然需要获取菜单树
        permissionStore.fetchMenuTree()
      }
    }

    // 获取所有角色信息
    const fetchAllRoles = async () => {
      try {
        const res = await getAllRoles()
        if (res && (res.code === 200 || res.code === 0) && res.data) {
          allRoles.value = res.data
        }
      } catch (e) {
        console.warn('获取角色信息失败:', e.message)
      }
    }

    const logout = () => {
      token.value = ''
      userInfo.value = {}
      allRoles.value = []

      // 清除权限数据
      const permissionStore = usePermissionStore()
      permissionStore.resetPermissions()
    }

    const isLoggedIn = () => {
      return !!token.value
    }

    return {
      token,
      userInfo,
      allRoles,
      userRole,
      userRoles,
      isProjectLeader,
      isPlanner,
      isProjectManager,
      isProjectMember,
      isLeaderRole,
      projectExecutionTitle,
      setToken,
      setUserInfo,
      fetchAllRoles,
      logout,
      isLoggedIn
    }
  },
  {
    persist: {
      paths: ['token', 'userInfo', 'allRoles']
    }
  }
)