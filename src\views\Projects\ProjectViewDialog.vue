<template>
  <el-drawer
    v-model="visible"
    direction="rtl"
    size="800px"
    destroy-on-close
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :show-close="false"
    class="project-detail-drawer"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="visible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          项目详情 - {{ detail.projectName || '项目信息' }}
        </div>
      </div>
    </template>

    <div class="project-detail-content drawer-optimized">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><InfoFilled /></el-icon>
            <span class="section-title">基本信息</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <div class="field-item">
                <label class="field-label">项目类型</label>
                <div class="field-value">{{ getProjectTypeDisplay ? getProjectTypeDisplay(detail.projectType) : (detail.projectType || '-') }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">项目编号</label>
                <div class="field-value">{{ detail.contractNo || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">项目名称</label>
                <div class="field-value">{{ detail.projectName || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">甲方单位</label>
                <div class="field-value">{{ detail.firstPartyName || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">项目状态</label>
                <div class="field-value">
                  <span class="status-tag" :class="getStatusClass(detail.projectState)">
                    {{ getProjectStateDisplay ? getProjectStateDisplay(detail.projectState) : (detail.projectState || '-') }}
                  </span>
                </div>
              </div>
              <div class="field-item">
                <label class="field-label">省份</label>
                <div class="field-value">{{ detail.provinceName || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 合同信息区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><Document /></el-icon>
            <span class="section-title">合同信息</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <div class="field-item">
                <label class="field-label">合同签订时间</label>
                <div class="field-value">{{ formatDate(detail.signDate) || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">合同完成时间</label>
                <div class="field-value">{{ formatDate(detail.acceptDate) || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">工期</label>
                <div class="field-value">{{ detail.projectDuration || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">合同金额</label>
                <div class="field-value amount">{{ formatAmount(detail.contractAmount) }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">收款总额</label>
                <div class="field-value amount">{{ formatAmount(detail.payCountMoney) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度信息区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><TrendCharts /></el-icon>
            <span class="section-title">进度信息</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <div class="field-item">
                <label class="field-label">汇款进度</label>
                <div class="field-value">{{ formatPercentage(detail.paymentCollection) }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">施工进度</label>
                <div class="field-value">{{ formatPercentage(detail.constructionProgress) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 人员信息区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><User /></el-icon>
            <span class="section-title">人员信息</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <div class="field-item">
                <label class="field-label">资料员</label>
                <div class="field-value">
                  <span v-if="stakeholdersLoading">加载中...</span>
                  <span v-else>{{ personnelInfo.dataClerk || '-' }}</span>
                </div>
              </div>
              <div class="field-item">
                <label class="field-label">计划员</label>
                <div class="field-value">
                  <span v-if="stakeholdersLoading">加载中...</span>
                  <span v-else>{{ personnelInfo.planner || '-' }}</span>
                </div>
              </div>
              <div class="field-item">
                <label class="field-label">区域项目总监</label>
                <div class="field-value">
                  <span v-if="stakeholdersLoading">加载中...</span>
                  <span v-else>{{ personnelInfo.regionDirector || '-' }}</span>
                </div>
              </div>
              <div class="field-item">
                <label class="field-label">项目经理</label>
                <div class="field-value">{{ detail.projectManagerName || '-' }}</div>
              </div>
              <div class="field-item">
                <label class="field-label">销售负责人</label>
                <div class="field-value">
                  <span v-if="stakeholdersLoading">加载中...</span>
                  <span v-else>{{ personnelInfo.salesUserName || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import {
  Close,
  InfoFilled,
  Document,
  TrendCharts,
  User
} from '@element-plus/icons-vue'
import { getStakeholderListAll } from '@/api/stakeholder'

const props = defineProps({
  modelValue: Boolean,
  detail: { type: Object, default: () => ({}) },
  getProjectTypeDisplay: Function,
  getProjectStateDisplay: Function
})

const emit = defineEmits(['update:modelValue'])
const visible = ref(props.modelValue)
const stakeholdersData = ref([])
const stakeholdersLoading = ref(false)

watch(() => props.modelValue, async (v) => {
  visible.value = v
  if (v && props.detail.contractNo) {
    // 弹窗打开时获取干系人数据
    await getStakeholdersData()
  }
})
watch(visible, v => emit('update:modelValue', v))

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  // 处理不同的日期格式
  if (dateString.includes('T')) {
    // ISO格式：2020-01-01T00:00:00
    return dateString.split('T')[0];
  } else if (dateString.includes(' ')) {
    // 带时间格式：2020-01-01 00:00:00
    return dateString.split(' ')[0];
  }
  // 已经是日期格式：2020-01-01
  return dateString;
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '-';
  return new Intl.NumberFormat('zh-CN').format(amount);
};

// 格式化百分比
const formatPercentage = (value) => {
  if (!value && value !== 0) return '-';
  // 如果值已经是百分比格式（包含%），直接返回
  if (typeof value === 'string' && value.includes('%')) {
    return value;
  }
  // 如果是数字，转换为百分比格式
  if (typeof value === 'number') {
    return `${value}%`;
  }
  // 如果是字符串数字，转换为百分比格式
  const numValue = parseFloat(value);
  if (!isNaN(numValue)) {
    return `${numValue}%`;
  }
  return value || '-';
};

// 获取干系人数据
const getStakeholdersData = async () => {
  if (!props.detail.contractNo) return;

  stakeholdersLoading.value = true;
  try {
    const response = await getStakeholderListAll({ contractNo: props.detail.contractNo });

    if (response && response.data) {
      stakeholdersData.value = response.data;
    } else {
      stakeholdersData.value = [];
    }
  } catch (error) {
    console.error('获取干系人数据失败:', error);
    stakeholdersData.value = [];
  } finally {
    stakeholdersLoading.value = false;
  }
};

// 根据角色获取干系人姓名
const getStakeholderByRole = (role) => {
  const stakeholder = stakeholdersData.value.find(item =>
    item.projectRole === role || item.projectRole?.includes(role)
  );
  return stakeholder ? stakeholder.stakeholderUserName : '-';
};

// 计算属性：获取各类人员
const personnelInfo = computed(() => ({
  dataClerk: getStakeholderByRole('资料员'),
  planner: getStakeholderByRole('计划员'),
  regionDirector: getStakeholderByRole('区域项目总监'),
  salesUserName: getStakeholderByRole('销售负责人')
}));

// 获取状态样式类
const getStatusClass = (status) => {
  // 这里可以根据实际的状态码值来设置不同的样式
  const statusMap = {
    '1': 'status-implementing', // 正在实施
    '2': 'status-paused',       // 暂停
    '3': 'status-pending'       // 待验收
  };
  return statusMap[status] || 'status-default';
};
</script> 

<style scoped>
/* 项目详情抽屉样式 */
.project-detail-drawer :deep(.el-drawer) {
  border-radius: 0;
}

.project-detail-drawer :deep(.el-drawer__header) {
  margin: 0;
  padding: 0;
  border-bottom: none;
}

.project-detail-drawer :deep(.el-drawer__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 自定义抽屉头部样式 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #e8eaed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.drawer-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: transparent;
  margin-right: 12px;
  flex-shrink: 0;
  border: 1px solid transparent;
}

.drawer-close-btn:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border-color: #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.2px;
}

/* 抽屉内容样式 */
.project-detail-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
}

.drawer-optimized {
  display: flex;
  flex-direction: column;
}

.form-section {
  background: #ffffff;
  border-bottom: 1px solid #f0f2f5;
  position: relative;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e8eaed 50%, transparent 100%);
  opacity: 0.6;
}

.section-layout {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px 10px 24px;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  border-bottom: none;
  margin-bottom: 6px;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #409eff 20%, #409eff 80%, transparent 100%);
  opacity: 0.3;
}

.section-icon {
  color: #409EFF;
  font-size: 16px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  line-height: 1;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-content {
  padding: 6px 24px 16px 24px;
}

/* 表单字段样式 */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 28px;
  margin-bottom: 8px;
  padding: 4px 0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.field-item:hover {
  background-color: rgba(64, 158, 255, 0.02);
  padding-left: 8px;
  padding-right: 8px;
  margin-left: -8px;
  margin-right: -8px;
}

.field-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  line-height: 1.3;
  min-width: 100px;
  text-align: left;
  flex-shrink: 0;
}

.field-value {
  flex: 1;
  font-size: 14px;
  color: #212529;
  line-height: 1.3;
  font-weight: 400;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #f39c12;
  text-shadow: 0 1px 2px rgba(243, 156, 18, 0.2);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.status-tag {
  display: inline-block;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  letter-spacing: 0.3px;
}

.status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.status-implementing {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border: 1px solid #90caf9;
}

.status-paused {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #e65100;
  border: 1px solid #ffcc02;
}

.status-pending {
  background: linear-gradient(135deg, #f1f8e9 0%, #c8e6c9 100%);
  color: #2e7d32;
  border: 1px solid #81c784;
}

.status-default {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 1px solid #ced4da;
}


</style> 