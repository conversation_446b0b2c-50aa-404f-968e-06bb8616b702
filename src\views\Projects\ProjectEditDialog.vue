<template>
  <!-- 编辑项目抽屉 -->
  <el-drawer
    v-model="visible"
    size="520px"
    direction="rtl"
    destroy-on-close
    class="project-edit-drawer"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="visible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Edit /></el-icon>
          编辑项目
        </div>
      </div>
    </template>

    <div class="drawer-content">
      <div class="form-container">
        <el-form :model="form" label-width="120px" label-position="right" class="project-form">
          <div class="form-fields">
            <div class="field-item">
              <label class="field-label">项目名称</label>
              <div class="field-value">
                <el-tooltip :content="form.projectName" placement="top" :disabled="!form.projectName || form.projectName.length <= 30">
                  <el-input
                    v-model="form.projectName"
                    readonly
                    class="readonly-input"
                    :title="form.projectName"
                    show-overflow-tooltip
                  />
                </el-tooltip>
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">项目简称</label>
              <div class="field-value">
                <el-input v-model="form.projectShortName" placeholder="请输入项目简称" maxlength="50" show-word-limit />
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">项目状态</label>
              <div class="field-value">
                <el-select v-model="form.projectState" placeholder="请选择" clearable>
                  <el-option
                    v-for="(label, value) in projectStateDictionary"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">合同签订时间</label>
              <div class="field-value">
                <div class="readonly-display">{{ formatDate(form.signDate) || '暂无' }}</div>
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">合同完成时间</label>
              <div class="field-value">
                <div class="readonly-display">{{ formatDate(form.acceptDate) || '暂无' }}</div>
              </div>
            </div>
            <div class="field-item">
              <label class="field-label">项目经理</label>
              <div class="field-value">
                <el-select v-model="form.projectManagerId" placeholder="请选择" clearable :loading="projectManagersLoading">
                  <el-option
                    v-for="manager in allProjectManagers"
                    :key="manager.id"
                    :label="manager.name"
                    :value="manager.id"
                  />
                </el-select>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { getSalesUsersByRole } from '@/api/user'
import { ElMessage } from 'element-plus'
import { Close, Edit } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: Boolean,
  detail: { type: Object, default: () => ({}) },
  projectStateDictionary: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'save'])
const visible = ref(props.modelValue)
const form = ref({ ...props.detail })

// 项目经理相关
const projectManagers = ref([])
const projectManagersLoading = ref(false)

// 合并当前项目经理和其他项目经理的列表
const allProjectManagers = computed(() => {
  const managers = []
  
  // 如果有当前项目经理，添加为第一项
  if (form.value.projectManagerId && form.value.projectManagerName) {
    managers.push({
      id: form.value.projectManagerId, // 这里保持原值，因为可能是username或id
      name: form.value.projectManagerName
    })
  }
  
  // 添加其他项目经理，避免重复
  projectManagers.value.forEach(manager => {
    if (manager.id !== form.value.projectManagerId) {
      managers.push({
        id: manager.username, // 使用username作为ID，因为API期望的是username
        name: manager.realName
      })
    }
  })
  
  return managers
})

watch(() => props.modelValue, v => {
  visible.value = v
  // 当弹窗打开时获取项目经理列表
  if (v) {
    // 延迟一下再获取，避免弹窗打开时立即请求
    setTimeout(() => {
      if (visible.value) { // 确保弹窗还是打开状态
        getProjectManagers()
      }
    }, 500)
  }
})
watch(() => props.detail, v => {
  form.value = { ...v }
})
watch(visible, v => emit('update:modelValue', v))

// 获取项目经理用户列表
const getProjectManagers = async (retryCount = 0) => {
  projectManagersLoading.value = true
  let shouldRetry = false
  
  try {
    // 调用销售用户接口获取项目经理列表
    const response = await getSalesUsersByRole()
    
    if (response && response.data) {
      projectManagers.value = response.data
      
      if (response.data.length === 0) {
        ElMessage.warning('未找到销售用户（项目经理）')
      }
    } else {
      projectManagers.value = []
    }
  } catch (error) {
    console.error('获取项目经理列表失败:', error)
    
          // 如果是超时错误且重试次数小于2次，则进行重试
      if (error.code === 'ECONNABORTED' && retryCount < 2) {
        ElMessage.warning(`网络超时，正在重试...（${retryCount + 1}/2）`)
      shouldRetry = true
      setTimeout(() => {
        getProjectManagers(retryCount + 1)
      }, 2000) // 2秒后重试
      return
    }
    
    // 不显示错误消息，因为request.js已经显示了
    projectManagers.value = []
  } finally {
    // 只有在不重试的情况下才停止loading
    if (!shouldRetry) {
      projectManagersLoading.value = false
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  // 处理不同的日期格式
  if (dateString.includes('T')) {
    // ISO格式：2020-01-01T00:00:00
    return dateString.split('T')[0];
  } else if (dateString.includes(' ')) {
    // 带时间格式：2020-01-01 00:00:00
    return dateString.split(' ')[0];
  }
  // 已经是日期格式：2020-01-01
  return dateString;
};

function handleSave() {
  emit('save', { ...form.value })
  visible.value = false
}

// 组件挂载时获取项目经理列表
onMounted(() => {
  getProjectManagers()
})
</script>

<style scoped>
/* 覆盖 Element Plus 抽屉默认样式 */
:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 0 !important;
}

:deep(.el-drawer__footer) {
  padding: 0 !important;
  margin: 0 !important;
}

/* 自定义抽屉头部样式 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #e8eaed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .drawer-close-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #909399;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: transparent;
    margin-right: 12px;
    flex-shrink: 0;
    border: 1px solid transparent;

    &:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      color: #495057;
      border-color: #dee2e6;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      transform: translateY(-1px);
    }

    .el-icon {
      font-size: 16px;
    }
  }

  .drawer-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    flex: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    letter-spacing: 0.2px;

    .drawer-icon {
      margin-right: 8px;
      color: #409eff;
      filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
    }
  }
}

/* 编辑项目抽屉样式 */
.project-edit-drawer {
  .drawer-footer {
    padding: 20px 24px;
    border-top: 1px solid #e8eaed;
    background-color: #ffffff;
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    margin: 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  }

  /* 抽屉内容区域样式 */
  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0 !important;
    border-bottom: none;
  }

  :deep(.el-drawer__footer) {
    padding: 0 !important;
    margin: 0 !important;
  }
}

.drawer-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: transparent;
}

/* 表单容器样式 */
.form-container {
  background: #ffffff;
  padding: 24px;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 表单字段样式 */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 28px;
  margin-bottom: 8px;
  padding: 4px 0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.field-item:hover {
  background-color: rgba(64, 158, 255, 0.02);
  padding-left: 8px;
  padding-right: 8px;
  margin-left: -8px;
  margin-right: -8px;
}

.field-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  line-height: 1.3;
  min-width: 100px;
  text-align: left;
  flex-shrink: 0;
}

.field-value {
  flex: 1;
  font-size: 14px;
  color: #212529;
  line-height: 1.3;
  font-weight: 400;
}

.project-form {
  max-width: 100%;
}

.project-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.project-form :deep(.el-form-item__label) {
  display: none;
}

.project-form :deep(.el-form-item__content) {
  line-height: 1.3;
  margin-left: 0 !important;
}

.project-form :deep(.el-input),
.project-form :deep(.el-select) {
  width: 100%;
}

.project-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.2s ease;
}

.project-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.project-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.project-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.readonly-input :deep(.el-input__wrapper),
.readonly-input :deep(.el-input__inner),
.readonly-input :deep(.el-date-editor.el-input__wrapper) {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  border-color: #e9ecef !important;
  box-shadow: 0 0 0 1px #e9ecef inset !important;
}

.readonly-display {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.3;
  min-height: 32px;
  display: flex;
  align-items: center;
}

.readonly-input {
  opacity: 0.9;
  pointer-events: none;
}

.readonly-input :deep(.el-input__inner) {
  pointer-events: none;
}

.readonly-input :deep(.el-input__icon),
.readonly-input :deep(.el-range__icon) {
  color: #c0c4cc !important;
  pointer-events: none;
}

.readonly-input :deep(.el-range__close-icon) {
  display: none !important;
}

/* 保证整个日期选择器不可点击 */
.readonly-input:deep(.el-date-editor) {
  background-color: #f5f5f5 !important;
}

/* 只读显示样式 - 参考项目名称的处理方式 */
.readonly-display {
  width: 100%;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background-color: #f8f9fa;
  color: #606266;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  font-size: 14px;
  cursor: not-allowed;
  user-select: none;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  transition: all 0.2s;
}

.readonly-display:empty::before {
  content: '暂无';
  color: #c0c4cc;
}

/* 统一按钮样式 */
.drawer-footer .el-button {
  padding: 10px 24px;
  height: 36px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 500;
  min-width: 80px;
}

.drawer-footer .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #FFFFFF;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  transition: all 0.2s;
}

.drawer-footer .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

/* 统一输入框、下拉框样式 */
.project-form :deep(.el-input__wrapper),
.project-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.project-form :deep(.el-input__wrapper:hover),
.project-form :deep(.el-select .el-input__wrapper:hover) {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.project-form :deep(.el-input__wrapper.is-focus),
.project-form :deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 统一选择框样式 */
.project-form :deep(.el-select) {
  width: 100%;
}

/* 输入框内容样式 */
.project-form :deep(.el-input__inner) {
  font-size: 14px;
  line-height: 1.5;
}
</style>