@import "./variables.scss";

// 弹性布局
@mixin flex($direction: row, $justify: flex-start, $align: flex-start, $wrap: nowrap, $gap: 0) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
  @if $gap != 0 {
    gap: $gap;
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略
@mixin multi-line-ellipsis($line: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 卡片样式
@mixin card {
  background-color: $background-color-white;
  border-radius: $border-radius-medium;
  box-shadow: $box-shadow-light;
  padding: $spacing-base;
}

// 内容区域样式
@mixin content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

// 表格容器样式
@mixin table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid $border-color-lighter;
  border-radius: $border-radius-base;
  background-color: $background-color-white;
  margin: 0 5px 5px 5px;
  overflow: hidden;
  min-height: 0;
}

// 表格包装器样式
@mixin table-wrapper {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

// 页头样式
@mixin page-header {
  display: flex;
  align-items: center;
  padding: $spacing-small $spacing-base;
  background-color: $background-color-white;
  border-radius: $border-radius-medium;
  box-shadow: $box-shadow-light;
  margin-bottom: $spacing-base;

  h3 {
    font-size: $font-size-medium;
    font-weight: 500;
    margin: 0;
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: $spacing-mini;
    }
  }
}

// 分页容器样式
@mixin pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: $spacing-small $spacing-small;
  border-top: 1px solid $border-color-lighter;
  background-color: $background-color-white;
  height: 32px;
  min-height: 32px;
  box-sizing: content-box;
}

// 操作按钮样式
@mixin operation-button {
  padding: 0 $spacing-mini !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;

  .el-icon {
    margin-right: 2px !important;
    font-size: 14px !important;
  }

  & + & {
    margin-left: 0 !important;
  }
} 