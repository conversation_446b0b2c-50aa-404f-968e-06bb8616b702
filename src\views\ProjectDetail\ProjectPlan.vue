<template>
  <div class="tab-content">
    <div class="project-plan">
      <div class="plan-header">
        <h3><el-icon><Calendar /></el-icon> 计划版本：{{ projectVersion || 'v1.0' }}</h3>
        <div class="header-buttons">
          <!-- 展开收起按钮 - 所有人都可以看到 -->
          <div class="toolbar-group">
            <button class="toolbar-btn" @click="refreshData" title="刷新数据">
              <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="toolbar-btn" @click="expandAllRows">
              <i class="fas fa-expand-alt"></i> 全部展开
            </button>
            <button class="toolbar-btn" @click="collapseAllRows">
              <i class="fas fa-compress-alt"></i> 全部收起
            </button>
            <button class="toolbar-btn" @click="exportToExcel" title="导出Excel">
              <i class="fas fa-file-excel"></i> 导出Excel
            </button>
          </div>
          <!-- 计划变更按钮 - 只有项目经理和计划员才能看到 -->
          <div class="toolbar-group" v-if="canChangePlan">
            <button class="toolbar-btn submit-btn" @click="showPlanEdit">
              <i class="fas fa-edit"></i> 计划变更
            </button>
          </div>
        </div>
      </div>
      
      <div class="table-wrapper">
        <ProjectTaskTable
          ref="taskTableRef"
          :task-data="planData"
          :edit-enabled="!readOnly"
          :loading="loading"
          :status-options="statusOptions"
          :stakeholders="stakeholders"
          :contract-no="contractNo"
          @view="handleViewPlan"
          @update="uploadAttachment"
          @save="handleSave"
          @pretask-click="handlePretaskClick"
        />
      </div>
      
      <!-- 上传对话框 -->
      <el-dialog
        v-model="uploadDialogVisible"
        title="进展提交"
        width="600px"
        destroy-on-close
        center
        append-to-body
      >
        <div class="upload-container">
          <!-- <h4 class="section-title">
            <el-icon><Plus /></el-icon>
            附件管理
          </h4> -->

          <div class="upload-area">
            <!-- 新上传组件 - 使用原生文件输入 -->
            <div class="new-upload">
              <div v-if="fileList.length === 0" class="upload-box">
                <input
                  ref="fileInputRef"
                  type="file"
                  accept="image/jpeg,image/png"
                  style="display: none"
                  @change="handleNativeFileChange"
                />
                <div class="upload-trigger" @click="triggerFileInput">
                  <el-button class="upload-image-btn" size="small" :disabled="fileList.length >= 1">上传图片</el-button>
                </div>
              </div>

              <!-- 图片预览列表 - 使用与日报相同的方式 -->
              <div v-if="fileList.length > 0" class="image-list">
                <div v-for="(img, index) in fileList" :key="index" class="upload-box image-thumb-box">
                  <img :src="img.url || img.preview || ''" alt="图片预览" class="image-thumb" v-if="img.url || img.preview" @click="handlePreviewImage(img)" />
                  <div class="image-thumb-empty" v-else>无预览</div>
                  <!-- 上传状态遮罩 -->
                  <div v-if="img.uploading" class="upload-status-mask">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <div class="upload-text">上传中...</div>
                  </div>
                  <div v-else-if="img.uploadError" class="upload-status-mask error">
                    <el-icon><Close /></el-icon>
                    <div class="upload-text">上传失败</div>
                  </div>
                  <div v-else-if="img.uploadSuccess" class="upload-success-badge">
                    <el-icon><Check /></el-icon>
                  </div>
                  <!-- 删除图标 - 与日报页面一致 -->
                  <el-icon class="delete-icon thumb-delete" @click="handleRemove(img)"><Close /></el-icon>
                  <div class="image-name">{{ img.name || '未命名' }}</div>
                </div>
              </div>
            </div>

            <!-- 已上传的附件 -->
            <div v-if="existingAttachments.length > 0" class="existing-attachments">
              <div
                v-for="attachment in existingAttachments"
                :key="attachment.id"
                class="attachment-item"
              >
                <div class="attachment-preview">
                  <!-- 尝试显示图片 -->
                  <img
                    v-if="!attachment.imageError && attachment.fileUrl"
                    :src="attachment.fileUrl"
                    :alt="attachment.fileName"
                    class="attachment-thumbnail"
                    @click="handlePreviewExisting(attachment)"
                    @error="(e) => handleImageError(e, attachment)"
                    @load="(e) => handleImageLoad(e, attachment)"
                    crossorigin="anonymous"
                  />
                  <!-- 图片加载失败时的占位符 -->
                  <div v-if="attachment.imageError || !attachment.fileUrl" class="image-placeholder" @click="handlePreviewExisting(attachment)">
                    <el-icon class="placeholder-icon"><Picture /></el-icon>
                    <span class="placeholder-text">{{ attachment.fileName }}</span>
                    <span class="placeholder-subtext">{{ attachment.fileUrl ? '加载失败，点击查看' : '无图片URL' }}</span>
                  </div>
                  <div class="attachment-overlay">
                    <span class="attachment-action" @click="handlePreviewExisting(attachment)">
                      <el-icon><ZoomIn /></el-icon>
                    </span>
                  </div>
                </div>
                <div class="attachment-info">
                  <div class="attachment-name" :title="attachment.fileName">
                    {{ attachment.fileName }}
                  </div>
                  <div v-if="attachment.fileSize && attachment.fileSize > 0" class="attachment-meta">
                    {{ formatFileSize(attachment.fileSize) }}
                  </div>

                </div>
              </div>
            </div>

            <!-- 加载中状态 -->
            <div v-if="attachmentsLoading" class="loading-attachments">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </div>

          <div class="upload-tip">
            支持JPG、PNG格式，单张不超过50MB，最多可上传1张
          </div>
        </div>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeUploadDialog">取 消</el-button>
            <el-button type="primary" @click="submitUpload" :loading="uploadLoading">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 图片预览 -->
      <el-dialog v-model="previewVisible" append-to-body>
        <img w-full :src="previewUrl" alt="Preview Image" style="max-width: 100%;" />
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
.upload-container {
  padding: 0;
}

/* 修复Element Plus对话框的滚动条冲突 */
.el-dialog__body {
  padding: 20px !important;
  max-height: 400px !important;
  overflow-y: auto !important;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.new-upload {
  flex-shrink: 0;
}

/* 添加与日报页面相同的图片预览样式 */
.upload-box {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #f5f7fa;
  position: relative;
}

.upload-box:hover {
  border-color: #409EFF;
}

.upload-trigger {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-left: 0;
}

/* 图片缩略图样式优化 */
.image-thumb-box {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.image-thumb-box:hover {
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: all 0.3s;
}

.image-thumb:hover {
  transform: scale(1.05);
}

.image-thumb-empty {
  color: #bbb;
  font-size: 12px;
}

/* 上传状态样式 */
.upload-status-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 4px;
}

.upload-status-mask.error {
  background-color: rgba(245, 108, 108, 0.8);
}

.upload-status-mask .upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.upload-success-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background-color: #67c23a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.delete-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  background: rgba(255,255,255,0.7);
  border-radius: 50%;
  cursor: pointer;
  color: #909399;
  z-index: 2;
  padding: 2px;
  transition: all 0.3s;
}

.delete-icon:hover {
  background: rgba(255,255,255,0.9);
  color: #F56C6C;
  transform: scale(1.1);
}

.thumb-delete {
  font-size: 16px;
}

.image-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-attachments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;
  gap: 8px;
}



.existing-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px; /* 与左侧上传区域保持一致 */
}

.attachment-preview {
  position: relative;
  width: 100px;  /* 与左侧上传区域保持一致 */
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.attachment-preview:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.attachment-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.image-placeholder:hover {
  background: #e6f7ff;
  color: #409eff;
}

.placeholder-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.placeholder-text {
  font-size: 10px;
  text-align: center;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
}

.placeholder-subtext {
  font-size: 9px;
  color: #c0c4cc;
}

.attachment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.attachment-preview:hover .attachment-overlay {
  opacity: 1;
}

.attachment-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  color: #606266;
}

.attachment-action:hover {
  background: #409eff;
  color: white;
}



.attachment-info {
  margin-top: 6px;
  text-align: center;
  width: 100%;
}

.attachment-name {
  font-size: 12px;
  color: #303133;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 104px;
  font-weight: 400;
}

.attachment-meta {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
}

.loading-attachments {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.upload-main {
  margin-top: 16px;
}

.el-upload-tip {
  margin-top: 12px;
  font-size: 12px;
  color: #909399;
  text-align: left;
}

/* 工具栏按钮组样式 - 参考HTML样本 */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-right: 12px;
  border-right: 1px solid rgba(0,0,0,0.08);
  position: relative;
}

.toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
}

/* 工具栏按钮样式 - 完全按照HTML样本 */
.toolbar-btn {
  padding: 8px 16px;
  background: #ffffff;
  border: 1px solid rgba(0,0,0,0.08);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 90px;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  position: relative;
  overflow: hidden;
}

.toolbar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.toolbar-btn:hover {
  background: #f8f9fa;
  border-color: rgba(0,123,255,0.2);
  color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,123,255,0.1);
}

.toolbar-btn:hover::before {
  opacity: 1;
}

.toolbar-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.toolbar-btn i {
  font-size: 14px;
  width: 16px;
  text-align: center;
  transition: transform 0.2s ease;
}

.toolbar-btn:hover i {
  color: #007bff;
  transform: scale(1.1);
}

/* 提交按钮特殊样式 */
.submit-btn {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.submit-btn:hover {
  background: #0056b3;
  border-color: #0056b3;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.submit-btn:hover i {
  color: #ffffff;
}

/* 确保按钮组在header-buttons中的布局 */
.header-buttons .toolbar-group:last-child {
  margin-left: auto;
}


</style>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Calendar, Edit, Plus, ZoomIn, Minus, Loading, Picture, Close, Check } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import ProjectTaskTable from './components/ProjectTaskTable.vue';
import { uploadFile } from '@/api/fileUpload';
import { uploadProjectAttachment, getProjectAttachments } from '@/api/projectAttachment';
import { getProjectVersion } from '@/api/project';
import { getVersionStatusApi } from '@/api/enums';
import { formatVersionNumber } from '@/utils/versionFormatter';
import { useUserStore } from '@/store/modules/user';
import ExcelJS from 'exceljs';


const router = useRouter();
const route = useRoute();

// 用户store
const userStore = useUserStore();

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 权限控制：判断是否为计划员
const isPlanner = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 2; // 计划员
});

// 权限控制：判断是否可以进行计划变更（项目经理或计划员，且不是只读模式）
const canChangePlan = computed(() => {
  return !props.readOnly && (isProjectManager.value || isPlanner.value);
});

// 表格引用
const taskTableRef = ref(null);

// 项目版本号
const projectVersion = ref('');

// 版本状态枚举
const versionStatusEnum = ref({});

// 组件接收props
const props = defineProps({
  planData: {
    type: Array,
    required: true
  },
  projectId: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  statusOptions: {
    type: Array,
    default: () => []
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  stakeholders: {
    type: Array,
    default: () => []
  },
  contractNo: {
    type: String,
    default: ''
  },
  projectName: {
    type: String,
    default: ''
  }
});

// 定义emit事件
const emit = defineEmits(['refresh-data']);



// 修改上传相关的变量和方法
// 上传相关变量
const uploadDialogVisible = ref(false);
const fileList = ref([]);
const existingAttachments = ref([]); // 已上传的附件列表
const currentPlan = ref(null);
const fileInputRef = ref(null); // 文件输入引用

const previewVisible = ref(false);
const previewUrl = ref('');
const uploadLoading = ref(false);
const attachmentsLoading = ref(false);

// 获取版本状态枚举
const fetchVersionStatusEnum = async () => {
  try {
    const response = await getVersionStatusApi();
    if (response && (response.code === 0 || response.code === 200) && response.data) {
      versionStatusEnum.value = response.data;
    } else {
      // 使用默认枚举
      versionStatusEnum.value = {
        "0": "预审核阶段",
        "1": "正式审核阶段"
      };
    }
  } catch (error) {
    console.error('获取版本状态枚举失败:', error);
    // 使用默认枚举
    versionStatusEnum.value = {
      "0": "预审核阶段",
      "1": "正式审核阶段"
    };
  }
};



// 获取项目版本号
const fetchProjectVersion = async () => {
  if (!props.projectId) {
    console.warn('项目ID为空，无法获取版本号');
    return;
  }

  try {
    const response = await getProjectVersion(props.projectId);
    if (response && (response.code === 0 || response.code === 200) && response.data) {
      const { version, versionStatus } = response.data;

      if (version !== undefined && versionStatus !== undefined) {
        // 根据版本状态格式化版本号
        projectVersion.value = formatVersionNumber(version, versionStatus);
      } else if (typeof version === 'string') {
        // 兼容字符串格式：直接使用，如 "1.0.0"
        projectVersion.value = version.startsWith('v') ? version : `v${version}`;
      } else if (typeof version === 'number') {
        // 兼容数字格式：转换为版本号格式，如 1 -> "v1.0"
        projectVersion.value = `v${version}.0`;
      } else {
        projectVersion.value = 'v1.0';
      }
    } else {
      console.warn('获取项目版本号失败，使用默认版本号');
      projectVersion.value = 'v1.0';
    }
  } catch (error) {
    console.error('获取项目版本号异常:', error);
    projectVersion.value = 'v1.0'; // 出错时使用默认版本号
  }
};

// 处理查看计划详情
const handleViewPlan = (row) => {
  // 获取当前项目ID
  const projectId = route.params.id || props.projectId;

  if (!projectId) {
    ElMessage.warning('无法获取项目ID，请重新进入页面');
    return;
  }

  // 使用原始ID进行API调用，如果没有则使用生成的ID
  const planId = row.originalId || row.id;



  // 使用新的路由路径
  router.push({
    name: 'ProjectPlanDetail',
    params: {
      projectId: projectId,
      planId: planId
    },
    query: {
      projectName: row.taskName,
      from: 'projectDetail'
    }
  });
};

// 处理保存编辑
const handleSave = ({ row, field }) => {
  // 提示保存成功
  ElMessage.success(`${row.taskName} 已保存`);
  // 这里可以添加保存到后端的代码
};

// 处理前置工作项点击
const handlePretaskClick = (serialNumber) => {
  // 这个功能现在由子组件处理，父组件只需要接收事件
};



// 清空文件列表并释放blob URL
const clearFileList = () => {
  fileList.value.forEach(file => {
    try {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
      if (file.url && file.url.startsWith('blob:')) {
        URL.revokeObjectURL(file.url);
      }
    } catch (error) {
      console.error('释放blob URL失败:', error);
    }
  });
  fileList.value = [];
};

// 关闭上传对话框
const closeUploadDialog = () => {
  clearFileList(); // 清空文件列表并释放URL
  uploadDialogVisible.value = false;
};

// 打开上传图片对话框
const uploadAttachment = async (row) => {
  currentPlan.value = row;
  clearFileList(); // 清空新上传文件列表并释放URL
  existingAttachments.value = []; // 清空已有附件列表
  uploadDialogVisible.value = true;

  // 加载已上传的附件
  await loadExistingAttachments();
};

// 加载已上传的附件
const loadExistingAttachments = async () => {
  if (!currentPlan.value) return;

  const planId = currentPlan.value.originalId || currentPlan.value.id;
  attachmentsLoading.value = true;

  try {
    const requestData = {
      planId: planId,
      pageNum: 1,
      pageSize: 20
    };

    const response = await getProjectAttachments(requestData);

    if (response && (response.code === 200 || response.code === 0)) {
      if (response.data) {
        let attachments = [];
        // 检查数据结构：可能是直接数组，也可能有records字段
        if (Array.isArray(response.data)) {
          attachments = response.data;
        } else if (response.data.records && Array.isArray(response.data.records)) {
          attachments = response.data.records;
        }

        // 处理附件数据，添加图片加载状态和字段映射
        existingAttachments.value = attachments.map(attachment => {
          let processedAttachment = {
            ...attachment,
            // 字段映射：API返回的是url，但模板中使用fileUrl
            fileUrl: attachment.url || attachment.fileUrl,
            imageError: false, // 初始化图片加载状态
            isValidImage: false // 标记是否为有效图片
          };



          return processedAttachment;
        });


      } else {
        existingAttachments.value = [];
      }
    } else {
      existingAttachments.value = [];
    }
  } catch (error) {
    ElMessage.error('加载附件列表失败');
    existingAttachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
};


// 预览已上传的附件
const handlePreviewExisting = (attachment) => {
  if (attachment.imageError) {
    // 如果图片加载失败，尝试在新窗口打开
    ElMessage.info('图片预览失败，尝试在新窗口打开');
    window.open(attachment.fileUrl, '_blank');
  } else {
    // 正常预览
    previewUrl.value = attachment.fileUrl;
    previewVisible.value = true;
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes <= 0) return '';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};



// 处理图片加载错误
const handleImageError = (event, attachment) => {
  // 标记图片加载失败
  attachment.imageError = true;
  attachment.isValidImage = false;
  event.target.style.display = 'none';
};

// 处理图片加载成功
const handleImageLoad = (event, attachment) => {
  // 标记图片加载成功
  attachment.imageError = false;
  attachment.isValidImage = true;
};

// 处理文件移除 - 使用与日报相同的方式
const handleRemove = (file) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid || item === file);
  if (index !== -1) {
    const fileToRemove = fileList.value[index];

    // 释放预览URL内存
    try {
      if (fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      if (fileToRemove.url && fileToRemove.url.startsWith('blob:')) {
        URL.revokeObjectURL(fileToRemove.url);
      }
    } catch (error) {
      console.error('释放URL失败:', error);
    }

    fileList.value.splice(index, 1);
  }
};

// 添加图片预览方法 - 与日报页面完全一致
const handlePreviewImage = (file) => {
  previewUrl.value = file.url || file.preview || URL.createObjectURL(file.raw);
  previewVisible.value = true;
};

// 触发文件选择
const triggerFileInput = () => {
  if (fileList.value.length >= 1) {
    ElMessage.warning('最多只能上传1张图片');
    return;
  }
  fileInputRef.value?.click();
};

// 处理原生文件输入变化
const handleNativeFileChange = (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  const file = files[0];

  // 检查文件大小和类型
  const isJPG = file.type === 'image/jpeg';
  const isPNG = file.type === 'image/png';
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isJPG && !isPNG) {
    ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
    return;
  }
  if (!isLt50M) {
    ElMessage.error('上传图片大小不能超过 50MB!');
    return;
  }

  // 限制上传数量
  if (fileList.value.length >= 1) {
    ElMessage.warning('最多只能上传1张图片');
    return;
  }

  try {
    // 创建文件对象，模拟 Element Plus 的文件结构
    const fileObj = {
      name: file.name,
      size: file.size,
      type: file.type,
      raw: file,
      uid: Date.now() + Math.random(), // 生成唯一ID
      preview: URL.createObjectURL(file),
      uploading: false,
      uploadSuccess: false,
      uploadError: false
    };

    fileObj.url = fileObj.preview; // 设置url用于显示

    // 添加到文件列表
    fileList.value.push(fileObj);

    // 清空文件输入，允许重新选择同一文件
    event.target.value = '';

  } catch (error) {
    console.error('创建预览URL失败:', error);
    ElMessage.error('创建图片预览失败');
  }
};

// 提交上传
const submitUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的图片');
    return;
  }

  if (!currentPlan.value) {
    ElMessage.error('未选择计划项');
    return;
  }

  uploadLoading.value = true;

  try {
    // 获取计划ID
    const planId = currentPlan.value.originalId || currentPlan.value.id;
    // 处理每个文件的上传
    const uploadPromises = fileList.value.map(async (file) => {
      try {
        // 设置上传中状态
        file.uploading = true;
        file.uploadError = false;
        file.uploadSuccess = false;

        // 第一步：调用文件预处理接口
        const fileUploadResponse = await uploadFile(file.raw, 50023, {
          // 可以根据需要添加可选参数
          // mimeType: 'image/',
          // maxSize: 10 * 1024 * 1024 // 10MB
        });

        // 检查API响应是否成功
        if (!fileUploadResponse || (fileUploadResponse.code !== 0 && fileUploadResponse.code !== 200)) {
          throw new Error(`API调用失败: ${fileUploadResponse?.msg || '未知错误'}`);
        }

        // 检查文件上传是否成功
        if (!fileUploadResponse.data || !fileUploadResponse.data.success) {
          const errorMsg = fileUploadResponse.data?.errorMessage || '文件上传失败';
          throw new Error(errorMsg);
        }

        // 检查是否获得了文件URL
        if (!fileUploadResponse.data.fileUrl) {
          throw new Error('未获得文件URL');
        }

        const fileUrl = fileUploadResponse.data.fileUrl;

        // 第二步：调用项目附件上传接口
        const attachmentResponse = await uploadProjectAttachment({
          url: fileUrl,
          planId: planId,
          btId: 50023, // 固定传50023
          objectName: file.name // 上传的文档的名字
        });

        if (!attachmentResponse || (attachmentResponse.code !== 0 && attachmentResponse.code !== 200)) {
          throw new Error(attachmentResponse?.msg || '附件保存失败');
        }

        // 上传成功，更新状态
        file.uploading = false;
        file.uploadSuccess = true;
        file.uploadedUrl = fileUrl;

        return {
          fileName: file.name,
          fileUrl: fileUrl,
          success: true
        };

      } catch (error) {
        // 上传失败，更新状态
        file.uploading = false;
        file.uploadError = true;

        return {
          fileName: file.name,
          error: error.message,
          success: false
        };
      }
    });

    // 等待所有文件上传完成
    const results = await Promise.all(uploadPromises);

    // 统计结果
    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 张图片到 ${currentPlan.value.taskName}`);
    }

    if (failCount > 0) {
      const failedFiles = results.filter(r => !r.success).map(r => r.fileName).join(', ');
      ElMessage.warning(`${failCount} 个文件上传失败: ${failedFiles}`);
    }

    // 如果有成功的上传，重新加载附件列表
    if (successCount > 0) {
      clearFileList(); // 清空新上传文件列表并释放URL
      await loadExistingAttachments(); // 重新加载已上传的附件
      // 不关闭对话框，让用户看到上传结果
    }

  } catch (error) {
    ElMessage.error('上传失败');
  } finally {
    uploadLoading.value = false;
  }
};

// 处理计划变更按钮点击 - 只有项目经理和计划员才能执行
const showPlanEdit = () => {
  if (!canChangePlan.value) {
    ElMessage.error('您没有权限进行计划变更');
    return;
  }

  // 获取当前项目ID
  const projectId = route.params.id || props.projectId;

  if (!projectId) {
    ElMessage.warning('无法获取项目ID，请重新进入页面');
    return;
  }
  
  // 跳转到计划变更页面，并传递项目ID、合同号和当前计划数据
  router.push({
    name: 'ProjectPlanEdit',
    params: {
      projectId: projectId
    },
    query: {
      mode: 'edit',
      contractNo: props.contractNo
    }
  });
};

// 展开所有行
const expandAllRows = () => {
  if (taskTableRef.value) {
    taskTableRef.value.expandAll();
    // 删除不必要的成功提示
  }
};

// 收起所有行
const collapseAllRows = () => {
  if (taskTableRef.value) {
    taskTableRef.value.collapseAll();
    // 删除不必要的成功提示
  }
};

// 刷新数据
const refreshData = () => {
  ElMessage.info('正在刷新数据...');
  emit('refresh-data');
};

// 导出Excel功能 - 支持多级分组展开收起
const exportToExcel = async () => {
  try {
    if (!props.planData || props.planData.length === 0) {
      ElMessage.warning('暂无数据可导出');
      return;
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('项目计划');

    // 定义列 - 按照指定的列宽设置
    worksheet.columns = [
      { header: '序号', key: 'serialNumber', width: 10 },
      { header: '工作项名称', key: 'taskName', width: 60 },
      { header: '前置工作项', key: 'parentTask', width: 18.5 },
      { header: '责任人', key: 'responsible', width: 10.5 },
      { header: '执行人', key: 'executorName', width: 10.5 },
      { header: '工作项状态', key: 'status', width: 18.5 },
      { header: '计划开始时间', key: 'planStartTime', width: 20 },
      { header: '计划完成时间', key: 'planEndTime', width: 20 },
      { header: '计划工期', key: 'planDuration', width: 15 },
      { header: '备注', key: 'remark', width: 18.5 },
      { header: '实际开始时间', key: 'actualStartTime', width: 20 },
      { header: '实际完成时间', key: 'actualEndTime', width: 20 },
      { header: '实际工期', key: 'actualDuration', width: 15 }
    ];

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = {
      name: '宋体',
      size: 14,
      bold: true,
      color: { argb: 'FFFFFFFF' }
    };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF6C757D' } // 低饱和度深灰色标题背景
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // 设置标题行各列的对齐方式
    headerRow.getCell(1).alignment = { horizontal: 'left', vertical: 'middle' }; // 序号左对齐
    headerRow.getCell(2).alignment = { horizontal: 'left', vertical: 'middle' }; // 工作项名称左对齐

    // 定义层级颜色 - 低饱和度灰色系，顶级最深，越下级越浅
    const getLevelColor = (level) => {
      const colors = [
        'FF8E9AAF', // 0级 - 最深的低饱和度灰色
        'FFA2ACBC', // 1级 - 稍浅
        'FFB6BEC9', // 2级 - 更浅
        'FFCAD0D6', // 3级 - 很浅
        'FFDEE2E3', // 4级 - 极浅
        'FFF2F4F5'  // 5级及以上 - 几乎白色
      ];
      return colors[Math.min(level, colors.length - 1)];
    };

    let currentRowIndex = 2; // 从第2行开始添加数据

    // 递归处理树形数据并添加分组
    const processTreeData = (data, level = 0) => {
      const groupStartRow = currentRowIndex;

      data.forEach((item) => {
        // 优先使用originalData中的数据，如果没有则使用显示数据
        const originalData = item.originalData || {};

        // 为工作项名称添加层级缩进
        const taskNameWithIndent = '  '.repeat(level) + (item.taskName || originalData.taskName || '');

        // 添加数据行
        const row = worksheet.addRow({
          serialNumber: originalData.seriNum || item.serialNumber || item.seriNum || '',
          taskName: taskNameWithIndent,
          parentTask: item.parentTask || originalData.preSeriNum || item.preSeriNum || '',
          responsible: item.responsible || originalData.ownerName || item.ownerName || '',
          executorName: item.executorName || originalData.executorName || item.executorName || '',
          status: getStatusLabel(originalData.planStatus || item.taskStatus || item.status),
          planStartTime: originalData.planStartTime || item.planStartDate || item.planStartTime || '',
          planEndTime: originalData.planEndTime || item.planEndDate || item.planEndTime || '',
          planDuration: originalData.planDuration || item.planDuration || '',
          remark: originalData.remark || item.note || item.remark || '',
          actualStartTime: originalData.actualStartTime || item.actualStartDate || item.actualStartTime || '',
          actualEndTime: originalData.actualEndTime || item.actualEndDate || item.actualEndTime || '',
          actualDuration: originalData.actualDuration || item.actualDuration || ''
        });

        // 设置行的outline level
        row.outlineLevel = level;

        // 设置行的基本样式
        row.alignment = { vertical: 'middle', horizontal: 'center' }; // 默认中间对齐

        // 根据层级设置背景色 - 低饱和度蓝色渐变
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: getLevelColor(level) }
        };

        // 设置字体样式 - 宋体14号
        const fontColor = level === 0 ? 'FF212529' : level === 1 ? 'FF343A40' : 'FF495057';
        const fontWeight = level === 0 ? true : false;
        row.font = {
          name: '宋体',
          size: 14,
          color: { argb: fontColor },
          bold: fontWeight
        };

        // 设置各列的对齐方式
        row.getCell(1).alignment = { vertical: 'middle', horizontal: 'left' }; // 序号左对齐
        row.getCell(2).alignment = {
          vertical: 'middle',
          horizontal: 'left',
          indent: level // 工作项名称左对齐并使用缩进
        };
        // 其余列保持中间对齐（已在row.alignment中设置）

        currentRowIndex++;

        // 如果有子任务，递归处理
        if (item.children && item.children.length > 0) {
          processTreeData(item.children, level + 1);
        }
      });

      // 如果当前级别有多个项目且有子项，创建分组
      if (level > 0 && currentRowIndex > groupStartRow + 1) {
        try {
          worksheet.groupRows(groupStartRow, currentRowIndex - 1, false); // false表示默认展开
        } catch (error) {
          console.warn('创建分组失败:', error);
        }
      }
    };

    // 处理数据
    processTreeData(props.planData);

    // 设置所有数据行的边框和最终样式
    for (let i = 1; i <= currentRowIndex - 1; i++) {
      const row = worksheet.getRow(i);

      // 为标题行设置特殊边框
      if (i === 1) {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'medium', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'medium', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
          };
        });
      } else {
        // 数据行边框 - 黑色边框
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
          };
        });

        // 为状态列（第6列）添加数据验证下拉框
        const statusCell = row.getCell(6);
        statusCell.dataValidation = {
          type: 'list',
          allowBlank: false,
          formulae: ['"未开始,进行中,已完成,逾期,逾期完成"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: '输入错误',
          error: '请从下拉列表中选择有效的状态值',
          showInputMessage: true,
          promptTitle: '状态选择',
          prompt: '请选择工作项状态：未开始、进行中、已完成、逾期、逾期完成'
        };
      }
    }

    // 生成文件名
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');

    // 清理项目名称，移除特殊字符以避免文件名问题
    const cleanProjectName = (props.projectName || '未命名项目').replace(/[<>:"/\\|?*]/g, '_');

    const fileName = `${cleanProjectName}_项目计划_${projectVersion.value || 'v1.0'}_${dateStr}_${timeStr}.xlsx`;

    // 导出文件
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success('Excel导出成功，支持多级展开收起');
  } catch (error) {
    console.error('导出Excel失败:', error);
    ElMessage.error('导出Excel失败，请重试');
  }
};

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    0: '未开始',
    1: '进行中',
    2: '已完成',
    3: '逾期',
    4: '逾期完成',
    '0': '未开始',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期',
    '4': '逾期完成'
  };
  return statusMap[status] || '未知';
};

// 动态计算表格高度
function calculateTableHeight() {
  nextTick(() => {
    // 确保表格容器能正确显示滚动条
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      const windowHeight = window.innerHeight;
      tableContainer.style.maxHeight = `${windowHeight - 160}px`;
    }
  });
}

// 监听项目ID变化，重新获取版本号
watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    fetchProjectVersion();
  }
}, { immediate: true });

onMounted(() => {
  calculateTableHeight();
  window.addEventListener('resize', debounce(calculateTableHeight, 100));
  // 获取版本状态枚举
  fetchVersionStatusEnum();
});

onUnmounted(() => {
  window.removeEventListener('resize', debounce(calculateTableHeight, 100));

  // 清理所有的 blob URL
  clearFileList();
});

// 防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}
</script>

<style lang="scss" scoped>
/* tab内容容器 */
.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: visible; /* 允许显示滚动条 */
}

/* 项目计划容器 */
.project-plan {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-top: 10px; /* 减少顶部边距 */
  overflow: visible; /* 允许显示滚动条 */
}

/* 表格外包装 */
.table-wrapper {
  margin: 0 5px 0; /* 移除底部边距，给表格更多空间 */
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: auto; /* 允许出现滚动条 */
  padding-bottom: 10px; /* 添加内边距确保底部内容可见 */
}

/* 其他样式保持不变 */
.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px; /* 减少内边距 */
  border-bottom: none;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 8px; /* 减少底部边距 */
  margin-left: 5px;
  margin-right: 5px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

/* 头部按钮容器样式 */
.header-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}



.plan-header h3 {
  font-size: 15px;
  margin: 0;
  font-weight: 500;
  color: #303133;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.plan-header h3 .el-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #409EFF;
}

/* 计划变更按钮样式 */
.plan-header .el-button {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
}

.plan-header .el-button .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 上传对话框样式 */
.upload-container {
  padding: 20px;
  position: relative;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 0;
}

/* 图片缩略图样式优化 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.file-name-thumb {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 12px;
  text-align: center;
  padding: 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 9;
}

.upload-notice {
  padding: 10px 16px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  margin-top: 5px;
}

.upload-list-title {
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
}

.numbering {
  font-weight: bold;
  color: #409EFF;
  margin-right: 4px;
}

.upload-tip-inline {
  margin-left: 20px;
  color: #909399;
  font-size: 13px;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  margin-bottom: 15px;
}



.upload-tip-global {
  position: absolute;
  right: 20px;
  bottom: 10px;
  color: #909399;
  font-size: 13px;
  line-height: 1.5;
}

/* 预览对话框样式 */
:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding: 15px 20px;
  margin-right: 0;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}
</style> 