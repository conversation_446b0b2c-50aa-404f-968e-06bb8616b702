@import "../variables.scss";
@import "../mixins.scss";

// 对话框全局样式
.el-dialog {
  border-radius: $border-radius-medium;
  box-shadow: $box-shadow-dark;
  overflow: hidden;

  // 对话框标题区域
  .el-dialog__header {
    padding: 15px 20px;
    margin-right: 0;
    background-color: $background-color-white;
    border-bottom: 1px solid $border-color-lighter;
    position: relative;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: $text-primary;
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: $spacing-mini;
      }
    }

    .el-dialog__headerbtn {
      top: 15px;
      right: 20px;
      width: 20px;
      height: 20px;
      font-size: 18px;
    }
  }

  // 对话框内容区域
  .el-dialog__body {
    padding: 20px;
    color: $text-regular;
    font-size: $font-size-base;
    word-break: break-all;
    max-height: calc(80vh - 100px);
    overflow-y: auto;
  }

  // 对话框底部区域
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid $border-color-lighter;
    display: flex;
    justify-content: center;
    align-items: center;

    // 底部按钮间距
    .el-button + .el-button {
      margin-left: $spacing-base;
    }
    
    // 对话框按钮样式
    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 0;
    }
  }

  // 不同尺寸的对话框
  &.el-dialog--small {
    max-width: 480px;
  }

  &.el-dialog--default {
    max-width: 600px;
  }

  &.el-dialog--medium {
    max-width: 700px;
  }

  &.el-dialog--large {
    max-width: 900px;
  }

  // 上传对话框特殊样式
  &.upload-dialog {
    .el-dialog__body {
      padding: $spacing-base;
    }

    // 上传区域样式
    .upload-area {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      margin-bottom: $spacing-small;
    }
  }
}

// 全局对话框容器样式，确保垂直居中
.el-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.el-overlay-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  padding: 30px 0;
}

// 上传容器样式
.upload-container {
  padding: 20px;
  position: relative;
}

// 预览容器样式
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
} 