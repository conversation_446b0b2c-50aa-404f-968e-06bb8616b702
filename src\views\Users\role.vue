<template>
  <div class="role-management">
    <!-- 返回按钮 -->
    <div class="back-section">
      <el-button @click="handleBack" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>

    <!-- 角色列表表格 -->
    <div class="table-container">
      <el-table 
        v-loading="loading"
        :data="roles"
        stripe
        style="width: 100%"
        :row-style="{ height: '48px' }"
        :cell-style="{ padding: '4px 0' }"
      >
        <el-table-column type="selection" width="50" align="center" />
        
        <el-table-column label="序号" width="70" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        
        <el-table-column prop="roleName" label="角色名称" min-width="200" align="center" />
        
        <el-table-column prop="remark" label="备注" min-width="300" align="center" />
        
        <el-table-column prop="createdTime" label="创建时间" min-width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              size="small"
              @click="handleEditRole(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共 {{ total }} 条
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色权限配置抽屉 -->
    <el-drawer
      v-model="dialogVisible"
      direction="rtl"
      size="600px"
      destroy-on-close
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :show-close="false"
      class="role-drawer"
    >
      <template #header>
        <div class="custom-drawer-header">
          <div class="drawer-close-btn" @click="dialogVisible = false">
            <el-icon><Close /></el-icon>
          </div>
          <div class="drawer-title">
            {{ currentRole.roleName ? `编辑角色 - ${currentRole.roleName}` : '新增角色' }}
          </div>
        </div>
      </template>

      <div class="role-edit-content drawer-optimized">
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-layout">
            <div class="section-header">
              <el-icon class="section-icon"><User /></el-icon>
              <span class="section-title">基本信息</span>
            </div>
            <div class="section-content">
              <div class="form-fields">
                <!-- 角色名称 -->
                <div class="field-item">
                  <label class="field-label">角色名称</label>
                  <div class="field-value">
                    <el-input
                      v-model="editForm.name"
                      readonly
                      class="readonly-input"
                      placeholder=""
                    >
                      <template #prefix>
                        <el-icon class="readonly-icon"><User /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>

                <!-- 角色备注 -->
                <div class="field-item">
                  <label class="field-label">角色备注</label>
                  <div class="field-value">
                    <el-input
                      v-model="editForm.description"
                      placeholder="请输入角色备注"
                      class="description-input"
                    >
                      <template #prefix>
                        <el-icon><Document /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 权限配置区域 -->
        <div class="form-section">
          <div class="section-layout">
            <div class="section-header">
              <el-icon class="section-icon"><Setting /></el-icon>
              <span class="section-title">权限配置</span>
            </div>
            <div class="section-content">
              <div class="permission-content">
                <div class="permission-header">
                  <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
                </div>

                <!-- 权限树形控件 -->
                <div class="permission-tree">
                  <el-tree
                    ref="permissionTree"
                    :data="permissionsData"
                    :props="{ label: 'name', children: 'children' }"
                    show-checkbox
                    node-key="id"
                    default-expand-all
                    highlight-current
                    check-strictly
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <template #footer>
        <div class="drawer-footer">
          <el-button type="primary" @click="saveRolePermissions">
            <el-icon><Check /></el-icon>
            <span>保存</span>
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { 
  ArrowLeft, 
  User, 
  Document, 
  Setting, 
  Check, 
  Close 
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getRoleList, updateRole, getMenuTree, getMenuIdsByRole } from '@/api/user';

const router = useRouter();

// 加载状态
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 角色列表数据
const roles = ref([]);

// 弹窗相关
const dialogVisible = ref(false);
const currentRole = ref({});
const selectAll = ref(false);
const permissionTree = ref(null);
const editForm = ref({
  name: '',
  description: ''
});

// 权限数据
const permissionsData = ref([]);

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  return dateString.replace('T', ' ').substring(0, 19);
};

// 获取角色列表
const getRoleListData = async () => {
  loading.value = true;
  
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const response = await getRoleList(params);
    
    if (response && response.data) {
      roles.value = response.data;
      total.value = response.total || 0;
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    ElMessage.error('获取角色列表失败：' + (error.message || '网络错误'));
  } finally {
    loading.value = false;
  }
};

// 直接使用fetch的备选API调用方法
const getMenuTreeDataDirect = async () => {
  const userStore = JSON.parse(localStorage.getItem('user') || '{}');
  const token = userStore.token;

  const response = await fetch('/api/menu/tree', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token || ''
    }
  });

  const data = await response.json();

  if (data.code === 200 && data.data && Array.isArray(data.data)) {
    return data.data.map(menu => ({
      id: menu.id,
      name: menu.menuName,
      children: menu.children ? menu.children.map(child => ({
        id: child.id,
        name: child.menuName
      })) : []
    }));
  } else {
    throw new Error(`菜单数据获取失败`);
  }
};

// 获取菜单树
const getMenuTreeData = async (retryCount = 0) => {
  try {
    let menuData;
    try {
      // 首先尝试使用标准API调用
      const response = await getMenuTree();

      if (response && response.data && Array.isArray(response.data)) {
        menuData = response.data.map(menu => ({
          id: menu.id,
          name: menu.menuName,
          children: menu.children ? menu.children.map(child => ({
            id: child.id,
            name: child.menuName
          })) : []
        }));
      } else {
        throw new Error('API响应格式异常');
      }
    } catch (apiError) {
      // 如果标准API失败，静默尝试直接调用
      if (apiError.isApiError) {
        menuData = await getMenuTreeDataDirect();
      } else {
        throw apiError;
      }
    }

    permissionsData.value = menuData;

  } catch (error) {
    // 如果是第一次失败，尝试重试一次
    if (retryCount === 0) {
      setTimeout(() => {
        getMenuTreeData(1);
      }, 1000);
      return;
    }

    // 使用Mock数据作为备选方案
    permissionsData.value = [
      { id: 1, name: '工作台', children: [] },
      { id: 2, name: '项目管理', children: [] },
      { id: 3, name: '用户管理', children: [] }
    ];

    if (retryCount > 0) {
      ElMessage.warning('菜单数据加载失败，已使用默认配置');
    }
  }
};

// 返回上一页
const handleBack = () => {
  router.back();
};

// 编辑角色权限
const handleEditRole = async (role) => {
  currentRole.value = { ...role };
  // 初始化表单数据
  editForm.value = {
    name: role.roleName || '',
    description: role.remark || ''
  };

  dialogVisible.value = true;
  loading.value = true;

  await getRolePermissions(role);
};

// 直接获取角色权限的备选方法
const getRolePermissionsDirect = async (roleId) => {
  const userStore = JSON.parse(localStorage.getItem('user') || '{}');
  const token = userStore.token;

  const response = await fetch(`/api/menu/ids-by-role?roleId=${roleId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token || ''
    }
  });

  const data = await response.json();

  if (data.code === 200 && data.data && Array.isArray(data.data)) {
    return data.data;
  } else {
    throw new Error(`角色权限获取失败`);
  }
};

// 获取角色权限的独立函数
const getRolePermissions = async (role, retryCount = 0) => {
  try {
    let permissionKeys = [];
    try {
      // 首先尝试使用标准API调用
      const response = await getMenuIdsByRole(role.id);

      if (response && Array.isArray(response)) {
        permissionKeys = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        permissionKeys = response.data;
      } else {
        throw new Error('API响应格式异常');
      }
    } catch (apiError) {
      // 如果标准API失败，静默尝试直接调用
      if (apiError.isApiError) {
        permissionKeys = await getRolePermissionsDirect(role.id);
      } else {
        throw apiError;
      }
    }

    // 等待权限树渲染完成后再设置选中状态
    await new Promise(resolve => setTimeout(resolve, 100));

    // 设置选中的节点
    permissionTree.value?.setCheckedKeys(permissionKeys);

    // 更新全选状态
    const allKeys = [];
    const getAllKeys = (data) => {
      data.forEach(item => {
        allKeys.push(item.id);
        if (item.children && item.children.length) {
          getAllKeys(item.children);
        }
      });
    };
    getAllKeys(permissionsData.value);

    // 如果所有权限都被选中，则全选为true
    selectAll.value = allKeys.length === permissionKeys.length &&
      allKeys.every(key => permissionKeys.includes(key));

  } catch (error) {
    // 如果是第一次失败，尝试重试一次
    if (retryCount === 0) {
      setTimeout(() => {
        getRolePermissions(role, 1);
      }, 1000);
      return;
    }

    // 使用Mock数据作为备选方案
    const permissionKeys = role.id === 1 ? [1, 2, 3] : [1, 2]; // 管理员有所有权限，其他角色有部分权限

    // 等待权限树渲染完成后再设置选中状态
    await new Promise(resolve => setTimeout(resolve, 100));

    // 设置选中的节点
    permissionTree.value?.setCheckedKeys(permissionKeys);

    if (retryCount > 0) {
      ElMessage.warning('权限数据加载失败，已使用默认配置');
    }
  } finally {
    loading.value = false;
  }
};

// 全选/取消全选
const handleSelectAll = (val) => {
  if (val) {
    // 选中所有节点
    const allKeys = [];
    const getAllKeys = (data) => {
      data.forEach(item => {
        allKeys.push(item.id);
        if (item.children && item.children.length) {
          getAllKeys(item.children);
        }
      });
    };
    getAllKeys(permissionsData.value);
    permissionTree.value?.setCheckedKeys(allKeys);
  } else {
    // 取消选中所有节点
    permissionTree.value?.setCheckedKeys([]);
  }
};

  // 保存角色权限
const saveRolePermissions = async () => {
  // 表单验证
  if (!editForm.value.description.trim()) {
    ElMessage.warning('请输入角色备注');
    return;
  }
  
  // 获取选中的权限ID（仅获取完全选中的节点）
  const checkedKeys = permissionTree.value?.getCheckedKeys() || [];
  
  // 准备保存的数据
  const saveData = {
    id: currentRole.value.id,
    roleName: currentRole.value.roleName,
    remark: editForm.value.description.trim(),
    menuIds: checkedKeys
  };
  
  try {
    // 显示加载状态
    loading.value = true;
    
    await updateRole(saveData);
      
      // 显示成功消息
    ElMessage.success(`成功更新角色 ${saveData.roleName}`);
      
      // 关闭对话框
      dialogVisible.value = false;
    
    // 刷新角色列表以获取最新数据
    getRoleListData();
  } catch (error) {
    // 显示错误信息
    console.error('保存角色失败:', error);
    ElMessage.error('保存失败：' + (error.message || '网络错误'));
  } finally {
    // 无论成功或失败，都关闭加载状态
    loading.value = false;
  }
};

// 页码变更事件
const handlePageChange = (page) => {
  currentPage.value = page;
  getRoleListData();
};

// 页面加载时获取数据
onMounted(() => {
  getRoleListData();
  getMenuTreeData();
});
</script>

<style scoped>
.role-management {
  padding: 16px;
  background: #ffffff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 8px;
  overflow: hidden;
}

.back-section {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-container {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0 8px 0;
  gap: 16px;
}

.pagination-info {
  color: #606266;
  font-size: 14px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: #f5f7fa;
  color: #303133;
}

/* 权限弹窗样式 */
.role-dialog :deep(.el-dialog) {
  margin: 15vh auto !important;
}

.role-dialog :deep(.el-dialog__header) {
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.role-dialog :deep(.el-dialog__body) {
  padding: 20px 20px 10px;
}

.role-dialog :deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

.role-edit-container {
  padding: 0;
  width: 100%;
}

/* 全局样式修复 */
:deep(.el-form-item__content) {
  width: 100%;
}

.permission-item {
  margin-bottom: 0;
}

/* 修复权限配置标签和内容的样式 */
.custom-label {
  display: inline-flex !important;
  align-items: center;
}

.permission-content {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.permission-header {
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.permission-tree {
  height: 280px;
  overflow-y: auto;
  padding: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.custom-label {
  display: flex;
  align-items: center;
  gap: 5px;
}

.input-with-icon {
  position: relative;
  padding-left: 20px;
  display: block;
  width: 100%;
}

.description-input {
  width: 100%;
  box-sizing: border-box;
}

.textarea-icon {
  position: absolute;
  left: 0;
  top: 8px;
  color: #909399;
  z-index: 1;
}

:deep(.el-button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 角色抽屉样式 */
.role-drawer :deep(.el-drawer) {
  border-radius: 0;
}

.role-drawer :deep(.el-drawer__header) {
  margin: 0;
  padding: 0;
  border-bottom: none;
}

.role-drawer :deep(.el-drawer__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
}

.role-drawer :deep(.el-drawer__footer) {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
}

/* 自定义抽屉头部样式 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
}

.drawer-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
  background-color: transparent;
  margin-right: 12px;
  flex-shrink: 0;
}

.drawer-close-btn:hover {
  background-color: #f5f7fa;
  color: #606266;
}

.drawer-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

/* 抽屉内容样式 */
.role-edit-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #ffffff;
}

.drawer-optimized {
  display: flex;
  flex-direction: column;
}

.form-section {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-layout {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px 8px 24px;
  background-color: #ffffff;
  border-bottom: none;
  margin-bottom: 8px;
}

.section-icon {
  color: #409EFF;
  font-size: 14px;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  line-height: 1;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-content {
  padding: 0 24px 16px 24px;
}

/* 表单字段样式 */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 28px;
  margin-bottom: 12px;
}

.field-label {
  font-size: 14px;
  color: #666666;
  font-weight: 400;
  line-height: 1.4;
  min-width: 80px;
  text-align: left;
  flex-shrink: 0;
}

.field-value {
  flex: 1;
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
}

/* 只读输入框样式 */
.readonly-input :deep(.el-input__wrapper) {
  background-color: #f0f2f5 !important;
  border: 1px solid #d9d9d9 !important;
  box-shadow: none !important;
  cursor: default;
  border-radius: 8px;
}

.readonly-input :deep(.el-input__inner) {
  color: #8c8c8c !important;
  cursor: default;
  background-color: transparent !important;
  font-weight: 400;
}

.readonly-input :deep(.el-input__wrapper:hover) {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
  background-color: #f0f2f5 !important;
}

.readonly-input :deep(.el-input__wrapper.is-focus) {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
  background-color: #f0f2f5 !important;
}

/* 只读输入框图标样式 */
.readonly-icon {
  color: #bfbfbf;
  font-size: 14px;
}

/* 可编辑字段样式 */
.field-value :deep(.el-select) {
  width: 100%;
}

.field-value :deep(.el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  background-color: #ffffff;
}

.field-value :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.field-value :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 底部操作按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #ffffff;
  margin-top: auto;
}
</style> 