import request from '@/utils/request'

// 上传计划附件（图片）
export function uploadProjectAttachment(data) {
  return request({
    url: '/project-attachments/upload',
    method: 'post',
    data
  })
}

// Mock数据 - 上传计划附件
export function uploadProjectAttachmentMock(data) {
  return new Promise((resolve, reject) => {
    // 模拟参数验证
    if (!data.url) {
      reject(new Error('文件URL不能为空'))
      return
    }
    if (!data.planId) {
      reject(new Error('计划ID不能为空'))
      return
    }
    // 新增参数验证
    if (!data.btId) {
      reject(new Error('业务类型ID不能为空'))
      return
    }
    if (!data.objectName) {
      reject(new Error('文件名称不能为空'))
      return
    }

    // 模拟上传过程（500ms-1s延迟）
    const delay = Math.random() * 500 + 500

    setTimeout(() => {
      // 95%成功率，5%失败率用于测试错误处理
      if (Math.random() > 0.05) {
        resolve({
          code: 0,
          msg: '附件上传成功',
          data: true
        })
      } else {
        reject(new Error('附件保存失败，请重试'))
      }
    }, delay)
  })
}

// 分页查询计划附件列表
export function getProjectAttachments(data) {
  return request({
    url: '/project-attachments/attachments',
    method: 'post',
    data
  })
}

// Mock数据 - 分页查询计划附件列表
export function getProjectAttachmentsMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      // 生成模拟附件数据
      const mockAttachments = []
      const attachmentCount = Math.floor(Math.random() * 5) + 1 // 1-5个附件
      
      for (let i = 0; i < attachmentCount; i++) {
        const timestamp = Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000 // 最近7天内
        mockAttachments.push({
          id: 1000 + i,
          planId: data.planId,
          fileName: `进展图片_${i + 1}.jpg`,
          url: `https://mock-storage.example.com/attachments/plan_${data.planId}_${timestamp}_${i}.jpg`,
          fileSize: Math.floor(Math.random() * 2000000) + 100000, // 100KB-2MB
          uploadedTime: new Date(timestamp).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(/\//g, '-'),
          uploaderName: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)]
        })
      }

      resolve({
        code: 0,
        msg: '查询成功',
        data: {
          records: mockAttachments,
          total: mockAttachments.length,
          size: data.pageSize || 20,
          current: data.pageNum || 1,
          pages: 1
        }
      })
    }, 300)
  })
}

// 删除计划附件
export function deleteProjectAttachment(attachmentId) {
  return request({
    url: `/project-attachments/delete/${attachmentId}`,
    method: 'delete'
  })
}

// Mock数据 - 删除计划附件
export function deleteProjectAttachmentMock(attachmentId) {
  return new Promise((resolve, reject) => {
    if (!attachmentId) {
      reject(new Error('附件ID不能为空'))
      return
    }

    setTimeout(() => {
      // 95%成功率
      if (Math.random() > 0.05) {
        resolve({
          code: 0,
          msg: '附件删除成功',
          data: true
        })
      } else {
        reject(new Error('附件删除失败，请重试'))
      }
    }, 300)
  })
}
