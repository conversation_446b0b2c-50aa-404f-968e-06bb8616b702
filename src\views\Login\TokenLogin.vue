<template>
  <div class="token-login-container">
    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-container">
            <el-icon class="logo-icon" color="#5B7BFA" :size="40"><Briefcase /></el-icon>
          </div>
          <h2>项目管理系统</h2>
          <p v-if="status === 'loading'">正在验证登录信息...</p>
          <p v-else-if="status === 'success'">登录成功，正在跳转...</p>
          <p v-else-if="status === 'error'">登录失败</p>
        </div>
        
        <div class="status-content">
          <!-- 加载状态 -->
          <div v-if="status === 'loading'" class="loading-section">
            <el-icon class="loading-icon is-loading"><Loading /></el-icon>
            <p>正在验证Token...</p>
          </div>
          
          <!-- 成功状态 -->
          <div v-else-if="status === 'success'" class="success-section">
            <el-icon class="success-icon" color="#67C23A" :size="60"><CircleCheckFilled /></el-icon>
            <p>登录成功！</p>
            <p class="redirect-info">{{ redirectCountdown }}秒后自动跳转到工作台</p>
          </div>
          
          <!-- 错误状态 -->
          <div v-else-if="status === 'error'" class="error-section">
            <el-icon class="error-icon" color="#F56C6C" :size="60"><CircleCloseFilled /></el-icon>
            <p>登录失败</p>
            <p class="error-message">{{ errorMessage }}</p>
            <el-button type="primary" @click="goToLogin" class="retry-button">
              返回登录页
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="login-bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Briefcase, Loading, CircleCheckFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { getCurrentUser, validateExternalToken } from '@/api/auth'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const status = ref('loading') // loading, success, error
const errorMessage = ref('')
const redirectCountdown = ref(3)

// 处理token登录
const handleTokenLogin = async () => {
  try {
    // 从URL参数中获取token
    const token = route.query.token

    if (!token) {
      throw new Error('未提供有效的登录Token')
    }

    // 解码token（如果需要）
    const decodedToken = decodeURIComponent(token)

    // 首先验证token是否有效（可选，如果后端支持）
    try {
      await validateExternalToken(decodedToken)
    } catch (validateError) {
      // 如果验证接口不存在或失败，继续尝试直接使用token获取用户信息
      console.warn('Token验证接口调用失败，尝试直接获取用户信息:', validateError.message)
    }

    // 设置token到store
    userStore.setToken(decodedToken)

    // 使用token获取用户信息来验证token有效性
    const userInfoResponse = await getCurrentUser()

    if (userInfoResponse.success && userInfoResponse.data) {
      const userInfo = userInfoResponse.data

      const userInfoToStore = {
        userId: userInfo.userId,
        username: userInfo.username,
        account: userInfo.account,
        realName: userInfo.realName || userInfo.account || userInfo.username,
        phone: userInfo.phone,
        position: userInfo.position,
        area: userInfo.area,
        roles: userInfo.roles,
        roleId: userInfo.roles && userInfo.roles.length > 0 ? userInfo.roles[0] : null,
        permissions: userInfo.permissions
      }

      userStore.setUserInfo(userInfoToStore)

      // 登录成功
      status.value = 'success'
      ElMessage.success(`欢迎回来，${userInfoToStore.realName}！`)

      // 开始倒计时跳转
      startRedirectCountdown()

    } else {
      throw new Error('Token无效或已过期，请重新登录')
    }

  } catch (error) {
    console.error('Token登录失败:', error)
    status.value = 'error'

    // 根据错误类型设置不同的错误信息
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      errorMessage.value = 'Token已过期或无效，请重新登录'
    } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
      errorMessage.value = '您没有访问权限，请联系管理员'
    } else if (error.message.includes('网络')) {
      errorMessage.value = '网络连接失败，请检查网络后重试'
    } else {
      errorMessage.value = error.message || 'Token验证失败，请重新登录'
    }

    // 清除可能设置的无效token
    userStore.logout()
  }
}

// 开始跳转倒计时
const startRedirectCountdown = () => {
  const timer = setInterval(() => {
    redirectCountdown.value--
    
    if (redirectCountdown.value <= 0) {
      clearInterval(timer)
      // 跳转到目标页面或工作台
      const redirectPath = route.query.redirect || '/workbench'
      router.push(redirectPath)
    }
  }, 1000)
}

// 返回登录页
const goToLogin = () => {
  router.push('/login')
}

// 组件挂载时执行token登录
onMounted(() => {
  handleTokenLogin()
})
</script>

<style scoped>
.token-login-container {
  position: relative;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #e1e8fd 0%, #c9d4f8 100%);
  overflow: hidden;
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card {
  width: 420px;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: slideInUp 0.8s ease-out;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-icon {
  padding: 16px;
  background-color: rgba(91, 123, 250, 0.15);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(91, 123, 250, 0.2);
}

.login-header h2 {
  font-size: 26px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.login-header p {
  color: #909399;
  font-size: 16px;
}

.status-content {
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-icon {
  font-size: 60px;
  color: #5B7BFA;
}

.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.success-icon {
  animation: bounceIn 0.6s ease-out;
}

.redirect-info {
  color: #67C23A;
  font-weight: 500;
}

.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.error-icon {
  animation: shake 0.6s ease-out;
}

.error-message {
  color: #F56C6C;
  font-size: 14px;
  margin-bottom: 10px;
}

.retry-button {
  margin-top: 10px;
}

/* 背景装饰 - 复用登录页样式 */
.login-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.1), rgba(91, 123, 250, 0.3));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 400px;
  height: 400px;
  bottom: -150px;
  right: -150px;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.1), rgba(91, 123, 250, 0.2));
  animation-delay: 2s;
}

.circle-3 {
  width: 200px;
  height: 200px;
  top: 40%;
  right: 10%;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.05), rgba(91, 123, 250, 0.15));
  animation-delay: 4s;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
