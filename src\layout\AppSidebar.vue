<template>
  <div class="app-sidebar">
    <div class="sidebar-header">
      <el-icon class="logo-icon" :size="24" color="#5B7BFA">
        <Monitor />
      </el-icon>
      <span class="app-name">项目管理系统</span>
    </div>

    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :collapse="false"
      background-color="transparent"
      text-color="#555555"
      @select="handleSelect"
      unique-opened>

      <!-- 工作台菜单 - 根据权限显示 -->
      <el-menu-item
        v-if="hasMenuPermission('/workbench')"
        index="/workbench"
        class="menu-item">
        <el-icon><DataBoard /></el-icon>
        <span>工作台</span>
      </el-menu-item>

      <!-- 项目管理菜单 - 根据权限显示 -->
      <el-sub-menu
        v-if="hasMenuPermission('/projects')"
        index="/projects"
        class="menu-item">
        <template #title>
          <el-icon><Briefcase /></el-icon>
          <span>项目管理</span>
        </template>

        <!-- 全部项目子菜单 -->
        <el-menu-item
          v-if="hasMenuPermission('/projects/all')"
          index="/projects/all"
          class="sub-menu-item all-projects-item">
          <span>全部项目</span>
        </el-menu-item>

        <!-- 加载状态 -->
        <el-menu-item v-if="projectsLoading" index="loading" class="sub-menu-item loading-item" disabled>
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载项目中...</span>
        </el-menu-item>

        <!-- 项目列表容器 -->
        <div v-if="!projectsLoading && userProjects.length > 0" class="projects-container">
          <div class="projects-scroll">
            <!-- 动态生成的用户项目列表 -->
            <el-menu-item
              v-for="project in userProjects"
              :key="project.id"
              :index="`/project-detail/${project.id}`"
              class="sub-menu-item project-menu-item">
              <span class="project-name" :title="project.projectShortName || project.projectName">
                {{ project.projectShortName || project.projectName }}
              </span>
            </el-menu-item>
          </div>
        </div>

        <!-- 无项目提示 -->
        <el-menu-item v-if="!projectsLoading && userProjects.length === 0" index="no-projects" class="sub-menu-item" disabled>
          <span style="color: #999;">暂无项目</span>
        </el-menu-item>
      </el-sub-menu>

      <!-- 用户管理菜单 - 根据权限显示 -->
      <el-menu-item
        v-if="hasMenuPermission('/users')"
        index="/users"
        class="menu-item">
        <el-icon><User /></el-icon>
        <span>用户管理</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DataBoard,
  Briefcase,
  User,
  Monitor,
  Loading
} from '@element-plus/icons-vue'
import { getAllUserProjects, getAllUserProjectsMock } from '@/api/project'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 用户项目列表
const userProjects = ref([])
const projectsLoading = ref(false)
// 直接显示所有项目，不需要展开收起功能

// 根据当前路由计算活动菜单
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/projects/')) {
    return path
  }
  return path
})

// 权限检查方法
const hasMenuPermission = (path) => {
  // 如果用户未登录，不显示任何菜单
  if (!userStore.isLoggedIn()) {
    return false
  }

  // 如果权限数据还在加载中，暂时不显示菜单
  if (permissionStore.loading) {
    return false
  }

  // 项目列表不受菜单权限控制，始终显示（根据需求说明）
  if (path === '/api/project/list-all') {
    return true
  }

  // 检查用户是否有访问该路径的权限
  return permissionStore.hasPathPermission(path)
}

// 获取用户项目列表
const getUserProjects = async (retryCount = 0) => {
  let shouldRetry = false
  
  try {
    // 只有在用户已登录时才获取项目
    if (!userStore.isLoggedIn()) {
      userProjects.value = []
      return
    }
    
    projectsLoading.value = true
    let response
    
    try {
      // 首先尝试真实API
      response = await getAllUserProjects()
    } catch (apiError) {
      // 检查是否是超时错误，如果是则进行重试
      if (apiError.code === 'ECONNABORTED' && retryCount < 2) {
        shouldRetry = true
        setTimeout(() => {
          getUserProjects(retryCount + 1)
        }, 2000) // 2秒后重试
        return
      }
      // 如果真实API失败，使用Mock数据
      response = await getAllUserProjectsMock()
    }
    
    // 检查响应数据格式
    if (response && response.data) {
      userProjects.value = Array.isArray(response.data) ? response.data : []
    } else if (response && Array.isArray(response)) {
      // 如果直接返回数组
      userProjects.value = response
    } else {
      userProjects.value = []
    }
  } catch (error) {
    console.error('获取用户项目列表完全失败:', error)
    userProjects.value = []
  } finally {
    // 只有在不重试的情况下才停止loading
    if (!shouldRetry) {
      projectsLoading.value = false
    }
  }
}

// 处理菜单选择事件
const handleSelect = (index) => {
  // 检查是否是项目详情页面的跳转
  if (index.startsWith('/project-detail/')) {
    // 从index中提取项目ID
    const projectId = index.replace('/project-detail/', '');
    // 查找对应的项目数据
    const project = userProjects.value.find(p => p.id === projectId);

    if (project && project.contractNo) {
      // 如果找到项目且有contractNo，携带contractNo参数跳转
      router.push({
        path: index,
        query: {
          contractNo: project.contractNo
        }
      });
    } else {
      // 如果没有找到项目或没有contractNo，直接跳转
      router.push(index);
    }
  } else {
    // 非项目详情页面，直接跳转
    router.push(index);
  }
}

// 监听用户登录状态变化
watch(() => userStore.token, (newToken) => {
  if (newToken) {
    // 用户登录后获取项目列表
    getUserProjects()
  } else {
    // 用户退出登录时清空项目列表
    userProjects.value = []
  }
}, { immediate: false })

// 监听权限数据变化
watch(() => permissionStore.userMenuIds, () => {
  // 权限数据更新后，重新获取项目列表
  if (userStore.isLoggedIn()) {
    getUserProjects()
  }
}, { immediate: false })

// 组件挂载时设置默认选中的菜单和获取用户项目
onMounted(() => {
  if (route.path === '/') {
    router.push('/workbench')
  }
  
  // 获取用户项目列表
  getUserProjects()
})

// 暴露刷新项目列表的方法给父组件
defineExpose({
  refreshProjects: getUserProjects
})
</script>

<style scoped>
.app-sidebar {
  width: 240px; /* 增加宽度，给项目名称更多空间 */
  height: calc(100vh - 20px);
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
  margin: 10px 0 10px 10px;
  padding: 24px 0; /* 增加内边距 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  border: 1px solid #f0f0f0; /* 添加细边框 */
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 0 24px 24px 24px; /* 增加底部内边距 */
  margin-bottom: 16px; /* 减少底部边距 */
  border-bottom: 1px solid #f5f5f5; /* 更浅的分割线 */
  position: relative;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, #5B7BFA 0%, transparent 100%); /* 渐变分割线 */
}

.logo-icon {
  margin-right: 12px;
  color: #5B7BFA;
  font-size: 26px; /* 增大图标 */
}

.app-name {
  font-size: 19px; /* 稍微增大字体 */
  font-weight: 600; /* 增加字重 */
  color: #2c3e50; /* 更深的颜色 */
  letter-spacing: 0.5px; /* 增加字间距 */
}

.sidebar-menu {
  border-right: none;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

/* 一级菜单项通用样式 */
.el-menu-item, .el-sub-menu__title {
  height: 48px !important; /* 稍微减少高度 */
  line-height: 48px !important;
  padding: 0 24px !important;
  text-align: left !important;
  margin: 0 !important;
  transition: all 0.3s ease; /* 添加过渡动画 */
}

.menu-item {
  margin: 6px 16px !important; /* 增加间距 */
  border-radius: 10px; /* 增加圆角 */
  position: relative;
  overflow: hidden;
}

/* 为菜单项添加微妙的背景渐变 */
.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(91, 123, 250, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-item:hover::before {
  opacity: 1;
}

/* 子菜单项样式 */
.sub-menu-item {
  height: 42px !important; /* 稍微增加高度 */
  line-height: 42px !important;
  padding-left: 56px !important; /* 增加左边距 */
  margin: 3px 20px !important; /* 调整边距 */
  border-radius: 8px; /* 增加圆角 */
  background-color: transparent !important;
  position: relative;
  transition: all 0.3s ease;
}

/* 为子菜单项添加左侧指示线 */
.sub-menu-item::before {
  content: '';
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: #d1d5db;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.sub-menu-item:hover::before,
.sub-menu-item.is-active::before {
  background-color: #5B7BFA;
  transform: translateY(-50%) scale(1.2);
}

/* 项目列表容器 */
.projects-container {
  max-height: calc(100vh - 320px); /* 调整最大高度 */
  overflow: hidden;
  position: relative;
}

/* 添加渐变遮罩效果 */
.projects-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 20px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.8));
  pointer-events: none;
  z-index: 1;
}

.projects-scroll {
  max-height: calc(100vh - 320px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 10px; /* 为渐变遮罩留出空间 */
}

/* 自定义滚动条样式 */
.projects-scroll::-webkit-scrollbar {
  width: 6px; /* 增加滚动条宽度 */
}

.projects-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05); /* 更透明的轨道 */
  border-radius: 3px;
  margin: 4px 0; /* 添加上下边距 */
}

.projects-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #5B7BFA, #4a67d8); /* 渐变滚动条 */
  border-radius: 3px;
  transition: background 0.3s ease;
}

.projects-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #4a67d8, #3b56c4); /* 悬停时的渐变 */
}

/* 项目菜单项样式 */
.project-menu-item {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 1px solid rgba(91, 123, 250, 0.08);
  margin: 2px 20px !important; /* 调整边距 */
}

/* 动态项目菜单项字体样式 */
.project-menu-item span {
  font-size: 14px !important;
  font-weight: 500; /* 增加字重 */
  color: #374151; /* 更深的文字颜色 */
}

.project-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px; /* 增加文字宽度 */
  cursor: pointer;
  font-size: 14px;
  position: relative;
  padding: 2px 0; /* 增加上下内边距 */
}

/* 为项目名称添加悬停效果 */
.project-menu-item:hover .project-name {
  color: #5B7BFA;
  transform: translateX(2px); /* 轻微右移效果 */
  transition: all 0.3s ease;
}





/* 菜单项图标样式 */
.el-menu-item .el-icon, .el-sub-menu__title .el-icon {
  margin-right: 14px; /* 增加图标间距 */
  vertical-align: middle;
  font-size: 18px; /* 增大图标 */
  color: #6b7280; /* 图标颜色 */
  transition: all 0.3s ease;
}

/* 菜单项文本样式 */
.el-menu-item span, .el-sub-menu__title span {
  font-size: 15px; /* 调整字体大小 */
  vertical-align: middle;
  font-weight: 500; /* 增加字重 */
  color: #374151; /* 文字颜色 */
  transition: all 0.3s ease;
}

/* 确保固定子菜单项（如"全部项目"）保持正常字体大小 */
.sub-menu-item:not(.project-menu-item) span {
  font-size: 14px !important; /* 调整子菜单字体 */
  font-weight: 500;
  color: #6b7280;
}

/* 选中状态样式 - 工作台和用户管理 */
.el-menu-item.is-active {
  background: linear-gradient(135deg, #5B7BFA 0%, #4a67d8 100%) !important;
  color: #FFFFFF !important;
  box-shadow: 0 4px 12px rgba(91, 123, 250, 0.3) !important;
  transform: translateY(-1px); /* 轻微上移效果 */
}

.el-menu-item.is-active .el-icon {
  color: #FFFFFF !important;
}

.el-menu-item.is-active span {
  color: #FFFFFF !important;
  font-weight: 600;
}

/* 子菜单项选中样式 */
.sub-menu-item.is-active {
  background: linear-gradient(135deg, #5B7BFA 0%, #4a67d8 100%) !important;
  color: #FFFFFF !important;
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.25) !important;
  transform: translateX(2px); /* 轻微右移效果 */
}

.sub-menu-item.is-active span {
  color: #FFFFFF !important;
  font-weight: 600;
}

/* 项目管理菜单选中样式 */
.el-sub-menu.is-active > .el-sub-menu__title {
  color: #5B7BFA !important;
  background-color: rgba(91, 123, 250, 0.08) !important;
}

.el-sub-menu.is-active > .el-sub-menu__title .el-icon {
  color: #5B7BFA !important;
}

.el-sub-menu.is-active > .el-sub-menu__title span {
  color: #5B7BFA !important;
  font-weight: 600;
}

/* 菜单悬停样式 */
.el-menu-item:hover:not(.is-active), .el-sub-menu__title:hover {
  background: linear-gradient(135deg, rgba(91, 123, 250, 0.08) 0%, rgba(91, 123, 250, 0.04) 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.el-menu-item:hover:not(.is-active) .el-icon,
.el-sub-menu__title:hover .el-icon {
  color: #5B7BFA !important;
  transform: scale(1.1);
}

.el-menu-item:hover:not(.is-active) span,
.el-sub-menu__title:hover span {
  color: #5B7BFA !important;
}

.sub-menu-item:hover:not(.is-active) {
  background: linear-gradient(135deg, rgba(91, 123, 250, 0.06) 0%, rgba(91, 123, 250, 0.03) 100%) !important;
  transform: translateX(3px);
}

.sub-menu-item:hover:not(.is-active) span {
  color: #5B7BFA !important;
}

/* 子菜单背景 */
:deep(.el-menu--inline) {
  background-color: #FFFFFF !important;
  padding: 5px 0;
}

/* 箭头调整 */
:deep(.el-sub-menu__icon-arrow) {
  right: 24px;
  color: #9ca3af;
  transition: all 0.3s ease;
}

:deep(.el-sub-menu.is-opened .el-sub-menu__icon-arrow) {
  color: #5B7BFA;
  transform: rotate(180deg);
}



/* 优化子菜单展开动画 */
:deep(.el-menu--inline) {
  background-color: #FFFFFF !important;
  padding: 8px 0;
  border-radius: 0 0 8px 8px;
  margin-top: -4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* 为整个侧边栏添加微妙的内阴影 */
.app-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.02);
  pointer-events: none;
  z-index: 1;
}

/* 确保菜单内容在阴影之上 */
.sidebar-menu {
  position: relative;
  z-index: 2;
}

/* "全部项目"菜单项特殊样式 */
.all-projects-item {
  margin-bottom: 12px !important; /* 增加底部间距 */
  position: relative;
}

.all-projects-item::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 32px;
  right: 20px;
  height: 1px;
  background: linear-gradient(90deg, rgba(91, 123, 250, 0.3) 0%, transparent 100%);
}

.all-projects-item span {
  font-weight: 600 !important;
  color: #5B7BFA !important;
}

/* 调整项目容器的上边距 */
.projects-container {
  margin-top: 8px; /* 减少上边距，因为不再显示标题 */
}

/* 响应式设计 */
@media (max-height: 768px) {
  .projects-scroll {
    max-height: calc(100vh - 400px);
  }

  .projects-container {
    max-height: calc(100vh - 400px);
  }
}

@media (max-height: 600px) {
  .app-sidebar {
    padding: 16px 0;
  }

  .sidebar-header {
    padding: 0 20px 16px 20px;
    margin-bottom: 12px;
  }

  .el-menu-item, .el-sub-menu__title {
    height: 42px !important;
    line-height: 42px !important;
  }

  .sub-menu-item {
    height: 36px !important;
    line-height: 36px !important;
  }
}

/* 添加平滑的加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.project-menu-item {
  animation: fadeInUp 0.3s ease-out;
}

.project-menu-item:nth-child(1) { animation-delay: 0.1s; }
.project-menu-item:nth-child(2) { animation-delay: 0.2s; }
.project-menu-item:nth-child(3) { animation-delay: 0.3s; }
.project-menu-item:nth-child(4) { animation-delay: 0.4s; }
.project-menu-item:nth-child(5) { animation-delay: 0.5s; }

/* 加载状态样式 */
.loading-item {
  opacity: 0.7;
  pointer-events: none;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-right: 8px;
  color: #5B7BFA;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style> 