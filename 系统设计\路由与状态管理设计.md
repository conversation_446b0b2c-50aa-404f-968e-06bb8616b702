# 交付部项目管理系统 - 路由与状态管理设计

## 1. 路由设计

系统采用 Vue Router 进行路由管理，使用嵌套路由实现多级页面结构，同时支持路由元信息配置实现权限控制、导航菜单自动生成等功能。

### 1.1 路由结构

```javascript
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/LoginView.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/AppLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue'),
        meta: {
          title: '工作台',
          icon: 'dashboard',
          keepAlive: true
        }
      },
      {
        path: 'projects',
        name: 'Projects',
        component: () => import('@/views/project/ProjectLayout.vue'),
        meta: {
          title: '项目管理',
          icon: 'project'
        },
        redirect: '/projects/list',
        children: [
          {
            path: 'list',
            name: 'ProjectList',
            component: () => import('@/views/project/ProjectListView.vue'),
            meta: {
              title: '全部项目',
              keepAlive: true
            }
          },
          {
            path: ':id',
            name: 'ProjectDetail',
            component: () => import('@/views/project/ProjectDetailView.vue'),
            meta: {
              title: '项目详情',
              activeMenu: '/projects/list',
              hidden: true
            },
            redirect: to => `/projects/${to.params.id}/basic`,
            children: [
              {
                path: 'basic',
                name: 'ProjectBasicInfo',
                component: () => import('@/views/project/detail/BasicInfoView.vue'),
                meta: {
                  title: '基本信息'
                }
              },
              {
                path: 'workitems',
                name: 'ProjectWorkItems',
                component: () => import('@/views/project/detail/WorkItemsView.vue'),
                meta: {
                  title: '项目工作项'
                }
              },
              {
                path: 'stakeholders',
                name: 'ProjectStakeholders',
                component: () => import('@/views/project/detail/StakeholdersView.vue'),
                meta: {
                  title: '干系人'
                }
              },
              {
                path: 'schedule',
                name: 'ProjectSchedule',
                component: () => import('@/views/project/detail/ScheduleView.vue'),
                meta: {
                  title: '日程'
                }
              }
            ]
          }
        ]
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/user/UserLayout.vue'),
        meta: {
          title: '用户管理',
          icon: 'user',
          permissions: ['admin', 'manager']
        },
        redirect: '/users/list',
        children: [
          {
            path: 'list',
            name: 'UserList',
            component: () => import('@/views/user/UserListView.vue'),
            meta: {
              title: '用户列表',
              keepAlive: true
            }
          },
          {
            path: 'roles',
            name: 'RoleManagement',
            component: () => import('@/views/user/RoleManagementView.vue'),
            meta: {
              title: '角色管理',
              permissions: ['admin']
            }
          }
        ]
      },
      {
        path: '/404',
        name: 'NotFound',
        component: () => import('@/views/error/NotFound.vue'),
        meta: {
          title: '404',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 };
  }
});

// 路由守卫实现权限控制
router.beforeEach((to, from, next) => {
  // 实现权限检查、登录状态验证等
  // ...
});

export default router;
```

### 1.2 路由元信息（Meta）说明

- `title`: 页面标题，用于面包屑、标签页、浏览器标题显示
- `icon`: 菜单图标
- `keepAlive`: 是否缓存页面组件
- `requiresAuth`: 是否需要登录才能访问
- `permissions`: 访问该路由所需的权限
- `hidden`: 是否在菜单中隐藏
- `activeMenu`: 当前路由激活的菜单项

### 1.3 动态路由管理

系统支持根据用户角色和权限动态加载路由，以实现精细的权限控制：

```javascript
// 动态添加路由的示例
function generateAsyncRoutes(permissions) {
  const asyncRoutes = filterAsyncRoutes(allAsyncRoutes, permissions);
  asyncRoutes.forEach(route => {
    router.addRoute(route);
  });
}

// 根据权限过滤路由
function filterAsyncRoutes(routes, permissions) {
  const res = [];
  routes.forEach(route => {
    const tmp = { ...route };
    if (hasPermission(permissions, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, permissions);
      }
      res.push(tmp);
    }
  });
  return res;
}
```

### 1.4 标签页导航实现

系统顶部的标签页导航基于路由和缓存实现：

```javascript
// 标签页状态管理示例
const tabsStore = defineStore('tabs', {
  state: () => ({
    visitedViews: [],
    cachedViews: []
  }),
  actions: {
    addView(view) {
      this.addVisitedView(view);
      this.addCachedView(view);
    },
    addVisitedView(view) {
      if (this.visitedViews.some(v => v.path === view.path)) return;
      this.visitedViews.push({
        ...view,
        title: view.meta?.title || 'Unknown'
      });
    },
    addCachedView(view) {
      if (this.cachedViews.includes(view.name)) return;
      if (view.meta?.keepAlive) {
        this.cachedViews.push(view.name);
      }
    },
    // 删除、关闭其他标签等操作...
  }
});
```

## 2. 状态管理设计

系统采用 Pinia 进行状态管理，通过模块化组织不同功能的状态。

### 2.1 核心 Store 模块

#### 用户状态 (userStore)

管理用户登录信息、权限等状态：

```javascript
import { defineStore } from 'pinia';
import { login, logout, getUserInfo } from '@/services/user';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: {},
    roles: [],
    permissions: []
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
    hasRole: (state) => (role) => state.roles.includes(role),
    hasPermission: (state) => (permission) => state.permissions.includes(permission)
  },
  actions: {
    async login(credentials) {
      try {
        const { token } = await login(credentials);
        this.token = token;
        localStorage.setItem('token', token);
        return token;
      } catch (error) {
        console.error('Login failed:', error);
        throw error;
      }
    },
    async getUserInfo() {
      try {
        const data = await getUserInfo();
        this.userInfo = data;
        this.roles = data.roles || [];
        this.permissions = data.permissions || [];
        return data;
      } catch (error) {
        console.error('Get user info failed:', error);
        throw error;
      }
    },
    async logout() {
      try {
        await logout();
        this.resetState();
        localStorage.removeItem('token');
      } catch (error) {
        console.error('Logout failed:', error);
        throw error;
      }
    },
    resetState() {
      this.token = '';
      this.userInfo = {};
      this.roles = [];
      this.permissions = [];
    }
  }
});
```

#### 应用状态 (appStore)

管理应用级别的配置和UI状态：

```javascript
import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', {
  state: () => ({
    sidebar: {
      opened: localStorage.getItem('sidebarStatus') ? !!+localStorage.getItem('sidebarStatus') : true,
      withoutAnimation: false
    },
    device: 'desktop',
    size: localStorage.getItem('size') || 'medium',
    theme: localStorage.getItem('theme') || 'light'
  }),
  actions: {
    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened;
      this.sidebar.withoutAnimation = false;
      if (this.sidebar.opened) {
        localStorage.setItem('sidebarStatus', '1');
      } else {
        localStorage.setItem('sidebarStatus', '0');
      }
    },
    closeSidebar({ withoutAnimation }) {
      this.sidebar.opened = false;
      this.sidebar.withoutAnimation = withoutAnimation;
      localStorage.setItem('sidebarStatus', '0');
    },
    toggleDevice(device) {
      this.device = device;
    },
    setSize(size) {
      this.size = size;
      localStorage.setItem('size', size);
    },
    setTheme(theme) {
      this.theme = theme;
      localStorage.setItem('theme', theme);
      // 应用主题样式
      document.documentElement.setAttribute('data-theme', theme);
    }
  }
});
```

#### 项目状态 (projectStore)

管理项目相关的状态：

```javascript
import { defineStore } from 'pinia';
import { getProjects, getProjectDetail } from '@/services/project';

export const useProjectStore = defineStore('project', {
  state: () => ({
    projectList: [],
    projectListLoading: false,
    totalProjects: 0,
    currentProject: null,
    projectDetailLoading: false,
    filterForm: {
      name: '',
      status: '',
      dateRange: []
    },
    pagination: {
      currentPage: 1,
      pageSize: 10
    }
  }),
  getters: {
    responsibleProjects: (state) => {
      return state.projectList.filter(project => project.isResponsible);
    },
    projectStatusCounts: (state) => {
      const counts = { inProgress: 0, completed: 0, pending: 0, overdue: 0 };
      state.projectList.forEach(project => {
        counts[project.status] = (counts[project.status] || 0) + 1;
      });
      return counts;
    }
  },
  actions: {
    async fetchProjects(params = {}) {
      this.projectListLoading = true;
      try {
        const { data, total } = await getProjects({
          ...this.filterForm,
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          ...params
        });
        this.projectList = data;
        this.totalProjects = total;
        return { data, total };
      } catch (error) {
        console.error('Fetch projects failed:', error);
        throw error;
      } finally {
        this.projectListLoading = false;
      }
    },
    async getProjectById(id) {
      this.projectDetailLoading = true;
      try {
        const data = await getProjectDetail(id);
        this.currentProject = data;
        return data;
      } catch (error) {
        console.error(`Get project ${id} failed:`, error);
        throw error;
      } finally {
        this.projectDetailLoading = false;
      }
    },
    updateFilterForm(form) {
      this.filterForm = { ...this.filterForm, ...form };
    },
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    }
  }
});
```

#### 工作项状态 (workItemStore)

管理项目工作项相关的状态：

```javascript
import { defineStore } from 'pinia';
import { getWorkItems, updateWorkItem, createWorkItem } from '@/services/workItem';

export const useWorkItemStore = defineStore('workItem', {
  state: () => ({
    workItems: [],
    loading: false,
    total: 0,
    filterForm: {
      name: '',
      status: '',
      assignee: null
    },
    pagination: {
      currentPage: 1,
      pageSize: 10
    }
  }),
  actions: {
    async fetchWorkItems(projectId, params = {}) {
      this.loading = true;
      try {
        const { data, total } = await getWorkItems(projectId, {
          ...this.filterForm,
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          ...params
        });
        this.workItems = data;
        this.total = total;
        return { data, total };
      } catch (error) {
        console.error('Fetch work items failed:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async createWorkItem(projectId, workItem) {
      try {
        const data = await createWorkItem(projectId, workItem);
        return data;
      } catch (error) {
        console.error('Create work item failed:', error);
        throw error;
      }
    },
    async updateWorkItem(workItemId, updates) {
      try {
        const data = await updateWorkItem(workItemId, updates);
        // 更新本地数据
        const index = this.workItems.findIndex(item => item.id === workItemId);
        if (index !== -1) {
          this.workItems[index] = { ...this.workItems[index], ...updates };
        }
        return data;
      } catch (error) {
        console.error('Update work item failed:', error);
        throw error;
      }
    },
    updateFilterForm(form) {
      this.filterForm = { ...this.filterForm, ...form };
    },
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    }
  }
});
```

### 2.2 状态管理最佳实践

1. **数据模块化**: 按功能领域划分Store，避免单个Store过于臃肿

2. **避免状态重复**: 相关联的状态应只维护在一个Store中，避免多处维护导致同步问题

3. **请求与状态分离**: 
   - 通过服务层调用API
   - Store中只管理状态及触发服务调用
   - 组件从Store获取状态

4. **使用TypeScript类型定义**:
   ```typescript
   interface UserState {
     token: string;
     userInfo: UserInfo;
     roles: string[];
     permissions: string[];
   }
   
   interface UserInfo {
     id: number;
     username: string;
     avatar: string;
     email: string;
     // ...
   }
   ```

5. **组合式API风格使用**:
   ```javascript
   // 在组件中使用Store
   import { useUserStore } from '@/stores/user';
   import { storeToRefs } from 'pinia';
   
   export default {
     setup() {
       const userStore = useUserStore();
       // 解构获取响应式状态
       const { userInfo, roles } = storeToRefs(userStore);
       // 直接获取actions
       const { login, logout } = userStore;
       
       return {
         userInfo,
         roles,
         login,
         logout
       };
     }
   };
   ```

6. **持久化存储**:
   - 敏感数据（如Token）在用户关闭浏览器后清除
   - 界面偏好设置等可持久化至localStorage

7. **状态重置**:
   - 用户退出登录时重置相关状态
   - 提供重置特定模块状态的方法

## 3. 状态与路由的交互

### 3.1 路由守卫中的状态检查

```javascript
import router from './router';
import { useUserStore } from './stores/user';
import { usePermissionStore } from './stores/permission';

const whiteList = ['/login', '/404'];

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  const permissionStore = usePermissionStore();
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 交付部项目管理系统` : '交付部项目管理系统';
  
  if (userStore.token) {
    if (to.path === '/login') {
      // 已登录重定向到首页
      next({ path: '/' });
    } else {
      if (userStore.roles.length === 0) {
        try {
          // 获取用户信息
          await userStore.getUserInfo();
          // 生成可访问路由
          const accessRoutes = await permissionStore.generateRoutes(userStore.roles);
          // 动态添加路由
          accessRoutes.forEach(route => {
            router.addRoute(route);
          });
          // 重新导航，保证新添加的路由可用
          next({ ...to, replace: true });
        } catch (error) {
          // 重置用户信息，重定向到登录
          userStore.resetState();
          next(`/login?redirect=${to.path}`);
        }
      } else {
        next();
      }
    }
  } else {
    // 未登录访问白名单页面
    if (whiteList.includes(to.path)) {
      next();
    } else {
      // 重定向到登录页
      next(`/login?redirect=${to.path}`);
    }
  }
});
```

### 3.2 标签导航与路由联动

```javascript
import { useTabsStore } from './stores/tabs';

router.afterEach((to) => {
  // 不在标签导航中显示的路由
  const excludedRoutes = ['/login', '/404'];
  
  if (!excludedRoutes.includes(to.path)) {
    const tabsStore = useTabsStore();
    tabsStore.addView(to);
  }
});
```

## 4. API服务层设计

### 4.1 服务层结构

```
services/
├── axios.js              # Axios实例和拦截器配置
├── user.js               # 用户相关API
├── project.js            # 项目相关API
├── workItem.js           # 工作项相关API
└── stakeholder.js        # 干系人相关API
```

### 4.2 Axios配置

```javascript
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';

// 创建Axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data;
    // API返回结构约定: { code: number, data: any, message: string }
    if (res.code !== 0) {
      ElMessage({
        message: res.message || '请求错误',
        type: 'error',
        duration: 5 * 1000
      });
      
      // 特定错误码处理
      if (res.code === 401) {
        // Token过期或无效
        const userStore = useUserStore();
        userStore.logout();
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
      }
      
      return Promise.reject(new Error(res.message || '请求错误'));
    } else {
      return res.data;
    }
  },
  error => {
    console.error('响应错误:', error);
    ElMessage({
      message: error.message || '网络错误',
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default request;
```

### 4.3 API服务示例

```javascript
// services/project.js
import request from './axios';

export function getProjects(params) {
  return request({
    url: '/projects',
    method: 'get',
    params
  });
}

export function getProjectDetail(id) {
  return request({
    url: `/projects/${id}`,
    method: 'get'
  });
}

export function createProject(data) {
  return request({
    url: '/projects',
    method: 'post',
    data
  });
}

export function updateProject(id, data) {
  return request({
    url: `/projects/${id}`,
    method: 'put',
    data
  });
}

export function deleteProject(id) {
  return request({
    url: `/projects/${id}`,
    method: 'delete'
  });
}

// 更多项目相关API...
```

## 5. 权限设计

### 5.1 权限模型

系统采用基于角色的访问控制（RBAC）模型：

- **用户（User）**: 系统的使用者
- **角色（Role）**: 用户的身份，如管理员、项目经理、普通用户等
- **权限（Permission）**: 对特定资源的特定操作权限，如创建项目、删除用户等

### 5.2 权限Store

```javascript
import { defineStore } from 'pinia';
import { getPermissions } from '@/services/permission';

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    routes: [],
    dynamicRoutes: [],
    permissionMap: {}
  }),
  actions: {
    async generateRoutes(roles) {
      // 管理员拥有所有权限
      if (roles.includes('admin')) {
        this.dynamicRoutes = asyncRoutes;
        this.routes = constantRoutes.concat(asyncRoutes);
        return asyncRoutes;
      }
      
      // 根据角色过滤路由
      try {
        const permissions = await getPermissions(roles);
        this.permissionMap = this.transformPermissions(permissions);
        const accessedRoutes = filterAsyncRoutes(asyncRoutes, this.permissionMap);
        this.dynamicRoutes = accessedRoutes;
        this.routes = constantRoutes.concat(accessedRoutes);
        return accessedRoutes;
      } catch (error) {
        console.error('生成路由失败:', error);
        throw error;
      }
    },
    transformPermissions(permissions) {
      // 转换权限列表为权限映射对象，便于快速查找
      const permMap = {};
      permissions.forEach(perm => {
        permMap[perm.code] = true;
      });
      return permMap;
    }
  }
});
```

### 5.3 指令级权限控制

```javascript
// 自定义权限指令
app.directive('permission', {
  mounted(el, binding) {
    const { value } = binding;
    const userStore = useUserStore();
    const permissionStore = usePermissionStore();
    
    const hasPermission = userStore.roles.includes('admin') || 
      (Array.isArray(value) ? 
        value.some(v => permissionStore.permissionMap[v]) : 
        permissionStore.permissionMap[value]);
    
    if (!hasPermission) {
      el.parentNode?.removeChild(el);
    }
  }
});

// 使用示例
// <el-button v-permission="'project:create'">新建项目</el-button>
// <el-button v-permission="['project:update', 'project:delete']">操作</el-button>
```

### 5.4 组件级权限控制

```javascript
// 权限检查组件
const AuthWrapper = {
  props: {
    permission: {
      type: [String, Array],
      required: true
    }
  },
  setup(props, { slots }) {
    const userStore = useUserStore();
    const permissionStore = usePermissionStore();
    
    const hasPermission = computed(() => {
      if (userStore.roles.includes('admin')) {
        return true;
      }
      if (Array.isArray(props.permission)) {
        return props.permission.some(p => permissionStore.permissionMap[p]);
      }
      return permissionStore.permissionMap[props.permission];
    });
    
    return () => hasPermission.value ? slots.default?.() : null;
  }
};

// 使用示例
// <AuthWrapper permission="project:create">
//   <el-button>新建项目</el-button>
// </AuthWrapper>
``` 