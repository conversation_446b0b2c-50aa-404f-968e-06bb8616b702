<template>
  <el-dialog v-model="visible" title="计划生成 (AI)" width="600px" :close-on-click-modal="false">
    <!-- 未生成状态 -->
    <div v-if="status === 'initial'">
      <el-alert
        type="info"
        :closable="false"
      >
        <div class="alert-content">
          <el-icon class="info-icon"><InfoFilled /></el-icon>
          可以上传合同文件，自动生成项目计划。
        </div>
      </el-alert>

      <div class="upload-section">
        <div class="upload-label">合同文件：</div>
        <el-upload
          :key="uploadKey"
          ref="uploadRef"
          class="file-upload"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :multiple="false"
          :limit="1"
          accept=".doc,.docx,.pdf"
        >
          <!-- 完全自定义按钮 -->
          <div class="custom-upload-btn">
            <i class="upload-icon"><Upload /></i>
            <span class="upload-text">上传文件</span>
          </div>
        </el-upload>
      </div>

      <div class="file-info">
        支持文件格式：doc .docx .pdf，单个文件不能超过50Mb
      </div>

      <!-- 上传文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          
          <!-- 进度条样式 -->
          <div class="progress-area">
            <el-progress 
              :percentage="file.percentage || 0" 
              :status="file.status" 
              :stroke-width="5"
              :show-text="file.status !== 'success'"
            />
            <span v-if="file.status === 'success'" class="success-icon">
              <el-icon><Check /></el-icon>
            </span>
            <span v-if="file.status === 'exception'" class="error-text">失败</span>
            <el-button v-if="file.status === 'success'" type="danger" link size="small" class="delete-btn" @click="handleDeleteFile(index)">删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成中状态 -->
    <div v-else-if="status === 'generating'" class="generating-container">
      <div class="loading-circle">
        <el-icon class="is-loading"><Loading /></el-icon>
      </div>
      <div class="generating-text">正在生成计划中，请稍后...</div>
    </div>

    <!-- 生成失败状态 -->
    <div v-else-if="status === 'failed'" class="failed-container">
      <el-alert
        type="error"
        :closable="false"
      >
        <div class="alert-content">
          <el-icon class="error-icon"><CircleCloseFilled /></el-icon>
          AI生成失败，请检查文件格式或重新尝试。
        </div>
      </el-alert>

      <div class="upload-section">
        <div class="upload-label">合同文件：</div>
        <el-upload
          :key="uploadKey"
          ref="uploadRef"
          class="file-upload"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :multiple="false"
          :limit="1"
          accept=".doc,.docx,.pdf"
        >
          <!-- 完全自定义按钮 -->
          <div class="custom-upload-btn">
            <i class="upload-icon"><Upload /></i>
            <span class="upload-text">重新上传文件</span>
          </div>
        </el-upload>
      </div>

      <div class="file-info">
        支持文件格式：doc .docx .pdf，单个文件不能超过50Mb
      </div>

      <!-- 上传文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>

          <!-- 进度条样式 -->
          <div class="progress-area">
            <el-progress
              :percentage="file.percentage || 0"
              :status="file.status"
              :stroke-width="5"
              :show-text="file.status !== 'success'"
            />
            <span v-if="file.status === 'success'" class="success-icon">
              <el-icon><Check /></el-icon>
            </span>
            <span v-if="file.status === 'exception'" class="error-text">失败</span>
            <el-button v-if="file.status === 'success'" type="danger" link size="small" class="delete-btn" @click="handleDeleteFile(index)">删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成完成状态 -->
    <div v-else-if="status === 'completed'" class="completed-container">
      <el-alert
        type="success"
        :closable="false"
      >
        <div class="alert-content">
          <el-icon class="success-icon"><SuccessFilled /></el-icon>
          已成功生成v0.1的项目计划。
        </div>
      </el-alert>

      <div class="upload-section">
        <div class="upload-label">合同文件：</div>
        <el-upload
          :key="uploadKey"
          ref="uploadRef"
          class="file-upload"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :multiple="false"
          :limit="1"
          accept=".doc,.docx,.pdf"
        >
          <!-- 完全自定义按钮 -->
          <div class="custom-upload-btn">
            <i class="upload-icon"><Upload /></i>
            <span class="upload-text">重新上传文件</span>
          </div>
        </el-upload>
      </div>

      <div class="file-info">
        支持文件格式：doc .docx .pdf，单个文件不能超过50Mb
      </div>

      <!-- 上传文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          
          <!-- 进度条样式 -->
          <div class="progress-area">
            <el-progress 
              :percentage="file.percentage || 0" 
              :status="file.status" 
              :stroke-width="5"
              :show-text="file.status !== 'success'"
            />
            <span v-if="file.status === 'success'" class="success-icon">
              <el-icon><Check /></el-icon>
            </span>
            <span v-if="file.status === 'exception'" class="error-text">失败</span>
            <el-button v-if="file.status === 'success'" type="danger" link size="small" class="delete-btn" @click="handleDeleteFile(index)">删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :disabled="status === 'generating' || ((status === 'initial' || status === 'failed' || status === 'completed') && (fileList.length === 0 || !fileList.some(f => f.status === 'success')))">
        {{ status === 'completed' ? '重新生成' : '确定' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import { Document, Upload, Check, Loading, InfoFilled, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: Boolean,
  detail: { type: Object, default: () => ({}) },
  taskStatus: { type: String, default: 'initial' } // initial, generating, completed, failed
})

const emit = defineEmits(['update:modelValue', 'generate'])

const visible = ref(props.modelValue)
const status = ref(props.taskStatus)
// 初始化为空数组，不显示任何示例文件
const fileList = ref([])
const uploadRef = ref()
// 用于强制重新渲染上传组件的key
const uploadKey = ref(0)

watch(() => props.modelValue, v => visible.value = v)
watch(visible, v => emit('update:modelValue', v))
watch(() => props.taskStatus, v => status.value = v)

// 处理文件变更
function handleFileChange(file) {
  // 文件大小验证（50MB限制）
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过50MB')
    return
  }

  // 文件格式验证
  const allowedTypes = ['.doc', '.docx', '.pdf']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedTypes.some(type => fileName.endsWith(type))

  if (!isValidType) {
    ElMessage.error('只支持上传.doc、.docx、.pdf格式的文件')
    return
  }

  // 清空之前的文件（限制只能上传一个文件）
  fileList.value = []

  // 创建新文件对象
  const newFile = {
    name: file.name,
    percentage: 0, // 从0开始
    status: '',
    raw: file.raw
  }

  // 添加到文件列表
  fileList.value.push(newFile)

  // 模拟上传进度
  simulateUploadProgress(fileList.value.length - 1)
}

// 模拟上传进度 - 不再随机失败
function simulateUploadProgress(fileIndex) {
  const file = fileList.value[fileIndex]
  if (!file) return
  
  const totalTime = 1500 // 总共1.5秒完成上传
  const intervalTime = 50 // 更新更频繁，动画更平滑
  const totalSteps = totalTime / intervalTime
  let currentStep = 0
  
  const interval = setInterval(() => {
    currentStep++
    
    // 使用非线性进度增加，模拟实际上传速度变化
    // 开始较慢，中间加速，最后减速完成
    let progress
    const ratio = currentStep / totalSteps
    if (ratio < 0.3) {
      // 开始阶段，较慢
      progress = ratio * 100 * 0.7
    } else if (ratio < 0.8) {
      // 中间阶段，加速
      progress = 30 + (ratio - 0.3) * 100 * 1.2
    } else {
      // 最后阶段，减速
      progress = 90 + (ratio - 0.8) * 100 * 0.5
    }
    
    progress = Math.min(Math.round(progress), 100)
    
    // 更新文件进度
    if (fileIndex < fileList.value.length) {
      fileList.value[fileIndex].percentage = progress
      
      // 设置最终状态
      if (progress >= 100) {
        clearInterval(interval)
        fileList.value[fileIndex].status = 'success' // 始终成功
      }
    } else {
      clearInterval(interval)
    }
  }, intervalTime)
}

// 处理文件移除
function handleFileRemove(file) {
  const index = fileList.value.findIndex(f => f.name === file.name)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 删除文件
function handleDeleteFile(index) {
  fileList.value.splice(index, 1)

  // 清空上传组件的文件输入框，确保可以重新上传相同文件
  clearUploadComponent()
}

// 清空上传组件的通用方法
function clearUploadComponent() {
  // 通过改变key强制重新渲染上传组件，这是最可靠的方法
  uploadKey.value++

  // 额外的清空逻辑作为备用
  if (uploadRef.value) {
    try {
      // 清空Element Plus组件的文件列表
      uploadRef.value.clearFiles()

      // 使用nextTick确保DOM更新后再清空原生input
      nextTick(() => {
        const inputElement = uploadRef.value?.$el?.querySelector('input[type="file"]')
        if (inputElement) {
          inputElement.value = ''
        }
      })
    } catch (error) {
      console.error('Error clearing upload component:', error)
    }
  }
}

// 处理取消
function handleCancel() {
  visible.value = false
  // 清空文件列表和上传组件
  fileList.value = []
  clearUploadComponent()
}

// 处理确认生成
function handleConfirm() {
  if (status.value === 'initial' || status.value === 'failed' || status.value === 'completed') {
    // 检查是否有上传的文件
    if (fileList.value.length === 0) {
      if (status.value === 'completed') {
        ElMessage.warning('请上传新的合同文件以重新生成')
      } else {
        ElMessage.warning('请先上传合同文件')
      }
      return
    }

    // 检查文件是否上传完成
    const uploadedFile = fileList.value.find(file => file.status === 'success')
    if (!uploadedFile) {
      ElMessage.warning('请等待文件上传完成')
      return
    }

    // 开始生成任务，传递文件信息
    emit('generate', {
      projectId: props.detail.id,
      file: uploadedFile.raw
    })

    if (status.value === 'completed') {
      ElMessage.success('已开始重新生成项目任务，请稍候...')
    } else {
      ElMessage.success('已开始生成项目任务，请稍候...')
    }
    visible.value = false
  }
}
</script>

<style scoped>
.alert-content {
  display: flex;
  align-items: center;
  line-height: 24px;
}

.info-icon, .success-icon {
  margin-right: 8px;
  font-size: 16px;
}

.upload-section {
  display: flex;
  align-items: center;
  margin: 20px 0 10px;
}

.upload-label {
  margin-right: 10px;
  min-width: 80px;
}

.file-upload {
  display: inline-block;
}

/* 自定义上传按钮 */
.custom-upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #409EFF;
  background-color: #fff;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s;
  height: 32px;
  line-height: 32px;
}

.custom-upload-btn:hover {
  background-color: #ecf5ff;
}

.upload-icon {
  display: inline-flex;
  margin-right: 5px;
  font-size: 16px;
}

.upload-text {
  font-size: 14px;
  white-space: nowrap;
}

.file-info {
  margin: 5px 0 15px;
  font-size: 12px;
  color: #909399;
}

.file-list {
  margin-top: 15px;
}

.file-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.file-name {
  margin: 0 10px;
}

.progress-area {
  flex: 1;
  display: flex;
  align-items: center;
}

.delete-btn {
  margin-left: 10px;
  padding: 0 4px !important;
  height: 22px !important;
  line-height: 22px !important;
  font-size: 12px !important;
}

.error-text {
  color: #F56C6C;
  margin-left: 5px;
}

.generating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.loading-circle {
  font-size: 50px;
  color: #409EFF;
  margin-bottom: 20px;
}

.generating-text {
  font-size: 16px;
  color: #606266;
}

.completed-container {
  margin-top: 10px;
}

.failed-container {
  margin-top: 10px;
}

.success-icon {
  color: #67C23A;
}

.error-icon {
  color: #F56C6C;
}
</style> 