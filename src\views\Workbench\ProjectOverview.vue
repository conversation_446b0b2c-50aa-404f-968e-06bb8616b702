<template>
  <div class="overview-section">
    <div class="block-container">
      <div class="block-header">项目总览</div>
      <div class="block-content">
        <div class="overview-content">
          <div class="overview-item">
            <div class="item-icon">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ statistics.totalProjects }}<span class="item-unit">个</span></div>
              <div class="item-label">在建项目总数</div>
            </div>
          </div>
          <div class="overview-item">
            <div class="item-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">¥{{ formatAmount(statistics.totalAmount) }}</div>
              <div class="item-label">合同总金额</div>
            </div>
          </div>
          <div class="overview-item">
            <div class="item-icon">
              <el-icon><Files /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ statistics.unStartedProjects }}<span class="item-unit">个</span></div>
              <div class="item-label">未开工项目总数</div>
            </div>
          </div>
          <div class="overview-item">
            <div class="item-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">¥{{ formatAmount(statistics.unStartedAmount) }}</div>
              <div class="item-label">未开工总金额</div>
            </div>
          </div>
          <div class="overview-item">
            <div class="item-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">¥{{ formatAmount(statistics.receivedAmount) }}</div>
              <div class="item-label">回款总金额</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { FolderOpened, Money, Files, Wallet, CreditCard } from '@element-plus/icons-vue';
import { getProjectOverview } from '@/api/project';
import { ElMessage } from 'element-plus';

const statistics = ref({
  totalProjects: 0,
  totalAmount: 0,
  unStartedProjects: 0,
  unStartedAmount: 0,
  receivedAmount: 0
});

// 格式化金额显示
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0';

  const num = parseFloat(amount);

  // 大于1亿，显示为 X.XX亿
  if (num >= 100000000) {
    return (num / 100000000).toFixed(2) + '亿';
  }
  // 大于1万，显示为 X.XX万
  else if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万';
  }
  // 小于1万，显示原数值，添加千分位分隔符
  else {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
};

const fetchOverviewData = async () => {
  try {
    const res = await getProjectOverview();
    if (res && (res.code === 200 || res.code === 0) && res.data) {
      // 兼容不同后端字段
      statistics.value.totalProjects = res.data.totalProjects ?? res.data.inProgressProjectCount ?? 0;
      statistics.value.totalAmount = res.data.totalAmount ?? res.data.totalContractAmount ?? 0;
      statistics.value.unStartedProjects = res.data.notStartedProjectCount ?? 0;
      statistics.value.unStartedAmount = res.data.notStartedTotalAmount ?? 0;
      statistics.value.receivedAmount = res.data.totalReceivedAmount ?? 0;
    } else {
      ElMessage.error(res?.msg || res?.message || '获取项目总览失败');
    }
  } catch (e) {
    ElMessage.error('获取项目总览失败: ' + (e.message || '网络错误'));
  }
};

onMounted(() => {
  fetchOverviewData();
});
</script>

<style scoped>
/* 项目总览区域 - 固定在顶部 */
.overview-section {
  margin-bottom: 8px; /* 减少底部间距 */
}

.block-container {
  background-color: #ffffff;
  border-radius: 6px; /* 减少圆角 */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06); /* 减少阴影 */
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.block-header {
  padding: 8px 16px; /* 减少上下内边距 */
  font-size: 15px; /* 减少字体大小 */
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.block-content {
  padding: 8px 16px; /* 减少内边距 */
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 项目总览内容区域优化 */
.overview-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 2px 0; /* 减少内边距 */
}

.overview-item {
  flex: 1;
  min-width: 160px; /* 减少最小宽度 */
  padding: 4px 8px; /* 减少内边距 */
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  border-radius: 6px; /* 减少圆角 */
  transition: all 0.3s ease;
}

/* 悬浮效果 - 减少移动距离和阴影 */
.overview-item:hover {
  background: linear-gradient(135deg, rgba(91, 123, 250, 0.03) 0%, rgba(91, 123, 250, 0.01) 100%);
  transform: translateY(-1px); /* 减少移动距离 */
  box-shadow: 0 4px 15px rgba(91, 123, 250, 0.1); /* 减少阴影 */
}

.overview-item:hover .item-icon {
  background-color: rgba(91, 123, 250, 0.12); /* 减少背景色强度 */
  transform: scale(1.02); /* 减少缩放比例 */
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.15); /* 减少阴影 */
}

.overview-item:hover .item-value {
  color: #5B7BFA;
  /* 移除缩放效果避免布局变化 */
}

.overview-item:hover .item-label {
  color: #606266;
}

.overview-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0; /* 恢复原有位置 */
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #ebeef5; /* 恢复简单背景色 */
}

/* 移除分割线的悬浮效果 */

.item-icon {
  font-size: 24px; /* 减少图标大小 */
  color: #5B7BFA;
  margin-right: 12px; /* 减少右边距 */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px; /* 减少宽度 */
  height: 40px; /* 减少高度 */
  background-color: rgba(91, 123, 250, 0.1);
  border-radius: 6px; /* 减少圆角 */
  transition: all 0.3s ease;
}

.item-info {
  display: flex;
  flex-direction: column;
}

.item-value {
  font-size: 22px; /* 减少字体大小 */
  color: #303133;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 2px; /* 减少底部间距 */
  transition: all 0.3s ease;
}

.item-unit {
  font-size: 12px; /* 减少字体大小 */
  font-weight: normal;
  margin-left: 2px;
  transition: color 0.3s ease;
}

.item-label {
  font-size: 12px; /* 减少字体大小 */
  color: #909399;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 移除脉冲动画效果 */

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .overview-item {
    min-width: 120px;
    padding: 10px 0;
  }

  .item-value {
    font-size: 20px;
  }
}

/* 保持简单的主题色设计 */
.overview-item:nth-child(2) .item-icon {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.overview-item:nth-child(3) .item-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.overview-item:nth-child(4) .item-icon {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.overview-item:nth-child(5) .item-icon {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

/* 悬浮时保持对应的主题色 */
.overview-item:nth-child(1):hover .item-value { color: #5B7BFA; }
.overview-item:nth-child(2):hover .item-value { color: #10b981; }
.overview-item:nth-child(3):hover .item-value { color: #f59e0b; }
.overview-item:nth-child(4):hover .item-value { color: #ef4444; }
.overview-item:nth-child(5):hover .item-value { color: #8b5cf6; }
</style> 