<template>
  <router-view />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  background-color: #e1e8fd;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #e1e8fd;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 修复 Element Plus 菜单样式 */
.el-menu {
  border-right: none !important;
}

.el-card {
  overflow: visible !important;
}

.el-card__body {
  height: 100%;
}

.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-y: auto !important;
}

/* 避免移动端上的灰色背景 */
@media (hover: none) {
  * {
    -webkit-tap-highlight-color: transparent;
  }
}
</style> 