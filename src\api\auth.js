import request from '@/utils/request'

// 用户名+工号登录，返回JWT
export function login(data) {
  return request({
    url: '/jwt/login',
    method: 'post',
    data
  })
}

// 获取JWT令牌
export function generateJWT(data) {
  return request({
    url: '/jwt/generate',
    method: 'post',
    data
  })
}

// 验证JWT令牌
export function verifyJWT(data) {
  return request({
    url: '/jwt/verify',
    method: 'post',
    data
  })
}

// 查询当前登录用户信息
export function getCurrentUser() {
  return request({
    url: '/users/me',
    method: 'get'
  })
}

// 外部token登录验证
export function validateExternalToken(token) {
  return request({
    url: '/jwt/validate-external',
    method: 'post',
    data: { token }
  })
}