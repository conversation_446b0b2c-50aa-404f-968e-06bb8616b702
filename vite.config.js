import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许局域网访问
    port: 3010,      // 默认端口改为3010
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://***************:8081/bp-dms/',         //服务器环境
        // target: 'http://*************:8081/bp-dms/',        //后端本地
        // target: 'http://************:8080/bp-dms/',         //跳转测试
        changeOrigin: true,
        timeout: 30000, // 30秒超时
        rewrite: (path) => path.replace(/^\/api/, '/api') // 保持/api前缀
      }
    }
  }
}) 