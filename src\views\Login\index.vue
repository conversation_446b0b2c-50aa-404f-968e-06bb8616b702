<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-container">
            <el-icon class="logo-icon" color="#5B7BFA" :size="40"><Briefcase /></el-icon>
          </div>
          <h2>项目管理系统</h2>
          <p>欢迎登录</p>
        </div>
        
        <div class="login-form">
          <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef">
            <el-form-item prop="username">
              <el-input
                ref="usernameInput"
                v-model="loginForm.username"
                placeholder="请输入用户名"
                :prefix-icon="User"
                size="large"
                clearable
                @keyup.enter="focusEmployeeId"
              />
            </el-form-item>

            <el-form-item prop="employeeId">
              <el-input
                ref="employeeIdInput"
                v-model="loginForm.employeeId"
                type="password"
                placeholder="请输入工号"
                :prefix-icon="Lock"
                size="large"
                clearable
                show-password
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            
            <div class="login-options">
              <el-checkbox v-model="rememberMe">记住我</el-checkbox>
              <el-button link type="primary" class="forget-password">忘记密码？</el-button>
            </div>
            
            <el-button 
              type="primary" 
              :loading="loading" 
              class="login-button" 
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form>
        </div>
        
        <div class="login-footer">
          <p>Copyright © 2025 项目管理系统</p>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="login-bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { User, Lock, Briefcase } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { login, getCurrentUser } from '@/api/auth';

const router = useRouter();
const userStore = useUserStore();
const loginFormRef = ref(null);
const usernameInput = ref(null);
const employeeIdInput = ref(null);
const loading = ref(false);
const rememberMe = ref(false);

// 登录表单
const loginForm = reactive({
  username: '',
  employeeId: ''
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  employeeId: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ]
};

// 记住密码相关常量
const REMEMBER_KEY = 'login_remember_info';
const REMEMBER_EXPIRE_DAYS = 30; // 记住信息保存30天

// 保存登录信息到localStorage
const saveLoginInfo = () => {
  if (rememberMe.value) {
    const expireTime = new Date().getTime() + (REMEMBER_EXPIRE_DAYS * 24 * 60 * 60 * 1000);
    const loginInfo = {
      username: loginForm.username,
      employeeId: loginForm.employeeId,
      rememberMe: true,
      expireTime: expireTime
    };
    localStorage.setItem(REMEMBER_KEY, JSON.stringify(loginInfo));
  } else {
    // 如果取消记住我，清除保存的信息
    localStorage.removeItem(REMEMBER_KEY);
  }
};

// 从localStorage恢复登录信息
const loadLoginInfo = () => {
  try {
    const savedInfo = localStorage.getItem(REMEMBER_KEY);
    if (savedInfo) {
      const loginInfo = JSON.parse(savedInfo);

      // 检查是否过期
      if (loginInfo.expireTime && new Date().getTime() > loginInfo.expireTime) {
        // 已过期，清除数据
        localStorage.removeItem(REMEMBER_KEY);
        return;
      }

      if (loginInfo.rememberMe) {
        loginForm.username = loginInfo.username || '';
        loginForm.employeeId = loginInfo.employeeId || '';
        rememberMe.value = true;
      }
    }
  } catch (error) {
    console.warn('恢复登录信息失败:', error);
    // 如果解析失败，清除可能损坏的数据
    localStorage.removeItem(REMEMBER_KEY);
  }
};

// 焦点跳转到工号输入框
const focusEmployeeId = () => {
  // 如果用户名为空，不进行跳转
  if (!loginForm.username.trim()) {
    return;
  }

  // 如果工号已有值，直接登录
  if (loginForm.employeeId.trim()) {
    handleLogin();
    return;
  }

  // 使用nextTick确保DOM更新完成后再聚焦到工号输入框
  nextTick(() => {
    if (employeeIdInput.value) {
      employeeIdInput.value.focus();
    }
  });
};

// 处理登录
const handleLogin = () => {
  loginFormRef.value.validate(async (valid) => {
    if (!valid) {
      return false;
    }
    
    loading.value = true;
    
    try {
      // 按接口文档直接传 username/number
      const loginData = {
        username: loginForm.username,
        number: loginForm.employeeId
      };
      
      const loginResult = await login(loginData);
      
      // 后端返回格式：{code: 200, msg: '操作成功', data: {Authorization: 'token', success: true}, success: true}
      if (loginResult.success && loginResult.data && loginResult.data.Authorization) {
        // 保存JWT令牌
        const token = loginResult.data.Authorization;
        userStore.setToken(token);
        
        // 获取当前用户信息
        try {
          const userInfoResponse = await getCurrentUser();
          const userInfo = userInfoResponse.data;

          const userInfoToStore = {
            userId: userInfo.userId,
            username: userInfo.username,
            account: userInfo.account,
            realName: userInfo.realName || userInfo.account || userInfo.username, // 优先使用realName
            phone: userInfo.phone,
            position: userInfo.position,
            area: userInfo.area,
            roles: userInfo.roles,
            roleId: userInfo.roles && userInfo.roles.length > 0 ? userInfo.roles[0] : null, // roles数组的第一个元素就是roleId
            permissions: userInfo.permissions // 这是页面权限，不是菜单权限
          };

          userStore.setUserInfo(userInfoToStore);
        } catch (userError) {
          console.error('获取用户信息失败:', userError);

        }

        // 保存登录信息（如果勾选了记住我）
        saveLoginInfo();

        ElMessage.success('登录成功');
        router.push('/workbench');
      } else {
        ElMessage.error(loginResult.msg || loginResult.message || '登录失败：用户名或工号错误');
      }
    } catch (error) {
      ElMessage.error(error.message || '登录失败，请检查网络连接或联系管理员');
    } finally {
      loading.value = false;
    }
  });
};

// 监听记住我状态变化
watch(rememberMe, (newValue) => {
  if (!newValue) {
    // 如果取消勾选记住我，立即清除保存的信息
    localStorage.removeItem(REMEMBER_KEY);
  }
});

// 组件挂载时恢复登录信息并聚焦到用户名输入框
onMounted(() => {
  loadLoginInfo();

  // 自动聚焦到用户名输入框
  nextTick(() => {
    if (usernameInput.value) {
      usernameInput.value.focus();
    }
  });
});
</script>

<style scoped>
.login-container {
  position: relative;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #e1e8fd 0%, #c9d4f8 100%);
  overflow: hidden;
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card {
  width: 420px;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(91, 123, 250, 0.18);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-icon {
  padding: 16px;
  background-color: rgba(91, 123, 250, 0.15);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(91, 123, 250, 0.2);
}

.login-header h2 {
  font-size: 26px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.login-header p {
  color: #909399;
  font-size: 16px;
}

.login-form {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forget-password {
  font-size: 14px;
  padding: 0;
}

.login-button {
  width: 100%;
  height: 46px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
  margin-top: 10px;
  transition: all 0.3s;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(91, 123, 250, 0.4);
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: #909399;
  font-size: 12px;
}

/* 输入框样式优化 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  padding: 0 15px;
  height: 46px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #5B7BFA inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(91, 123, 250, 0.3) inset;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 移除自定义loading样式，使用Element Plus内置的loading效果 */

/* 记住我复选框 */
:deep(.el-checkbox__inner) {
  border-radius: 4px;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #5B7BFA;
  border-color: #5B7BFA;
}

/* 背景装饰 */
.login-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.1), rgba(91, 123, 250, 0.3));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 400px;
  height: 400px;
  bottom: -150px;
  right: -150px;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.1), rgba(91, 123, 250, 0.2));
  animation-delay: 2s;
}

.circle-3 {
  width: 200px;
  height: 200px;
  top: 40%;
  right: 10%;
  background: linear-gradient(45deg, rgba(91, 123, 250, 0.05), rgba(91, 123, 250, 0.15));
  animation-delay: 4s;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 登录卡片进入动画 */
.login-card {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单项依次出现动画 */
:deep(.el-form-item) {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

:deep(.el-form-item:nth-child(1)) {
  animation-delay: 0.2s;
}

:deep(.el-form-item:nth-child(2)) {
  animation-delay: 0.4s;
}

.login-options {
  animation: fadeInUp 0.6s ease-out 0.6s forwards;
  opacity: 0;
}

.login-button {
  animation: fadeInUp 0.6s ease-out 0.8s forwards;
  opacity: 0;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 