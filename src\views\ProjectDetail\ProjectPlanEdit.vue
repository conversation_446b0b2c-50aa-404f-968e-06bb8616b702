<template>
  <div class="tab-content">
    <div class="block-container">
      <div class="project-plan-edit">
        <div class="plan-header">
          <h3><el-icon><Calendar /></el-icon> 计划版本变更</h3>
          <div class="header-buttons">
            <div class="toolbar-group">
              <button class="toolbar-btn" @click="downloadTemplate" title="请严格按照模版格式填写计划">
                <i class="fas fa-file-download"></i> 模版下载
              </button>
              <button class="toolbar-btn" @click="importExcel" title="仅预审核阶段的项目可以导入计划">
                <i class="fas fa-file-import"></i> 导入Excel
              </button>
            </div>
            <div class="toolbar-group">
              <button class="toolbar-btn" @click="refreshData" title="刷新数据">
                <i class="fas fa-sync-alt"></i> 刷新
              </button>
              <button class="toolbar-btn" @click="expandAllRows">
                <i class="fas fa-expand-alt"></i> 全部展开
              </button>
              <button class="toolbar-btn" @click="collapseAllRows">
                <i class="fas fa-compress-alt"></i> 全部收起
              </button>
            </div>
            <div class="toolbar-group">
              <button class="toolbar-btn cancel-btn" @click="cancelEdit">
                <i class="fas fa-times"></i> 取消
              </button>
              <button class="toolbar-btn submit-btn" @click="saveEdit" :disabled="saveLoading">
                <i class="fas fa-check" v-if="!saveLoading"></i>
                <i class="fas fa-spinner fa-spin" v-if="saveLoading"></i>
                {{ saveLoading ? '提交中...' : '提交' }}
              </button>
            </div>
          </div>
        </div>

        <div class="table-wrapper">
          <el-table
              ref="planEditTableRef"
              :data="planDataEdit"
              border
              row-key="id"
              :default-expand-all="false"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              table-layout="fixed"
              class="plan-table rounded-table"
              :stripe="false"
              :highlight-current-row="true"
              :row-class-name="tableRowClassName"
              :max-height="maxHeight"
              :expand-icon="tableProps.expandIcon"
              :cell-style="{padding: '0px', height: '32px'}"
              :header-cell-style="{padding: '0px', height: '32px'}"
              style="--el-table-header-row-height: 32px; --el-table-row-height: 32px; --el-table-border-width: 1px;"
              v-loading="loading"
              element-loading-text="加载计划数据中..."
              @expand-change="handleExpandChange"
              >
            <el-table-column
              label="序号"
              :width="serialColumnWidth"
              align="left"
              class-name="serial-column"
              fixed="left">
              <template #default="scope">
                <div class="serial-cell">
                  <el-tag v-if="scope.row.isDeleted" type="danger" size="small" class="delete-tag">删除</el-tag>
                  <el-tag v-else-if="scope.row.isNewItem" type="success" size="small" class="new-tag">新增</el-tag>
                  <span
                    v-if="scope.row.serialNumber && scope.row.serialNumber.trim() !== ''"
                    class="serial-number-text"
                    :style="getSerialNumberStyle(scope.row)"
                    :class="{ 'deleted-text': scope.row.isDeleted }"
                  >
                    {{ scope.row.serialNumber }}
                  </span>
                  <span v-else class="stage-indicator">
                    ●
                  </span>
                </div>
              </template>
            </el-table-column>

            <!-- 工作项名称列 -->
            <el-table-column prop="taskName" label="工作项名称" :width="taskNameColumnWidth" align="left" show-overflow-tooltip fixed="left">
              <template #default="scope">
                <div
                  class="editable-cell task-name-cell"
                  @dblclick="handleEdit(scope.row, 'taskName')"
                >
                  <el-input
                    v-if="scope.row.editing === 'taskName'"
                    v-model="scope.row.taskName"
                    size="small"
                    @blur="handleSave(scope.row, 'taskName')"
                    @keyup.enter="handleSave(scope.row, 'taskName')"
                    ref="taskNameInput"
                  />
                  <span v-else class="task-name-text" :style="getTaskNameStyle(scope.row)">
                    <el-tag v-if="scope.row.isNewItem" type="success" size="small" class="new-tag">新增</el-tag>
                    {{ scope.row.taskName }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <!-- 前置工作项列 -->
            <el-table-column prop="parentTask" label="前置工作项" width="95" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div
                  class="editable-cell"
                  @dblclick="handleEdit(scope.row, 'parentTask')"
                >
                  <el-input
                    v-if="scope.row.editing === 'parentTask'"
                    v-model="scope.row.parentTask"
                    size="small"
                    @blur="handleSave(scope.row, 'parentTask')"
                    @keyup.enter="handleSave(scope.row, 'parentTask')"
                    ref="parentTaskInput"
                  />
                  <div v-else class="pretask-content" style="line-height: 16px;">
                    <template v-if="scope.row.parentTask">
                      <span
                        v-for="(item, index) in scope.row.parentTask.split('、')"
                        :key="index"
                        class="pretask-number"
                        @click.stop="handlePretaskClick(item)"
                      >
                        {{ item }}{{ index < scope.row.parentTask.split('、').length - 1 ? '、' : '' }}
                      </span>
                    </template>
                    <template v-else>
                      {{ scope.row.parentTask }}
                    </template>
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 责任人列 -->
            <el-table-column prop="responsible" label="责任人" width="120" align="center" show-overflow-tooltip>
              <template #default="scope">
                <el-select
                  v-model="scope.row.responsible"
                  @change="handleResponsibleChange(scope.row)"
                  placeholder="选择责任人"
                  size="small"
                  style="width: 100%"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 执行人列 -->
            <el-table-column prop="executorName" label="执行人" width="120" align="center" show-overflow-tooltip>
              <template #default="scope">
                <el-select
                  v-model="scope.row.executorName"
                  @change="handleExecutorChange(scope.row)"
                  placeholder="选择执行人"
                  size="small"
                  style="width: 100%"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.value"
                    :label="user.label"
                    :value="user.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 状态列 -->
            <el-table-column prop="status" label="工作项状态" width="95" align="center" class-name="status-column">
              <template #default="{ row }">
                <el-select v-model="row.status" placeholder="请选择状态" size="small" style="width: 90px; height: 24px; line-height: 24px;">
                  <el-option
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="计划开始时间" width="105" align="center" show-overflow-tooltip>
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.planStartDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="small"
                  style="width: 95px; height: 24px; line-height: 24px;"
                  @change="handleDateChange(scope.row, 'planStartDate')"
                />
              </template>
            </el-table-column>

            <el-table-column label="计划完成时间" width="105" align="center" show-overflow-tooltip>
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.planEndDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="small"
                  style="width: 95px; height: 24px; line-height: 24px;"
                  @change="handleDateChange(scope.row, 'planEndDate')"
                />
              </template>
            </el-table-column>

            <el-table-column label="计划工期" width="80" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div class="duration-cell">{{ calculateDuration(scope.row.planStartDate, scope.row.planEndDate) }}</div>
              </template>
            </el-table-column>

            <el-table-column label="备注" width="60" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div class="editable-cell" style="justify-content: center;">
                  <el-input
                    v-if="scope.row.editing === 'note'"
                    v-model="scope.row.note"
                    size="small"
                    class="edit-input note-input"
                    placeholder="请输入备注"
                    @blur="handleSave(scope.row, 'note')"
                    @keyup.enter="handleSave(scope.row, 'note')"
                    ref="noteInput"
                  />
                  <span
                    v-else
                    class="editable-text note-text"
                    @click="handleEdit(scope.row, 'note')"
                    :title="scope.row.note || '点击编辑备注'"
                  >
                    {{ scope.row.note || '点击编辑' }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="实际开始时间" width="105" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div class="readonly-date">{{ scope.row.actualStartDate || '--' }}</div>
              </template>
            </el-table-column>

            <el-table-column label="实际完成时间" width="105" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div class="readonly-date">{{ scope.row.actualEndDate || '--' }}</div>
              </template>
            </el-table-column>

            <el-table-column label="实际工期" width="80" align="center" show-overflow-tooltip>
              <template #default="scope">
                <div class="duration-cell">{{ calculateDuration(scope.row.actualStartDate, scope.row.actualEndDate) }}</div>
              </template>
            </el-table-column>

            <!-- 变更说明列 -->
            <el-table-column label="变更说明" width="150" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="editable-cell" style="justify-content: flex-start;">
                  <el-input
                    v-if="scope.row.editing === 'changeRemark'"
                    v-model="scope.row.changeRemark"
                    size="small"
                    class="edit-input change-remark-input"
                    placeholder="请输入变更说明"
                    @blur="handleSave(scope.row, 'changeRemark')"
                    @keyup.enter="handleSave(scope.row, 'changeRemark')"
                    ref="changeRemarkInput"
                  />
                  <span
                    v-else
                    class="editable-text change-remark-text"
                    @click="handleEdit(scope.row, 'changeRemark')"
                    :title="scope.row.changeRemark || '点击编辑变更说明'"
                  >
                    {{ scope.row.changeRemark || '点击编辑' }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <!-- 审核意见列 -->
            <el-table-column label="审核意见" width="150" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="review-opinion-cell">
                  {{ scope.row.reviewOpinion || '--' }}
                </div>
              </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column label="操作" width="170" align="center" fixed="right">
              <template #default="{ row, $index }">
                <div class="operation-column">
                  <template v-if="row.isDeleted">
                    <el-button type="warning" link size="small" class="op-btn" @click="restoreTask(row)" style="height: 16px; padding: 0 2px; font-size: 12px;">
                      <el-icon><RefreshLeft /></el-icon>恢复
                    </el-button>
                  </template>
                  <template v-else>
                    <!-- 只有有序号的节点才能添加子项和删除 -->
                    <template v-if="row.serialNumber && row.serialNumber.trim() !== ''">
                      <el-button type="primary" link size="small" class="op-btn" @click="addSubTask(row)" style="height: 16px; padding: 0 2px; font-size: 12px;">
                        <el-icon><circle-plus /></el-icon>添加子项
                      </el-button>
                      <el-button type="danger" link size="small" class="op-btn" @click="deleteTask(row, $index)" style="height: 16px; padding: 0 2px; font-size: 12px;">
                        <el-icon><Delete /></el-icon>删除
                      </el-button>
                    </template>
                    <!-- 无序号的顶级节点显示禁用状态的图标 -->
                    <template v-else>
                      <el-tooltip content="阶段数据不可删除" placement="top">
                        <el-icon class="disabled-operation-icon">
                          <Lock />
                        </el-icon>
                      </el-tooltip>
                    </template>
                  </template>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, reactive, h, defineComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ElIcon } from 'element-plus';
import {
  CaretRight,
  Calendar,
  Check,
  Close,
  Plus,
  Minus,
  Delete,
  RefreshLeft,
  FolderOpened,
  Lock
} from '@element-plus/icons-vue';
// 深拷贝函数
const cloneDeep = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => cloneDeep(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = cloneDeep(obj[key]);
      }
    }
    return clonedObj;
  }
};
import { useRoute, useRouter } from 'vue-router';
import {
  getProjectWorkItemDraftTree,
  updateWorkItemDraft,
  createWorkItemDraft,
  createWorkItemDraftMock,
  deleteWorkItemDraft,
  deleteWorkItemDraftMock,
  submitProjectWorkItemReview,
  clearChangeMarks,
  batchUpdateWorkItemDraft,
  generatePlanId,
  generatePlanIds,
  getProjectWorkItemReviewList,
  importFromExcel
} from '@/api/projectPlanDraft';
import { getStakeholderListAll } from '@/api/stakeholder';
import { getUserList } from '@/api/user';
import { getPlanStatusEnum, getPlanStatusEnumMock, getProjectPlanTree } from '@/api/projectPlan';
// 移除debounce导入，不再使用自动保存

const route = useRoute();
const router = useRouter();

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  maxHeight: {
    type: [String, Number],
    default: 'calc(100vh - 245px)'
  }
});

// 从route.params获取项目ID
const projectId = route.params.projectId;
// 获取合同号，优先从route.query获取，如果没有则从sessionStorage获取
const contractNo = route.query.contractNo || sessionStorage.getItem('currentContractNo');

// 数据状态
const planDataOrigin = ref([]); // 正式表数据

// 创建当前计划数据的深拷贝用于编辑
const planDataEdit = ref([]);
const loading = ref(false);

// 保存状态
const saveLoading = ref(false);

// 加载正式表数据
const loadFormalData = async () => {
  try {
    const response = await getProjectPlanTree(projectId);
    if (response && response.data) {
      planDataOrigin.value = convertApiDataToTableFormat(response.data);
      return true;
    }
    return false;
  } catch (error) {
    console.error('获取正式表数据失败:', error);
    return false;
  }
};

// 移除复制草稿表的逻辑，不再需要

// 获取草稿数据
const loadDraftData = async () => {
  if (!projectId) {
    ElMessage.error('项目ID不能为空');
    return;
  }

  loading.value = true;
  try {
    // 只获取草稿数据
    const draftResponse = await getProjectWorkItemDraftTree(projectId);

    if (draftResponse && draftResponse.data && draftResponse.data.length > 0) {
      // 有草稿数据，直接使用
      planDataEdit.value = convertApiDataToTableFormat(draftResponse.data);
    } else {
      // 没有草稿数据，初始化为空数组
      planDataEdit.value = [];
      ElMessage.info('当前项目暂无草稿数据');
    }

    // 更新序号列宽度
    serialColumnWidth.value = calculateSerialColumnWidth(planDataEdit.value);
    // 更新工作项名称列宽度
    taskNameColumnWidth.value = calculateTaskNameColumnWidth(planDataEdit.value);
  } catch (error) {
    console.error('获取草稿数据失败:', error);
    ElMessage.error('获取草稿数据失败: ' + error.message);
    planDataEdit.value = [];
  } finally {
    loading.value = false;
  }
};

// 转换API数据格式为前端表格格式
const convertApiDataToTableFormat = (apiData) => {
  const convertItem = (item, index = 0, parentSerialNum = '', isTopLevel = false) => {
    // 使用后端返回的seriNum作为序号
    // 如果seriNum为null或空，则不显示序号（阶段类型）
    let serialNumber = '';
    if (item.seriNum !== null && item.seriNum !== undefined && item.seriNum !== '') {
      serialNumber = item.seriNum;
    } else {
      // 对于阶段类型（seriNum为null），不显示序号
      serialNumber = '';
    }

    const uniqueId = `plan-edit-${serialNumber ? serialNumber.replace(/\./g, '-') : 'no-serial'}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

    return {
      id: uniqueId,
      originalId: item.id, // 保存原始ID用于API调用
      taskName: item.planName,
      parentTask: item.preSeriNum || '',
      responsible: item.ownerName || '',
      executorName: item.executorName || '',
      status: String(item.planStatus || '0'),
      planStartDate: item.planStartTime ? item.planStartTime.split(' ')[0] : '',
      planEndDate: item.planEndTime ? item.planEndTime.split(' ')[0] : '',
      planDuration: item.planDuration || '',
      note: '', // 备注字段，用于用户添加备注
      changeRemark: item.changeRemark || '', // 变更说明字段
      actualStartDate: item.actualStartTime ? item.actualStartTime.split(' ')[0] : '',
      actualEndDate: item.actualEndTime ? item.actualEndTime.split(' ')[0] : '',
      actualDuration: item.actualDuration || '',
      reviewOpinion: item.reviewOpinion || '', // 审核意见字段
      level: getItemLevel(serialNumber),
      serialNumber: serialNumber,
      hasChildren: item.children && item.children.length > 0,
      children: item.children ? item.children.map((child, childIndex) => convertItem(child, childIndex, serialNumber, false)) : [],
      // 保存原始API数据用于提交
      _originalData: item
    };
  };

  // 如果是顶层数据且只有一个项目，直接处理其children（跳过项目级别）
  if (Array.isArray(apiData) && apiData.length === 1 && apiData[0].children) {
    return apiData[0].children.map((item, index) => convertItem(item, index, '', true));
  }

  const result = apiData.map((item, index) => convertItem(item, index, '', true));
  return result;
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '未开始',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期',
    '4': '逾期完成'
  };
  return statusMap[status] || '未开始';
};

// 获取项目层级
const getItemLevel = (seriNum) => {
  if (!seriNum || seriNum === '') return 0; // 阶段类型返回0层级
  // 层级 = 点号数量，顶级节点（如"1"）为0级，二级节点（如"1.1"）为1级
  const dotCount = (seriNum.match(/\./g) || []).length;
  return dotCount;
};

// 表格引用
const planEditTableRef = ref(null);

// 序号列宽度
const serialColumnWidth = ref(120);

// 工作项名称列宽度
const taskNameColumnWidth = ref(250);

// 创建对编辑的引用
const taskNameInput = ref(null);
const parentTaskInput = ref(null);
const responsibleInput = ref(null);
const noteInput = ref(null);
const changeRemarkInput = ref(null);

// 用户选项数据
const userOptions = ref([
  { label: '危慧慧', value: '45' }, // 默认用户，防止API失败时有数据可用
]);

// 状态枚举数据
const statusOptions = ref([]);

// 自定义展开图标组件
const CustomExpandIcon = defineComponent({
  props: {
    expanded: Boolean,
  },
  setup(props) {
    return () => h(
      'div',
      {
        class: [
          'custom-triangle-icon',
          props.expanded ? 'expanded' : '',
        ],
        style: {
          display: 'inline-block',
          verticalAlign: 'middle'
        }
      }
    );
  }
});

// 表格配置
const tableProps = reactive({
  expandIcon: ({ expanded }) => h(CustomExpandIcon, { expanded })
});

// 行样式设置
function tableRowClassName({ row }) {
  const hasChildren = row.hasChildren ? 'has-children' : 'no-children';
  const isDeleted = row.isDeleted ? 'deleted-row' : '';
  const isNew = row.isNewItem ? 'new-item' : '';
  const level = row.level || 0;
  const levelClass = `level-${level}`;
  return `tree-row tree-row-${row.id} ${hasChildren} ${isDeleted} ${isNew} ${levelClass}`.trim();
}

// 处理展开/收起状态变化
const handleExpandChange = (row, expanded) => {
  // 展开收起事件处理
};

// 计算序号列宽度
const calculateSerialColumnWidth = (data) => {
  if (!data || data.length === 0) return 120;

  let maxLength = 0;

  // 递归查找最长的序号
  const findMaxSerialLength = (items) => {
    items.forEach(item => {
      if (item.serialNumber) {
        maxLength = Math.max(maxLength, item.serialNumber.length);
      }
      if (item.children && item.children.length > 0) {
        findMaxSerialLength(item.children);
      }
    });
  };

  findMaxSerialLength(data);

  // 基础宽度 + 每个字符的宽度 + 展开图标宽度 + 内边距
  // 中文字符约14px，数字和点约8px，展开图标约20px，内边距约20px
  const baseWidth = 60; // 基础宽度
  const charWidth = 12; // 平均字符宽度
  const expandIconWidth = 20; // 展开图标宽度
  const padding = 20; // 内边距

  const calculatedWidth = baseWidth + (maxLength * charWidth) + expandIconWidth + padding;

  // 设置最小宽度120px，最大宽度220px
  const finalWidth = Math.max(120, Math.min(220, calculatedWidth));
  return finalWidth;
};

// 计算工作项名称列宽度
const calculateTaskNameColumnWidth = (data) => {
  if (!data || data.length === 0) return 250;

  let maxLength = 0;
  let maxLevel = 0;

  // 递归查找最长的工作项名称和最大层级
  const findMaxTaskNameLength = (items, level = 0) => {
    items.forEach(item => {
      if (item.taskName) {
        // 计算实际显示长度（考虑中文字符）
        const displayLength = getStringDisplayLength(item.taskName);
        maxLength = Math.max(maxLength, displayLength);
        maxLevel = Math.max(maxLevel, level);
      }
      if (item.children && item.children.length > 0) {
        findMaxTaskNameLength(item.children, level + 1);
      }
    });
  };

  findMaxTaskNameLength(data);

  // 基础宽度 + 字符宽度 + 层级缩进 + 内边距
  const baseWidth = 100; // 基础宽度
  const charWidth = 14; // 中文字符宽度约14px
  const levelIndent = maxLevel * 20; // 每级缩进20px（计划变更页面）
  const padding = 40; // 内边距（包括编辑按钮空间）

  const calculatedWidth = baseWidth + (maxLength * charWidth) + levelIndent + padding;

  // 设置最小宽度200px，最大宽度450px
  const finalWidth = Math.max(200, Math.min(450, calculatedWidth));
  return finalWidth;
};

// 计算字符串显示长度（中文字符算1，英文字符算0.5）
const getStringDisplayLength = (str) => {
  if (!str) return 0;
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i);
    // 判断是否为中文字符
    if (/[\u4e00-\u9fa5]/.test(char)) {
      length += 1; // 中文字符
    } else {
      length += 0.6; // 英文字符和数字
    }
  }
  return Math.ceil(length);
};

// 检查行是否有真正的子项
const hasRealChildren = (row) => {
  return row.children && Array.isArray(row.children) && row.children.length > 0;
};

// 递归展开所有行
const expandAllRowsRecursive = (data) => {
  data.forEach(row => {
    if (hasRealChildren(row)) {
      planEditTableRef.value.toggleRowExpansion(row, true);
      if (row.children && row.children.length > 0) {
        expandAllRowsRecursive(row.children);
      }
    }
  });
};

// 递归收起所有行
const collapseAllRowsRecursive = (data) => {
  data.forEach(row => {
    if (hasRealChildren(row)) {
      planEditTableRef.value.toggleRowExpansion(row, false);
      if (row.children && row.children.length > 0) {
        collapseAllRowsRecursive(row.children);
      }
    }
  });
};

// 展开所有行
const expandAllRows = () => {
  if (planEditTableRef.value && planDataEdit.value && planDataEdit.value.length > 0) {
    nextTick(() => {
      expandAllRowsRecursive(planDataEdit.value);
    });
  }
};

// 收起所有行
const collapseAllRows = () => {
  if (planEditTableRef.value && planDataEdit.value && planDataEdit.value.length > 0) {
    nextTick(() => {
      collapseAllRowsRecursive(planDataEdit.value);
    });
  }
};

// 刷新数据
const refreshData = async () => {
  try {
    ElMessage.info('正在刷新数据...');
    await loadDraftData();
    ElMessage.success('数据刷新成功');
  } catch (error) {
    console.error('刷新数据失败:', error);
    ElMessage.error('数据刷新失败，请重试');
  }
};

// 下载模版
const downloadTemplate = () => {
  try {
    // 模板文件URL
    const templateUrl = 'https://miniotest.maxvision.cn:9801/pfile/accesscontrol/biz/2025/08/15/项目计划模板.xlsx';

    // 创建一个隐藏的下载链接
    const link = document.createElement('a');
    link.href = templateUrl;
    link.download = '项目计划模板.xlsx'; // 设置下载文件名
    link.target = '_blank'; // 在新窗口打开，避免跨域问题
    link.style.display = 'none';

    // 添加到DOM并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理DOM
    document.body.removeChild(link);

    ElMessage.success('下载成功，请编辑后导入真实计划');
  } catch (error) {
    console.error('模板下载失败:', error);
    ElMessage.error('模板下载失败，请重试');
  }
};

// 导入Excel
const importExcel = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.style.display = 'none';

  input.onchange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel' // .xls
    ];

    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('请选择Excel文件（.xlsx或.xls格式）');
      return;
    }

    // 验证文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB');
      return;
    }

    try {
      // 显示确认对话框
      await ElMessageBox.confirm(
        '导入Excel将会覆盖项目下原有的草稿数据，是否继续？',
        '确认导入',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 开始导入
      const loading = ElMessage({
        message: '正在导入Excel文件，请稍候...',
        type: 'info',
        duration: 0 // 不自动关闭
      });

      try {
        const response = await importFromExcel(props.projectId, file);
        loading.close();

        if (response && response.data) {
          const result = response.data;

          if (result.success) {
            // 导入成功
            const message = `导入成功！共处理 ${result.processedRows} 行数据`;
            ElMessage.success(message);

            // 重新加载数据
            await loadDraftData();
          } else {
            // 导入失败
            ElMessage.error(result.errorMessage || '导入失败');
          }
        } else {
          ElMessage.error('导入响应数据格式错误');
        }
      } catch (error) {
        loading.close();
        console.error('Excel导入失败:', error);
        ElMessage.error(error.message || '导入失败，请重试');
      }
    } catch {
      // 用户取消了导入
    }

    // 清理文件输入
    document.body.removeChild(input);
  };

  // 添加到DOM并触发点击
  document.body.appendChild(input);
  input.click();
};



// 处理编辑
const handleEdit = (row, field) => {
  // 设置当前编辑的字段
  row.editing = field;

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    // 根据不同字段聚焦对应的输入框
    if (field === 'taskName' && taskNameInput.value) {
      taskNameInput.value.focus();
    } else if (field === 'parentTask' && parentTaskInput.value) {
      parentTaskInput.value.focus();
    } else if (field === 'responsible' && responsibleInput.value) {
      responsibleInput.value.focus();
    } else if (field === 'note' && noteInput.value) {
      noteInput.value.focus();
    } else if (field === 'changeRemark' && changeRemarkInput.value) {
      changeRemarkInput.value.focus();
    }
  });
};

// 保存编辑后的值 - 仅清除编辑状态，不自动保存
const handleSave = async (row, field) => {
  // 清除编辑状态
  row.editing = '';

  // 不再自动保存，只在用户点击提交按钮时统一保存
};

// 处理日期变更 - 仅记录变更，不自动保存
const handleDateChange = async (row, field) => {
  // 不再自动保存，只在用户点击提交按钮时统一保存
};

// 处理责任人变更 - 仅记录变更，不自动保存
const handleResponsibleChange = async (row) => {
  // 不再自动保存，只在用户点击提交按钮时统一保存
};

// 处理执行人变更 - 仅记录变更，不自动保存
const handleExecutorChange = async (row) => {
  // 不再自动保存，只在用户点击提交按钮时统一保存
};

// 处理状态变更 - 仅记录变更，不自动保存
const handleStatusChange = async (row) => {
  // 不再自动保存，只在用户点击提交按钮时统一保存
};

// 计算日期之间的天数
const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return '';

  const start = new Date(startDate);
  const end = new Date(endDate);

  // 检查日期是否有效
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return '';

  // 计算天数差
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 因为包括首尾两天

  return diffDays.toString();
};

// 生成新任务的唯一ID
const generateUniqueId = (serialNumber = '') => {
  const serialPart = serialNumber ? serialNumber.replace(/\./g, '-') : 'no-serial';
  return `plan-new-${serialPart}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
};

// 智能生成子任务序号
const generateChildSerialNumber = (parentRow, allData) => {
  const parentSerialNumber = parentRow.serialNumber;

  // 如果父任务有序号，直接基于父序号生成
  if (parentSerialNumber && parentSerialNumber.trim() !== '') {
    const childrenCount = parentRow.children.length;
    return `${parentSerialNumber}.${childrenCount + 1}`;
  }

  // 如果父任务没有序号，需要智能分析
  // 1. 查找父任务的兄弟节点，看是否有序号模式
  const parentLevel = parentRow.level || 0;
  const siblingNumbers = findSiblingSerialNumbers(parentRow, allData, parentLevel);

  if (siblingNumbers.length > 0) {
    // 2. 基于兄弟节点的序号模式，为父任务生成序号
    const suggestedParentSerial = generateSerialForParent(siblingNumbers, parentRow, allData);

    if (suggestedParentSerial) {
      // 更新父任务的序号
      parentRow.serialNumber = suggestedParentSerial;
      // 基于新的父序号生成子序号
      const childrenCount = parentRow.children.length;
      return `${suggestedParentSerial}.${childrenCount + 1}`;
    }
  }

  // 3. 如果无法确定父序号，查找子任务的兄弟节点模式
  const childLevel = parentLevel + 1;
  const childSiblingNumbers = findChildSiblingNumbers(parentRow, allData, childLevel);

  if (childSiblingNumbers.length > 0) {
    // 基于子任务兄弟节点生成序号
    return generateNextSerialInSequence(childSiblingNumbers);
  }

  // 4. 最后的备选方案：生成一个基础序号
  return generateFallbackSerial(parentRow, allData);
};

// 查找兄弟节点的序号
const findSiblingSerialNumbers = (targetRow, allData, level) => {
  const siblings = [];

  const findSiblingsRecursive = (data, currentLevel) => {
    data.forEach(item => {
      if (currentLevel === level && item.id !== targetRow.id) {
        if (item.serialNumber && item.serialNumber.trim() !== '') {
          siblings.push(item.serialNumber);
        }
      }
      if (item.children && item.children.length > 0) {
        findSiblingsRecursive(item.children, currentLevel + 1);
      }
    });
  };

  findSiblingsRecursive(allData, 0);
  return siblings.sort();
};

// 查找子任务兄弟节点的序号
const findChildSiblingNumbers = (parentRow, allData, childLevel) => {
  const childSiblings = [];

  if (parentRow.children && parentRow.children.length > 0) {
    parentRow.children.forEach(child => {
      if (child.serialNumber && child.serialNumber.trim() !== '') {
        childSiblings.push(child.serialNumber);
      }
    });
  }

  return childSiblings.sort();
};

// 为父任务生成序号
const generateSerialForParent = (siblingNumbers, parentRow, allData) => {
  if (siblingNumbers.length === 0) return null;

  // 分析兄弟节点的序号模式
  const pattern = analyzeSerialPattern(siblingNumbers);

  if (pattern.type === 'numeric') {
    // 数字序号模式 (1, 2, 3...)
    const maxNumber = Math.max(...pattern.numbers);
    return (maxNumber + 1).toString();
  } else if (pattern.type === 'decimal') {
    // 小数序号模式 (1.1, 1.2, 1.3...)
    const baseNumber = pattern.baseNumber;
    const maxSubNumber = Math.max(...pattern.subNumbers);
    return `${baseNumber}.${maxSubNumber + 1}`;
  }

  return null;
};

// 在序列中生成下一个序号
const generateNextSerialInSequence = (existingNumbers) => {
  if (existingNumbers.length === 0) return '1';

  const pattern = analyzeSerialPattern(existingNumbers);

  if (pattern.type === 'numeric') {
    const maxNumber = Math.max(...pattern.numbers);
    return (maxNumber + 1).toString();
  } else if (pattern.type === 'decimal') {
    const baseNumber = pattern.baseNumber;
    const maxSubNumber = Math.max(...pattern.subNumbers);
    return `${baseNumber}.${maxSubNumber + 1}`;
  }

  return (existingNumbers.length + 1).toString();
};

// 分析序号模式
const analyzeSerialPattern = (serialNumbers) => {
  const numbers = [];
  const decimals = [];

  serialNumbers.forEach(serial => {
    if (serial.includes('.')) {
      const parts = serial.split('.');
      if (parts.length === 2) {
        const base = parseInt(parts[0]);
        const sub = parseInt(parts[1]);
        if (!isNaN(base) && !isNaN(sub)) {
          decimals.push({ base, sub, original: serial });
        }
      }
    } else {
      const num = parseInt(serial);
      if (!isNaN(num)) {
        numbers.push(num);
      }
    }
  });

  if (decimals.length > 0) {
    // 小数模式
    const baseNumbers = [...new Set(decimals.map(d => d.base))];
    if (baseNumbers.length === 1) {
      return {
        type: 'decimal',
        baseNumber: baseNumbers[0],
        subNumbers: decimals.map(d => d.sub)
      };
    }
  }

  if (numbers.length > 0) {
    // 数字模式
    return {
      type: 'numeric',
      numbers: numbers
    };
  }

  return { type: 'unknown' };
};

// 生成备选序号
const generateFallbackSerial = (parentRow, allData) => {
  // 查找整个数据中的最大序号
  const allSerials = [];

  const collectAllSerials = (data) => {
    data.forEach(item => {
      if (item.serialNumber && item.serialNumber.trim() !== '') {
        allSerials.push(item.serialNumber);
      }
      if (item.children && item.children.length > 0) {
        collectAllSerials(item.children);
      }
    });
  };

  collectAllSerials(allData);

  if (allSerials.length === 0) {
    return '1.1'; // 如果没有任何序号，从1.1开始
  }

  // 找到最大的顶级序号
  const topLevelSerials = allSerials.filter(s => !s.includes('.') || s.split('.').length === 2);
  const pattern = analyzeSerialPattern(topLevelSerials);

  if (pattern.type === 'numeric') {
    const maxNumber = Math.max(...pattern.numbers);
    return `${maxNumber + 1}.1`;
  } else if (pattern.type === 'decimal') {
    const maxBase = Math.max(...topLevelSerials.map(s => parseInt(s.split('.')[0])).filter(n => !isNaN(n)));
    return `${maxBase + 1}.1`;
  }

  return '1.1';
};

// 确保父任务有正确的ID用于父子关系
const ensureParentHasId = async (parentRow) => {
  // 如果父任务已经有真实的ID（originalId），直接返回
  if (parentRow.originalId) {
    return;
  }

  // 如果父任务是新增项但还没有预生成的ID，为其生成一个ID
  if (parentRow.isNewItem && !parentRow.preGeneratedId) {
    try {
      const response = await generatePlanId();
      if (response && response.data) {
        parentRow.preGeneratedId = response.data;
        // 同时设置为originalId，这样在创建时可以使用这个ID
        parentRow.originalId = response.data;
      }
    } catch (error) {
      console.error('预生成父任务ID失败:', error);
      // 如果预生成失败，使用临时ID作为备选
      parentRow.preGeneratedId = generateUniqueId(parentRow.serialNumber);
      parentRow.originalId = parentRow.preGeneratedId;
    }
  }
};

// 添加子任务
const addSubTask = async (parentRow) => {
  try {
    // 检查是否为顶级节点（无序号），顶级节点不能添加子项
    if (!parentRow.serialNumber || parentRow.serialNumber.trim() === '') {
      ElMessage.warning('顶级节点不能添加子项');
      return;
    }

    // 确保父任务有children数组
    if (!parentRow.children) {
      parentRow.children = [];
    }

    // 确保父任务有正确的ID用于父子关系
    await ensureParentHasId(parentRow);

    // 使用智能序号生成
    const newSerialNumber = generateChildSerialNumber(parentRow, planDataEdit.value);

    // 为新子任务预生成ID
    let childPreGeneratedId = null;
    try {
      const response = await generatePlanId();
      if (response && response.data) {
        childPreGeneratedId = response.data;
      }
    } catch (error) {
      console.error('预生成子任务ID失败:', error);
    }

    // 创建临时的新子任务（前端临时ID，标记为新增）
    const newTask = {
      id: generateUniqueId(newSerialNumber), // 临时ID
      originalId: childPreGeneratedId, // 使用预生成的ID
      preGeneratedId: childPreGeneratedId, // 保存预生成的ID
      taskName: '新子任务',
      parentTask: parentRow.serialNumber, // 使用父任务的序号
      responsible: '',
      executorName: '',
      status: '0', // 未开始
      planStartDate: '',
      planEndDate: '',
      planDuration: '',
      note: '',
      actualStartDate: '',
      actualEndDate: '',
      actualDuration: '',
      level: parentRow.level + 1,
      serialNumber: newSerialNumber,
      hasChildren: false,
      children: [],
      // 标记字段
      isNewItem: true, // 标记为新增项
      parentPlanId: parentRow.originalId || parentRow.preGeneratedId || parentRow.id, // 使用父任务的真实ID或预生成ID
      needsCreate: true, // 标记需要调用创建接口
      // 变更相关字段
      changeRemark: '新增子任务',
      reviewOpinion: ''
    };



    // 将新子任务添加到父任务的children数组
    parentRow.children.push(newTask);

    // 设置父任务的hasChildren为true
    parentRow.hasChildren = true;

    // 更新序号列宽度（新的序号可能更长）
    serialColumnWidth.value = calculateSerialColumnWidth(planDataEdit.value);
    // 更新工作项名称列宽度
    taskNameColumnWidth.value = calculateTaskNameColumnWidth(planDataEdit.value);

    ElMessage.success('已添加新子任务，请填写必填信息后提交');
  } catch (error) {
    console.error('添加子任务失败:', error);
    ElMessage.error('添加子任务失败，请重试');
  }
};

// 删除任务 - 修改为标记删除，不立即调用API
const deleteTask = async (row, index) => {
  // 检查是否为顶级节点（无序号），顶级节点不能删除
  if (!row.serialNumber || row.serialNumber.trim() === '') {
    ElMessage.warning('顶级节点不能删除');
    return;
  }

  ElMessageBox.confirm(`确定要删除"${row.taskName}"任务吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    try {
      // 如果是新增项，直接从前端数据中移除
      if (row.isNewItem) {
        const removeTask = (tasks, targetId) => {
          for (let i = 0; i < tasks.length; i++) {
            if (tasks[i].id === targetId) {
              tasks.splice(i, 1);
              return true;
            }

            if (tasks[i].children && tasks[i].children.length) {
              if (removeTask(tasks[i].children, targetId)) {
                // 如果子任务被删除后没有其他子任务，更新hasChildren标志
                if (tasks[i].children.length === 0) {
                  tasks[i].hasChildren = false;
                }
                return true;
              }
            }
          }
          return false;
        };

        removeTask(planDataEdit.value, row.id);
        ElMessage.success('已移除新增任务');
      } else {
        // 如果是已存在的项，标记为删除状态
        row.isDeleted = true;
        row.deleteRemark = `删除任务: ${row.taskName}`;

        // 递归标记所有子任务为删除状态
        const markChildrenAsDeleted = (item) => {
          if (item.children && item.children.length > 0) {
            item.children.forEach(child => {
              child.isDeleted = true;
              child.deleteRemark = `父任务删除，级联删除: ${child.taskName}`;
              markChildrenAsDeleted(child);
            });
          }
        };

        markChildrenAsDeleted(row);
        ElMessage.success('已标记删除，将在提交时生效');
      }
    } catch (error) {
      ElMessage.error('删除操作失败：' + (error.message || '未知错误'));
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 恢复任务 - 取消删除标记
const restoreTask = (row) => {
  try {
    // 取消删除标记
    row.isDeleted = false;
    delete row.deleteRemark;

    // 递归恢复所有子任务
    const restoreChildren = (item) => {
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          child.isDeleted = false;
          delete child.deleteRemark;
          restoreChildren(child);
        });
      }
    };

    restoreChildren(row);
    ElMessage.success('已恢复任务');
  } catch (error) {
    ElMessage.error('恢复任务失败：' + (error.message || '未知错误'));
  }
};

// 获取干系人列表作为责任人选项
const loadUserOptions = async () => {
  try {
    if (!contractNo) {
      console.warn('合同号不存在，无法获取干系人列表');
      // 使用默认数据
      userOptions.value = [
        { label: '危慧慧', value: '45' }
      ];
      return;
    }

    const response = await getStakeholderListAll({ contractNo: contractNo });
    if (response && (response.code === 200 || response.code === 0) && response.data) {
      const users = response.data.map(stakeholder => ({
        label: stakeholder.stakeholderUserName || stakeholder.name,
        value: String(stakeholder.id)
      }));
      userOptions.value = users;
    } else {
      // 如果没有干系人数据，使用默认数据
      userOptions.value = [
        { label: '危慧慧', value: '45' }
      ];
    }
  } catch (error) {
    console.error('获取干系人列表失败:', error);
    ElMessage.warning('获取干系人列表失败，使用默认数据');
    userOptions.value = [
      { label: '危慧慧', value: '45' }
    ];
  }
};

// 通过stakeholderUserName查询用户ID
const getUserIdByName = async (stakeholderUserName) => {
  try {
    if (!stakeholderUserName) {
      return null;
    }

    // 调用用户列表接口，使用stakeholderUserName作为realName查询
    const response = await getUserList({
      pageNum: 1,
      pageSize: 100,
      realName: stakeholderUserName
    });

    if (response && response.data && response.data.length > 0) {
      // 查找完全匹配的用户
      const matchedUser = response.data.find(user =>
        user.realName === stakeholderUserName
      );

      if (matchedUser) {
        return matchedUser.id;
      }
    }

    console.warn(`未找到用户: ${stakeholderUserName}`);
    return null;
  } catch (error) {
    console.error('查询用户ID失败:', error);
    return null;
  }
};



// 计算序号的缩进样式
const getSerialNumberStyle = (row) => {
  // 根据序号计算层级深度
  const serialNumber = row.serialNumber || '';
  const level = getLevelFromSerialNumber(serialNumber);

  // 每级缩进16px
  const paddingLeft = level * 16 + 8; // 基础缩进8px + 层级缩进

  return {
    paddingLeft: `${paddingLeft}px`,
    fontSize: '12px',
    lineHeight: '32px',
    display: 'inline-block',
    whiteSpace: 'nowrap'
  };
};

// 计算工作项名称的缩进样式
const getTaskNameStyle = (row) => {
  // 根据序号计算层级深度
  const serialNumber = row.serialNumber || '';
  const level = getLevelFromSerialNumber(serialNumber);

  // 每级缩进20px
  const paddingLeft = level * 20 + 8; // 基础缩进8px + 层级缩进

  return {
    paddingLeft: `${paddingLeft}px`,
    display: 'inline-block',
    width: '100%'
  };
};

// 根据序号计算层级深度
const getLevelFromSerialNumber = (serialNumber) => {
  if (!serialNumber) return 0;

  // 计算序号中的点号数量来确定层级
  const dots = (serialNumber.match(/\./g) || []).length;
  return dots;
};

// 处理前置工作项点击
const handlePretaskClick = (serialNumber) => {
  // 获取所有表格行
  const tableRows = document.querySelectorAll('.plan-table .el-table__row');
  let targetRow = null;

  // 遍历所有行查找匹配的序号
  for (const row of tableRows) {
    const serialCell = row.querySelector('.serial-cell span');
    if (serialCell && serialCell.textContent.trim() === serialNumber.trim()) {
      targetRow = row;
      break;
    }
  }

  if (targetRow) {
    // 先移除所有之前的高亮
    document.querySelectorAll('.el-table__row.highlighted').forEach(row => {
      row.classList.remove('highlighted');
    });

    // 添加高亮
    targetRow.classList.add('highlighted');

    // 滚动到目标行
    targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // 2秒后移除高亮
    setTimeout(() => {
      targetRow.classList.remove('highlighted');
    }, 2000);
  } else {
    ElMessage.warning(`未找到工作项序号: ${serialNumber}`);
  }
};

// 取消编辑
const cancelEdit = () => {
  ElMessageBox.confirm('确定要取消计划变更吗？未保存的修改将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 返回到项目详情页面
    router.push({
      name: 'ProjectDetail',
      params: { id: projectId }
    });
  }).catch(() => {
    // 用户取消操作
  });
};

// 获取所有变更项的摘要
const getChangeSummary = () => {
  const changedItems = [];

  const collectChangedItems = (items) => {
    items.forEach(item => {
      // 检查删除项
      if (item.isDeleted) {
        changedItems.push({
          serialNumber: item.serialNumber,
          taskName: item.taskName,
          changes: ['删除任务'],
          changeType: 'delete'
        });
      }
      // 检查新增项
      else if (item.isNewItem && item.needsCreate) {
        changedItems.push({
          serialNumber: item.serialNumber,
          taskName: item.taskName,
          changes: ['新增子任务'],
          changeType: 'create'
        });
      }
      // 检查已有项的变更
      else if (item._originalData && hasItemChanged(item)) {
        const changes = [];
        const original = item._originalData;

        if (item.taskName !== original.planName) {
          changes.push(`任务名称: ${original.planName} → ${item.taskName}`);
        }
        if (item.responsible !== original.ownerName) {
          changes.push(`责任人: ${original.ownerName || '无'} → ${item.responsible || '无'}`);
        }
        if (item.executorName !== original.executorName) {
          changes.push(`执行人: ${original.executorName || '无'} → ${item.executorName || '无'}`);
        }
        if (item.status !== String(original.planStatus || '0')) {
          changes.push(`状态: ${original.planStatus || '0'} → ${item.status}`);
        }
        if (item.planStartDate !== (original.planStartTime ? original.planStartTime.split(' ')[0] : '')) {
          changes.push(`开始日期: ${original.planStartTime ? original.planStartTime.split(' ')[0] : '无'} → ${item.planStartDate || '无'}`);
        }
        if (item.planEndDate !== (original.planEndTime ? original.planEndTime.split(' ')[0] : '')) {
          changes.push(`结束日期: ${original.planEndTime ? original.planEndTime.split(' ')[0] : '无'} → ${item.planEndDate || '无'}`);
        }

        changedItems.push({
          serialNumber: item.serialNumber,
          taskName: item.taskName,
          changes,
          changeType: 'update'
        });
      }

      if (item.children && item.children.length > 0) {
        collectChangedItems(item.children);
      }
    });
  };

  collectChangedItems(planDataEdit.value);
  return changedItems;
};

// 提交计划变更（先保存到草稿，再提交审核）
const saveEdit = async () => {
  try {
    console.log('=== 开始提交计划变更 ===');
    console.log('当前项目ID:', route.params.projectId);

    // 1. 检查是否有变更
    const changeSummary = getChangeSummary();
    console.log('变更摘要:', changeSummary);

    if (changeSummary.length === 0) {
      ElMessage.warning('没有检测到计划变更，无需提交');
      return;
    }

    // 2. 验证有变更的行的必填字段
    const validationErrors = validateChangedItems();
    if (validationErrors.length > 0) {
      ElMessage.error(`提交失败，请完善以下信息：\n${validationErrors.join('\n')}`);
      return;
    }

    // 3. 确认提交
    await ElMessageBox.confirm(
      `检测到 ${changeSummary.length} 项计划变更，确认提交审核？\n\n提交后将进入审核流程。`,
      '确认提交计划变更',
      {
        type: 'warning',
        confirmButtonText: '确认提交',
        cancelButtonText: '取消'
      }
    );

    saveLoading.value = true;

    // 4. 先清除之前的变更标识
    try {
      await clearChangeMarks(projectId);
    } catch (clearError) {
      // 清除失败不影响后续操作
    }

    // 5. 使用批量更新接口保存并提交审核
    const totalCount = await saveAllChangesWithBatchUpdate();

    ElMessage.success('计划变更已提交审核');

    // 导航回项目详情页
    router.push({
      name: 'ProjectDetail',
      params: { id: projectId }
    });

  } catch (error) {
    if (error === 'cancel') {
      return; // 用户取消操作
    }

    // 使用统一的错误处理
    if (error.code) {
      // 尝试使用统一的错误码处理
      import('@/utils/errorCodes').then(({ showErrorMessage }) => {
        showErrorMessage(error.code, error.message || '提交计划变更失败')
      }).catch(() => {
        // 如果导入失败，使用原有的错误处理逻辑
        handleLegacyError(error)
      })
    } else {
      // 没有错误码，使用原有的错误处理逻辑
      handleLegacyError(error)
    }
  } finally {
    saveLoading.value = false;
  }
}

// 原有的错误处理逻辑（作为备用）
const handleLegacyError = (error) => {
  let errorMessage = '提交计划变更失败';

  if (error.code === 20009) {
    errorMessage = '没有待提交的草稿，请先修改计划内容';
  } else if (error.message) {
    // 判断是在哪个阶段失败的
    if (error.message.includes('草稿保存失败') || error.message.includes('新增项创建失败') || error.message.includes('修改项更新失败')) {
      errorMessage = `草稿保存失败: ${error.message}`;
    } else if (error.message.includes('数据验证失败')) {
      errorMessage = `数据验证失败: ${error.message}`;
    } else if (error.message.includes('提交审核失败')) {
      errorMessage = `提交审核失败: ${error.message}`;
    } else {
      errorMessage = `操作失败: ${error.message}`;
    }
  } else if (typeof error === 'string') {
    errorMessage = `操作失败: ${error}`;
  }

  ElMessage.error(errorMessage);
}

// 保存所有变更到草稿表
const saveAllChangesToDraft = async () => {
  const changedItems = [];

  // 递归收集所有有变更的工作项（包括新增项）
  const collectChangedItems = (items) => {
    items.forEach(item => {
      // 检查是否为新增项或已有变更
      const isNewItem = item.isNewItem && item.needsCreate;
      const isChangedItem = item._originalData && hasItemChanged(item);

      if (isNewItem || isChangedItem) {
        // 数据验证
        const errors = validatePlanData(item);
        if (errors.length > 0) {
          throw new Error(`数据验证失败: ${errors.join(', ')}`);
        }

        changedItems.push({
          item,
          taskName: item.taskName,
          serialNumber: item.serialNumber,
          isNewItem: isNewItem
        });
      }

      if (item.children && item.children.length > 0) {
        collectChangedItems(item.children);
      }
    });
  };

  collectChangedItems(planDataEdit.value);

  if (changedItems.length === 0) {
    return 0;
  }

  // 逐个保存变更项，便于错误定位
  let savedCount = 0;
  const failedItems = [];

  for (const { item, taskName, serialNumber, isNewItem } of changedItems) {
    try {
      if (isNewItem) {
        await createWorkItemDraftFromItem(item);
        savedCount++;
      } else {
        await updateWorkItemDraftFromItem(item);
        savedCount++;
      }
    } catch (error) {
      console.error(`保存失败: ${serialNumber} - ${taskName}`, error);
      failedItems.push({ serialNumber, taskName, error: error.message });
    }
  }

  if (failedItems.length > 0) {
    const failedList = failedItems.map(f => `${f.serialNumber} - ${f.taskName}`).join(', ');
    throw new Error(`以下工作项保存失败: ${failedList}`);
  }

  return savedCount;
};

// 使用批量更新接口保存所有变更（支持新增、编辑、删除）
const saveAllChangesWithBatchUpdate = async () => {
  const createList = [];
  const updateList = [];
  const deleteList = [];

  // 递归收集所有变更项目
  const collectAllChanges = async (items) => {
    for (const item of items) {
      const hasChanged = item._originalData ? hasItemChanged(item) : false;
      const canUpdate = canUpdateItem(item);

      console.log(`检查项目: ${item.taskName || '未命名'} - 有变更:${hasChanged}, 可更新:${canUpdate}`);

      // 收集删除项
      if (item.isDeleted && !item.isNewItem) {
        // 只有非新增项才需要调用删除API
        deleteList.push({
          id: item.originalId || item.id,
          changeRemark: item.deleteRemark || `删除任务: ${item.taskName}`
        });
        console.log(`  → 添加到删除列表`);
      }
      // 收集新增项
      else if (item.isNewItem && item.needsCreate && canCreateItem(item)) {
        console.log(`  → 符合新增项条件`);
        // 数据验证
        const errors = validatePlanData(item);
        if (errors.length > 0) {
          console.log(`新增项数据验证失败 - ${item.taskName}:`, errors);
          return;
        }

        // 获取责任人ID - 通过stakeholderUserName查询用户ID
        let ownerId = null;
        if (item.responsible && item.responsible !== '无') {
          ownerId = await getUserIdByName(item.responsible);
        }

        // 获取执行人ID - 通过stakeholderUserName查询用户ID
        let executorId = null;
        if (item.executorName && item.executorName !== '无') {
          executorId = await getUserIdByName(item.executorName);
        }

        // 获取父级计划ID - 直接使用item中保存的parentPlanId
        let parentPlanId = item.parentPlanId || null;

        const createItem = {
          projectId: route.params.projectId,
          planName: item.taskName,
          planStartTime: item.planStartDate || null, // 允许为空
          planEndTime: item.planEndDate || null, // 允许为空
          ownerId: ownerId, // 允许为空
          executorId: executorId, // 允许为空
          parentPlanId: parentPlanId,
          seriNum: item.serialNumber || null,
          planStatus: item.status || '1',
          changeRemark: item.changeRemark || '新增计划'
        };

        console.log(`新增项数据 - ${item.taskName}:`, createItem);

        // 如果有预生成的ID，使用它
        if (item.preGeneratedId) {
          createItem.id = item.preGeneratedId;
        }

        createList.push(createItem);
        console.log(`  → 添加到新增列表`);
      }
      // 收集编辑项
      else if (!item.isDeleted && item._originalData && hasItemChanged(item) && canUpdateItem(item)) {
        console.log(`  → 符合编辑项条件`);
        // 数据验证
        const errors = validatePlanData(item);
        if (errors.length > 0) {
          console.log(`编辑项数据验证失败 - ${item.taskName}:`, errors);
          return;
        }

        // 获取责任人ID - 通过stakeholderUserName查询用户ID
        let ownerId = null;
        if (item.responsible && item.responsible !== '无') {
          ownerId = await getUserIdByName(item.responsible);
        }

        // 获取执行人ID - 通过stakeholderUserName查询用户ID
        let executorId = null;
        if (item.executorName && item.executorName !== '无') {
          executorId = await getUserIdByName(item.executorName);
        }

        const updateItem = {
          id: item.originalId || item.id,
          planName: item.taskName,
          planStartTime: item.planStartDate || null, // 允许为空
          planEndTime: item.planEndDate || null, // 允许为空
          ownerId: ownerId, // 允许为空
          executorId: executorId, // 允许为空
          prePlanId: item._originalData?.prePlanId || null,
          seriNum: item.serialNumber || null,
          preSeriNum: item.parentTask || null, // 使用用户编辑的前置工作项序号
          planStatus: item.status || '1',
          changeRemark: item.changeRemark || '编辑计划'
        };

        console.log(`编辑项数据 - ${item.taskName}:`, updateItem);
        updateList.push(updateItem);
        console.log(`  → 添加到编辑列表`);
      }
      else {
        console.log(`  → 不符合任何收集条件`);
      }

      // 递归处理子项目（但跳过已删除的项目的子项目）
      if (!item.isDeleted && item.children && item.children.length > 0) {
        await collectAllChanges(item.children);
      }
    }
  };

  await collectAllChanges(planDataEdit.value);

  console.log('=== 批量更新调试信息 ===');
  console.log('项目ID:', route.params.projectId);
  console.log('新增项数量:', createList.length);
  console.log('修改项数量:', updateList.length);
  console.log('删除项数量:', deleteList.length);
  console.log('新增项详情:', createList);
  console.log('修改项详情:', updateList);
  console.log('删除项详情:', deleteList);

  // 检查是否有任何变更
  const totalChanges = createList.length + updateList.length + deleteList.length;
  if (totalChanges === 0) {
    console.log('没有检测到任何变更');
    return 0;
  }

  // 调用批量更新接口
  const batchUpdateData = {
    projectId: route.params.projectId,
    createList: createList,
    updateList: updateList,
    deleteList: deleteList
  };

  console.log('批量更新请求数据:', batchUpdateData);

  try {
    console.log('正在调用批量更新接口...');
    const response = await batchUpdateWorkItemDraft(batchUpdateData);
    console.log('批量更新接口响应:', response);
    console.log('根据接口文档，此接口应该自动提交审核');

    // 等待一段时间让后端处理完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 检查是否有待审核的项目，如果没有说明接口可能没有自动提交审核
    try {
      console.log('检查是否有待审核项目...');
      const reviewListResponse = await getProjectWorkItemReviewList(route.params.projectId);
      console.log('审核列表响应:', reviewListResponse);

      if (!reviewListResponse.data || reviewListResponse.data.length === 0) {
        console.log('没有找到待审核项目，尝试手动提交审核...');
        // 手动调用提交审核接口
        const submitResponse = await submitProjectWorkItemReview(route.params.projectId);
        console.log('手动提交审核响应:', submitResponse);
      } else {
        console.log(`找到 ${reviewListResponse.data.length} 个待审核项目，批量更新接口已自动提交审核`);
      }
    } catch (checkError) {
      console.warn('检查审核状态失败，但批量更新已完成:', checkError);
    }

    return totalChanges;
  } catch (error) {
    console.error('批量更新接口调用失败:', error);
    throw new Error(`批量更新失败: ${error.message}`);
  }
};

// 分步骤保存所有变更到草稿表（先创建新增项，再更新修改项）
const saveAllChangesToDraftStepByStep = async () => {
  const newItems = [];
  const changedItems = [];

  // 递归收集新增项和修改项
  const collectItems = (items) => {
    items.forEach(item => {
      // 检查是否为新增项
      if (item.isNewItem && item.needsCreate) {
        // 检查是否具备创建的必要条件
        if (canCreateItem(item)) {
          // 数据验证
          const errors = validatePlanData(item);
          if (errors.length > 0) {
            // 验证失败，跳过此项
          } else {
            newItems.push({
              item,
              taskName: item.taskName,
              serialNumber: item.serialNumber
            });
          }
        } else {

        }
      }
      // 检查是否为修改项
      else if (item._originalData && hasItemChanged(item)) {
        // 检查是否具备更新的必要条件
        if (canUpdateItem(item)) {
          // 数据验证
          const errors = validatePlanData(item);
          if (errors.length > 0) {
            // 验证失败，跳过此项
          } else {
            changedItems.push({
              item,
              taskName: item.taskName,
              serialNumber: item.serialNumber
            });
          }
        } else {

        }
      }

      if (item.children && item.children.length > 0) {
        collectItems(item.children);
      }
    });
  };

  collectItems(planDataEdit.value);



  // 如果没有任何变更，直接返回
  if (newItems.length === 0 && changedItems.length === 0) {
    return { newItemsCount: 0, updatedItemsCount: 0 };
  }

  let newItemsCount = 0;
  let updatedItemsCount = 0;
  const failedItems = [];

  // 第一步：创建所有新增项
  if (newItems.length > 0) {
    for (const { item, taskName, serialNumber } of newItems) {
      try {
        const result = await createWorkItemDraftFromItem(item);
        if (result) {
          newItemsCount++;
        }
      } catch (error) {
        console.error(`创建失败: ${serialNumber} - ${taskName}`, error);
        failedItems.push({ serialNumber, taskName, error: error.message, type: '创建' });
      }
    }

    // 如果有新增项创建失败，立即停止
    if (failedItems.length > 0) {
      const failedList = failedItems.map(f => `${f.serialNumber} - ${f.taskName}`).join(', ');
      throw new Error(`新增项创建失败: ${failedList}`);
    }

    // 等待数据库同步
    await new Promise(resolve => setTimeout(resolve, 1500));
  }

  // 第二步：更新所有修改项
  if (changedItems.length > 0) {
    for (const { item, taskName, serialNumber } of changedItems) {
      try {
        const result = await updateWorkItemDraftFromItem(item);
        if (result) {
          updatedItemsCount++;
        }
      } catch (error) {
        console.error(`更新失败: ${serialNumber} - ${taskName}`, error);

        // 如果是"计划草稿不存在"错误，尝试先创建草稿再更新
        if (error.message && error.message.includes('计划草稿不存在')) {
          try {
            // 将修改项当作新增项来创建
            const createResult = await createWorkItemDraftFromItem(item);
            if (createResult) {
              updatedItemsCount++;
            } else {
              failedItems.push({ serialNumber, taskName, error: '创建草稿失败', type: '创建' });
            }
          } catch (createError) {
            console.error(`创建草稿也失败: ${serialNumber} - ${taskName}`, createError);
            failedItems.push({ serialNumber, taskName, error: createError.message, type: '创建' });
          }
        } else {
          failedItems.push({ serialNumber, taskName, error: error.message, type: '更新' });
        }
      }
    }

    // 如果有修改项更新失败，立即停止
    if (failedItems.length > 0) {
      const failedList = failedItems.map(f => `${f.serialNumber} - ${f.taskName}`).join(', ');
      throw new Error(`修改项更新失败: ${failedList}`);
    }

    // 等待数据库同步
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  return { newItemsCount, updatedItemsCount };
};

// 验证草稿是否存在
const verifyDraftExists = async () => {
  try {
    const response = await getProjectWorkItemDraftTree(projectId);

    if (response && response.data && response.data.length > 0) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

// 检查项目是否具备更新的必要条件（根据后端接口要求）
const canUpdateItem = (item) => {
  // 放宽更新条件：只要求任务名称必填，其他字段可以为空
  if (!item.taskName) {
    console.log(`  - canUpdateItem检查失败，缺少任务名称`);
    return false;
  }

  console.log(`  - canUpdateItem检查通过`);
  return true; // 任务名称有值就可以更新
};

// 检查项目是否具备创建的必要条件
const canCreateItem = (item) => {
  // 对于新增项，只要求计划名称必填，其他字段可以后续补充
  if (!item.taskName) {
    return false; // 计划名称必填
  }

  return true; // 计划名称有值就可以创建
};

// 创建新的工作项草稿
const createWorkItemDraftFromItem = async (item) => {
  // 获取责任人ID
  let ownerId = null;
  if (item.responsible) {
    ownerId = await getUserIdByName(item.responsible);
  }

  // 获取执行人ID
  let executorId = null;
  if (item.executorName) {
    executorId = await getUserIdByName(item.executorName);
  }

  // 判断是否为修改项（有原始数据）
  const isModifyItem = item._originalData && item.originalId;

  // 根据接口文档 PlanCreateDTO 构建请求数据
  const createData = {
    projectId: route.params.projectId,
    planName: item.taskName || '新子任务',
    parentPlanId: item.parentPlanId, // 父任务ID
    planStatus: item.status || '0', // 计划状态
    seriNum: item.serialNumber || null, // 计划序号
    planStartTime: item.planStartDate || null, // 计划开始时间
    planEndTime: item.planEndDate || null, // 计划完成时间
    ownerId: ownerId, // 负责人ID
    executorId: executorId, // 执行人ID
    prePlanId: null, // 前置计划ID（暂不支持）
    preSeriNum: item.parentTask || null, // 使用用户输入的前置工作项序号
    changeRemark: isModifyItem ? '编辑计划草稿' : (item.changeRemark || '新增子任务') // 变更说明
  };

  // 如果有预生成的ID，使用它作为计划ID
  if (item.preGeneratedId) {
    createData.id = item.preGeneratedId;
  }

  // 如果是修改项，需要添加原始ID信息
  if (isModifyItem) {
    createData.originalPlanId = item.originalId; // 原始计划ID
    createData.changeType = '2'; // 编辑类型
  } else {
    createData.changeType = '1'; // 新增类型
  }

  // 基本验证（不抛出错误，只记录日志）
  if (!createData.planName) {
    console.warn('计划名称不能为空，跳过创建');
    return null;
  }
  if (!createData.projectId) {
    console.warn('项目ID不能为空，跳过创建');
    return null;
  }



  try {
    // 调用创建API
    let response;
    try {
      response = await createWorkItemDraft(createData);
    } catch (apiError) {

      // 检查是否是"操作成功"的特殊情况
      if (apiError.message && apiError.message.includes('操作成功')) {
        const responseData = apiError.response?.data;
        if (responseData) {
          response = responseData;
        } else {
          response = await createWorkItemDraftMock(createData);
        }
      } else {
        response = await createWorkItemDraftMock(createData);
      }
    }



    // 检查响应是否成功 - 兼容多种响应格式
    const isSuccess = response && (
      response.code === 200 ||
      response.code === 0 ||
      (response.msg && response.msg.includes('成功')) ||
      (response.message && response.message.includes('成功'))
    );

    if (isSuccess) {
      // 创建成功，更新item的相关信息
      // 注意：有些接口可能不返回data，或者data为空，这也是正常的
      const returnedId = response.data?.id || generateUniqueId(item.serialNumber);

      item.originalId = returnedId; // 保存后端返回的ID或生成的ID
      item.isNewItem = false; // 标记为已创建
      item.needsCreate = false; // 不再需要创建

      return response;
    } else {
      // 响应不是成功状态
      const errorMsg = response?.msg || response?.message || `创建失败，响应码: ${response?.code}`;
      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error('创建草稿失败:', error);
    // 如果错误信息是"操作成功"，说明实际上是成功的，但响应格式有问题
    if (error.message && error.message.includes('操作成功')) {
      item.originalId = generateUniqueId(item.serialNumber);
      item.isNewItem = false;
      item.needsCreate = false;
      return { code: 200, msg: '操作成功' };
    }
    throw error;
  }
};

// 验证有变更的工作项的必填字段
const validateChangedItems = () => {
  const errors = [];

  // 递归检查所有有变更的工作项
  const checkItems = (items) => {
    items.forEach(item => {
      // 只检查有变更的项目（新增项或修改项）
      const isNewItem = item.isNewItem && item.needsCreate;
      const isChangedItem = item._originalData && hasItemChanged(item);

      if (isNewItem || isChangedItem) {
        const itemErrors = [];
        const itemName = item.taskName || `序号${item.serialNumber}`;

        // 验证工作项名称
        if (!item.taskName?.trim()) {
          itemErrors.push('工作项名称');
        }

        // 验证计划开始时间
        if (!item.planStartDate?.trim()) {
          itemErrors.push('计划开始时间');
        }

        // 验证计划结束时间
        if (!item.planEndDate?.trim()) {
          itemErrors.push('计划结束时间');
        }

        // 如果有错误，添加到错误列表
        if (itemErrors.length > 0) {
          errors.push(`${itemName}：${itemErrors.join('、')}不能为空`);
        }

        // 日期逻辑验证
        if (item.planStartDate && item.planEndDate) {
          if (new Date(item.planStartDate) > new Date(item.planEndDate)) {
            errors.push(`${itemName}：开始日期不能晚于结束日期`);
          }
        }
      }

      // 递归检查子项目
      if (item.children && item.children.length > 0) {
        checkItems(item.children);
      }
    });
  };

  checkItems(planDataEdit.value);
  return errors;
};

// 数据验证函数 - 只对有变更的行进行强制验证
const validatePlanData = (item) => {
  const errors = [];

  // 强制验证：工作项名称不能为空
  if (!item.taskName?.trim()) {
    errors.push('工作项名称不能为空');
  }

  // 强制验证：计划开始时间不能为空
  if (!item.planStartDate?.trim()) {
    errors.push('计划开始时间不能为空');
  }

  // 强制验证：计划结束时间不能为空
  if (!item.planEndDate?.trim()) {
    errors.push('计划结束时间不能为空');
  }

  // 日期逻辑验证：如果两个日期都有值，检查开始日期不能晚于结束日期
  if (item.planStartDate && item.planEndDate) {
    if (new Date(item.planStartDate) > new Date(item.planEndDate)) {
      errors.push('开始日期不能晚于结束日期');
    }
  }

  return errors;
};

// 检查工作项是否有变更
const hasItemChanged = (item) => {
  const original = item._originalData;
  if (!original) return false;

  // 标准化空值处理函数
  const normalizeValue = (value) => {
    if (value === null || value === undefined) return '';
    return String(value).trim();
  };

  // 比较各个字段，使用标准化的值比较
  return (
    normalizeValue(item.taskName) !== normalizeValue(original.planName) ||
    normalizeValue(item.planStartDate) !== normalizeValue(original.planStartTime ? original.planStartTime.split(' ')[0] : '') ||
    normalizeValue(item.planEndDate) !== normalizeValue(original.planEndTime ? original.planEndTime.split(' ')[0] : '') ||
    normalizeValue(item.responsible) !== normalizeValue(original.ownerName) ||
    normalizeValue(item.executorName) !== normalizeValue(original.executorName) ||
    normalizeValue(item.status) !== normalizeValue(original.planStatus || '0') ||
    normalizeValue(item.changeRemark) !== normalizeValue(original.changeRemark) ||
    normalizeValue(item.parentTask) !== normalizeValue(original.preSeriNum)
  );
};

// 在正式表数据中查找对应的计划项
const findItemInFormalData = (item) => {
  if (!planDataOrigin.value || planDataOrigin.value.length === 0) {
    return null;
  }

  // 递归查找函数
  const findInItems = (items) => {
    for (const formalItem of items) {
      // 通过序号匹配（最可靠的方式）
      if (formalItem.serialNumber === item.serialNumber) {
        return formalItem;
      }

      // 如果有子项，递归查找
      if (formalItem.children && formalItem.children.length > 0) {
        const found = findInItems(formalItem.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findInItems(planDataOrigin.value);
};

// 从表格项更新草稿计划项
const updateWorkItemDraftFromItem = async (item) => {
  // 根据责任人姓名查找用户ID
  let ownerId = item._originalData?.ownerId;
  if (item.responsible && item.responsible !== item._originalData?.ownerName) {
    ownerId = await getUserIdByName(item.responsible);
  }

  // 根据执行人姓名查找用户ID
  let executorId = item._originalData?.executorId;
  if (item.executorName && item.executorName !== item._originalData?.executorName) {
    executorId = await getUserIdByName(item.executorName);
  }

  // 强制将所有变更的记录重置为草稿状态，确保能被提交
  const targetReviewStatus = '0'; // 0 = 草稿状态

  // 判断变更类型的核心逻辑：
  // 1. 如果这个项目从来没有正式的计划数据（正式表为空），那么所有计划项都应该是"新增"
  // 2. 如果正式表有数据，但当前计划项在正式表中不存在，则是"新增"
  // 3. 如果正式表有数据，且当前计划项在正式表中存在，则是"编辑"

  // 检查正式表是否有数据
  const hasFormalData = planDataOrigin.value && planDataOrigin.value.length > 0;

  let changeType = '1'; // 默认为新增

  if (hasFormalData) {
    // 正式表有数据，需要检查当前计划项是否在正式表中存在
    // 通过序号或ID来匹配正式表中的对应项
    const formalItem = findItemInFormalData(item);
    if (formalItem) {
      // 在正式表中找到对应项，说明是编辑
      changeType = '2';
    } else {
      // 在正式表中没找到对应项，说明是新增
      changeType = '1';
    }
  } else {
    // 正式表没有数据，所有计划项都是新增
    changeType = '1';
  }



  // 根据接口文档 PlanUpdateDTO 构建请求数据，只包含支持的字段
  const updateData = {
    id: item.originalId || item.id, // 计划ID (必填)
    planName: item.taskName, // 计划名称 (必填)
    prePlanId: item._originalData?.prePlanId || null, // 前置计划ID (可选)
    planStartTime: item.planStartDate || null, // 计划开始时间 (必填) - 只传年月日
    planEndTime: item.planEndDate || null, // 计划完成时间 (必填) - 只传年月日
    ownerId: ownerId, // 负责人ID (必填)
    executorId: executorId, // 执行人ID (可选)
    planStatus: item.status, // 计划状态 (可选)
    seriNum: item._originalData?.seriNum || null, // 计划序号 (可选)
    preSeriNum: item.parentTask || null, // 使用用户编辑的前置工作项序号
    changeRemark: item.changeRemark || '编辑计划草稿' // 变更说明 (可选)
  };

  // 基本验证（不抛出错误，只记录日志）
  if (!updateData.id) {
    console.warn('计划ID不能为空，跳过更新');
    return null;
  }
  if (!updateData.planName) {
    console.warn('计划名称不能为空，跳过更新');
    return null;
  }



  try {
    const response = await updateWorkItemDraft(updateData);

    // 检查响应是否成功
    const isSuccess = response && (
      response.code === 200 ||
      response.code === 0 ||
      (response.msg && response.msg.includes('成功')) ||
      (response.message && response.message.includes('成功'))
    );

    if (isSuccess) {
      return response;
    } else {
      const errorMsg = response?.msg || response?.message || `更新失败，响应码: ${response?.code}`;
      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error('更新草稿失败:', error);
    throw error;
  }
};

// 加载状态枚举
const loadStatusEnum = async () => {
  try {
    let response;
    try {
      response = await getPlanStatusEnum();
    } catch (apiError) {
      response = await getPlanStatusEnumMock();
    }

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      // 将枚举数据转换为选项格式
      statusOptions.value = Object.entries(response.data).map(([key, value]) => ({
        label: value,
        value: key
      }));
    } else {
      // 使用默认状态选项
      statusOptions.value = [
        { label: '未开始', value: '0' },
        { label: '进行中', value: '1' },
        { label: '已完成', value: '2' },
        { label: '逾期', value: '3' },
        { label: '逾期完成', value: '4' }
      ];
    }
  } catch (error) {
    // 使用默认状态选项
    statusOptions.value = [
      { label: '未开始', value: '0' },
      { label: '进行中', value: '1' },
      { label: '已完成', value: '2' },
      { label: '逾期', value: '3' },
      { label: '逾期完成', value: '4' }
    ];
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDraftData();
  loadUserOptions();
  loadStatusEnum();
});
</script>

<style lang="scss" scoped>
/* tab内容容器 */
.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止外部出现滚动条 */
}

/* 白色圆角背景区块 */
.block-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin: 0;
  padding-bottom: 0;
  flex: 1;
  overflow: hidden; /* 防止外部出现滚动条 */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  box-sizing: border-box;
}

/* 项目计划编辑容器 */
.project-plan-edit {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden; /* 防止外部出现滚动条 */
  padding: 20px 20px 0;
  height: 100%;
}

/* 表格外包装 */
.table-wrapper {
  margin: 0 5px 5px;
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: hidden; /* 防止外部出现滚动条，只在表格内部滚动 */
  border-radius: 8px; /* 给包装器添加圆角 */
}

/* 确保表格滚动条可见 */
:deep(.el-table__body-wrapper) {
  overflow: auto !important;
}

:deep(.el-table__header-wrapper) {
  overflow: hidden !important; /* 改为hidden以显示圆角 */
}





/* 完全自定义表格样式 - 覆盖Element Plus默认样式 */
:deep(.el-table) {
  --el-table-row-height: 32px !important;
  --el-table-header-row-height: 32px !important;
  border-radius: 8px !important;
  --el-table-border-color: #cbd5e1;
  --el-table-header-bg-color: #f1f5f9;
  --el-table-row-hover-bg-color: #f8fafc;
  --el-table-tr-bg-color: transparent;
  color: #333;
  font-size: 12px !important;
  width: 100%;
  height: auto !important;
  max-height: calc(100vh - 220px) !important;
  border: 1px solid #cbd5e1 !important;
  overflow: hidden !important;
}

/* 表头行高度与表格内容行相同 */
:deep(.el-table__header-wrapper) {
  height: 32px !important;
}

:deep(.el-table__header-wrapper th) {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 !important;
  background-color: #f1f5f9;
  border-bottom: 1px solid #cbd5e1;
  border-right: 1px solid #cbd5e1;
}

:deep(.el-table__header-wrapper th .cell) {
  height: 32px !important;
  line-height: 32px !important;
  font-weight: 500;
  color: #333;
  padding: 0 4px !important;
  font-size: 12px !important;
}

/* 表格数据行高度比表头大 */
:deep(.el-table__body-wrapper) {
  overflow: auto;
  height: calc(100% - 32px);
}

:deep(.el-table__row) {
  height: 32px !important;
}

:deep(.el-table__cell) {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 !important;
}

:deep(.el-table__body td .cell) {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 4px !important;
  font-size: 12px !important;
  display: flex;
  align-items: center;
}

/* 控制表格的展开图标 */
:deep(.el-table__expand-icon) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  height: 24px !important;
  padding: 0 4px !important;
  box-shadow: none !important;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

:deep(.el-input__inner) {
  height: 22px !important;
  line-height: 22px !important;
  font-size: 12px !important;
}

/* 状态列样式优化 */
:deep(.status-column .el-select) {
  height: 24px !important;
  max-width: 90px;
  width: 90px;
  margin: 0 auto;
}

:deep(.status-column .el-select .el-input) {
  height: 24px !important;
  line-height: 24px !important;
}

:deep(.status-column .el-select .el-input__wrapper) {
  padding: 0 8px 0 4px !important;
}

/* 下拉选择器修复 */
:deep(.el-select-dropdown__item) {
  font-size: 12px !important;
  padding: 0 8px !important;
  height: 30px !important;
  line-height: 30px !important;
}

:deep(.el-select__popper) {
  z-index: 2100 !important;
}

:deep(.el-popper) {
  z-index: 2100 !important;
  min-width: 80px !important;
}

/* 日期选择器样式优化 */
:deep(.el-date-editor.el-input) {
  height: 24px !important;
  line-height: 24px !important;
  width: 95px !important;
}

:deep(.el-date-editor .el-input__wrapper) {
  padding: 0 4px !important;
}

:deep(.el-date-picker) {
  z-index: 2100 !important;
}

/* 操作列样式 */
.operation-column {
  display: flex;
  justify-content: center;
  gap: 4px;
  width: 100% !important;
  height: 100% !important;
  position: relative;
  z-index: 2;
  /* 移除强制白色背景 */
}

/* 左侧固定列样式简化 */



/* 右侧固定列样式优化 */
:deep(.el-table__fixed-right) {
  height: 100% !important;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

/* 确保操作列按钮样式正确 */
.operation-column .op-btn {
  height: 24px !important;
  line-height: 1 !important;
  padding: 0 4px !important;
  margin: 0 !important;
  background-color: transparent !important;
  z-index: 11 !important;
}

/* 禁用操作图标样式 */
.disabled-operation-icon {
  font-size: 16px;
  color: #c0c4cc;
  cursor: not-allowed;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
}

/* 确保固定列在鼠标悬停时有正确的背景色 - 与层级悬停效果保持一致 */
:deep(.el-table__fixed-left .el-table__row.level-0:hover td),
:deep(.el-table__fixed-right .el-table__row.level-0:hover td) {
  background-color: #cbd5e1 !important;
}

:deep(.el-table__fixed-left .el-table__row.level-1:hover td),
:deep(.el-table__fixed-right .el-table__row.level-1:hover td) {
  background-color: #e2e8f0 !important;
}

:deep(.el-table__fixed-left .el-table__row.level-2:hover td),
:deep(.el-table__fixed-right .el-table__row.level-2:hover td) {
  background-color: #f1f5f9 !important;
}

:deep(.el-table__fixed-left .el-table__row.level-3:hover td),
:deep(.el-table__fixed-left .el-table__row.level-4:hover td),
:deep(.el-table__fixed-left .el-table__row.level-5:hover td),
:deep(.el-table__fixed-right .el-table__row.level-3:hover td),
:deep(.el-table__fixed-right .el-table__row.level-4:hover td),
:deep(.el-table__fixed-right .el-table__row.level-5:hover td) {
  background-color: #f8fafc !important;
}

/* 层级背景色样式 - 低饱和度灰色系 */
:deep(.el-table__row.level-0 td) {
  background-color: #e2e8f0 !important; /* 顶级节点 - 中等灰色 */
}

:deep(.el-table__row.level-1 td) {
  background-color: #f1f5f9 !important; /* 二级节点 - 浅灰色 */
}

:deep(.el-table__row.level-2 td) {
  background-color: #f8fafc !important; /* 三级节点 - 极浅灰色 */
}

:deep(.el-table__row.level-3 td),
:deep(.el-table__row.level-4 td),
:deep(.el-table__row.level-5 td) {
  background-color: #ffffff !important; /* 四级及以下节点 - 白色 */
}

/* 确保固定列也有相同的层级背景色 */
:deep(.el-table__fixed-left .el-table__row.level-0 td),
:deep(.el-table__fixed-right .el-table__row.level-0 td) {
  background-color: #e2e8f0 !important;
}

:deep(.el-table__fixed-left .el-table__row.level-1 td),
:deep(.el-table__fixed-right .el-table__row.level-1 td) {
  background-color: #f1f5f9 !important;
}

:deep(.el-table__fixed-left .el-table__row.level-2 td),
:deep(.el-table__fixed-right .el-table__row.level-2 td) {
  background-color: #f8fafc !important;
}

:deep(.el-table__fixed-left .el-table__row.level-3 td),
:deep(.el-table__fixed-left .el-table__row.level-4 td),
:deep(.el-table__fixed-left .el-table__row.level-5 td),
:deep(.el-table__fixed-right .el-table__row.level-3 td),
:deep(.el-table__fixed-right .el-table__row.level-4 td),
:deep(.el-table__fixed-right .el-table__row.level-5 td) {
  background-color: #ffffff !important;
}

/* 悬停效果 - 在层级背景色基础上稍微调深 */
:deep(.el-table__row.level-0:hover td) {
  background-color: #cbd5e1 !important; /* 比#e2e8f0稍深的灰色 */
}

:deep(.el-table__row.level-1:hover td) {
  background-color: #e2e8f0 !important; /* 比#f1f5f9稍深的灰色 */
}

:deep(.el-table__row.level-2:hover td) {
  background-color: #f1f5f9 !important; /* 比#f8fafc稍深的灰色 */
}

:deep(.el-table__row.level-3:hover td),
:deep(.el-table__row.level-4:hover td),
:deep(.el-table__row.level-5:hover td) {
  background-color: #f8fafc !important; /* 浅灰色悬停 */
}

/* 表格单元格边框 */
:deep(.el-table td) {
  border-right: 1px solid #cbd5e1 !important;
  border-bottom: 1px solid #cbd5e1 !important;
}

:deep(.el-table__body tr td .cell) {
  background-color: inherit !important;
}

/* 基础固定列样式 - 确保正确显示 */
:deep(.el-table__fixed-left),
:deep(.el-table__fixed-right) {
  background-color: inherit !important;
  position: sticky !important;
}

:deep(.el-table__fixed-left .el-table__cell),
:deep(.el-table__fixed-right .el-table__cell) {
  background-color: inherit !important;
}

/* 确保固定列的表头正确显示 */
:deep(.el-table__fixed-left .el-table__header-wrapper),
:deep(.el-table__fixed-right .el-table__header-wrapper) {
  background-color: var(--el-table-header-bg-color) !important;
}

/* 确保固定列的表体正确显示 */
:deep(.el-table__fixed-left .el-table__body-wrapper),
:deep(.el-table__fixed-right .el-table__body-wrapper) {
  background-color: inherit !important;
}

/* 固定列阴影效果 - 参考ProjectTaskTable.vue */
:deep(.el-table__fixed-left) {
  height: 100% !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.12) !important;
  z-index: 10 !important;
}

:deep(.el-table__fixed-right) {
  height: 100% !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.12) !important;
  z-index: 10 !important;
}

/* 添加更明显的固定列分隔效果 */
:deep(.el-table__fixed-left::after) {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1));
  z-index: 20;
  pointer-events: none;
}

:deep(.el-table__fixed-right::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to left, transparent, rgba(0, 0, 0, 0.1));
  z-index: 20;
  pointer-events: none;
}

/* 调试样式 - 确保固定列可见 */
:deep(.el-table__fixed-left),
:deep(.el-table__fixed-right) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保固定列内容不被隐藏 */
:deep(.el-table__fixed-left .el-table__cell),
:deep(.el-table__fixed-right .el-table__cell) {
  display: table-cell !important;
  visibility: visible !important;
  opacity: 1 !important;
}




/* 使用更高的层级来确保操作列不会被其他内容覆盖 */
:deep(.el-table__fixed-right .el-table__cell .cell) {
  background-color: inherit !important;
  z-index: 11 !important;
  position: relative !important;
}

/* 序号列样式 */
.serial-cell {
  height: 32px !important;
  line-height: 32px !important;
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 12px;
}

.serial-number-text {
  font-size: 12px;
  line-height: 32px;
  white-space: nowrap;
  /* 移除overflow和text-overflow，让序号完整显示 */
  /* overflow: hidden; */
  /* text-overflow: ellipsis; */
  transition: all 0.3s ease;

  &:hover {
    color: #409EFF;
  }
}

.stage-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 12px;
  color: #409EFF;
  font-weight: bold;
}





/* 前置工作项样式 */
.pretask-content {
  display: inline-block;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  line-height: 32px !important;
  font-size: 12px;
  text-align: center;
  width: 100%;
}

.pretask-number {
  cursor: pointer;
  color: #409EFF;
  transition: color 0.3s;
  font-size: 12px;
  text-decoration: underline;

  &:hover {
    color: #337ecc;
  }
}

/* 高亮样式 */
:deep(.el-table__row.highlighted) {
  background-color: #ecf5ff !important;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% { background-color: #ecf5ff; }
  50% { background-color: #d9ecff; }
  100% { background-color: #ecf5ff; }
}

/* 可编辑单元格样式 */
.editable-cell {
  width: 100%;
  height: 32px !important;
  cursor: pointer;
  padding: 2px 6px;
  transition: all 0.2s ease;
  font-size: 12px;
  line-height: 28px !important;
  display: flex;
  align-items: center;
  border-radius: 3px;
  border: 1px solid transparent;
  position: relative;

  &:hover {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  /* 添加编辑提示图标 */
  &::after {
    content: '✎';
    position: absolute;
    right: 4px;
    top: 2px;
    font-size: 10px;
    color: #94a3b8;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::after {
    opacity: 0.6;
  }
}

/* 可编辑文本样式 */
.editable-text {
  font-size: 12px;
  line-height: 16px;
  cursor: pointer;
  min-height: 16px;
  display: block;
  padding: 2px 4px;
  border-radius: 2px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  word-break: break-all;

  &:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    color: #1e293b;
  }
}

/* 备注文本特定样式 */
.note-text {
  text-align: center;
  color: #64748b;

  &:empty::before {
    content: '点击编辑';
    color: #94a3b8;
    font-style: italic;
  }
}

/* 变更说明文本特定样式 */
.change-remark-text {
  text-align: left;
  color: #64748b;

  &:empty::before {
    content: '点击编辑';
    color: #94a3b8;
    font-style: italic;
  }
}

/* 编辑输入框样式 */
.edit-input {
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #3b82f6 inset !important;
    border-radius: 4px;
    background-color: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 0 0 1px #2563eb inset !important;
    }

    &.is-focus {
      box-shadow: 0 0 0 2px #3b82f6 inset !important;
    }
  }

  :deep(.el-input__inner) {
    height: 26px !important;
    line-height: 26px !important;
    font-size: 12px;
    padding: 0 8px;
    color: #1e293b;

    &::placeholder {
      color: #94a3b8;
      font-size: 11px;
    }
  }
}

/* 备注输入框特定样式 */
.note-input {
  :deep(.el-input__inner) {
    text-align: center;
  }
}

/* 变更说明输入框特定样式 */
.change-remark-input {
  :deep(.el-input__inner) {
    text-align: left;
  }
}

/* 工作项名称列样式 */
.task-name-cell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.task-name-text {
  font-size: 12px;
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;

  &:hover {
    color: #409EFF;
  }
}

/* 居中的可编辑单元格 */
:deep(.el-table__cell.is-center .editable-cell) {
  justify-content: center;
}

/* 头部样式 */
.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: none;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.plan-header h3 {
  font-size: 15px;
  margin: 0;
  font-weight: 500;
  color: #303133;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.plan-header h3 .el-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #409EFF;
}

/* 头部按钮样式 */
.header-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.header-buttons .el-button {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
}

.header-buttons .el-button .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 展开收起按钮组样式 */
.header-buttons .el-button[type="info"] {
  margin-right: 4px;
}

/* 在展开收起按钮和操作按钮之间添加分隔 */
.header-buttons .el-button[type="info"]:last-of-type {
  margin-right: 16px;
  position: relative;
}

.header-buttons .el-button[type="info"]:last-of-type::after {
  content: '';
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background-color: #e4e7ed;
}

/* 固定列样式 */
/* 移除重复的右侧固定列白色背景设置 */

/* 自定义展开图标样式 */
:deep(.custom-triangle-icon) {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 4px 0 4px 6px;
  border-color: transparent transparent transparent #c0c4cc;
  transform-origin: center;
  transition: transform 0.3s;
  margin-left: 6px;
  margin-right: 3px;
}

:deep(.custom-triangle-icon.expanded) {
  transform: rotate(90deg);
}

/* 修改表格最大高度，使其填充剩余空间 */
:deep(.el-table.plan-table) {
  max-height: calc(100vh - 220px) !important;
  height: auto !important;
}

/* 表格样式 */
:deep(.plan-table) {
  border: none !important;
  border-radius: 8px !important;
  overflow: visible !important; /* 改为visible以显示阴影 */
  /* 移除强制白色背景 */
  position: relative !important;

  /* Element Plus表格变量 */
  --el-table-border-color: #cbd5e1;
  --el-table-header-bg-color: #f1f5f9;
  --el-table-row-hover-bg-color: #f8fafc;
  --el-table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  --el-table-tr-bg-color: transparent;
}

/* 表格圆角样式 */
:deep(.rounded-table) {
  border-radius: 8px !important;
  overflow: hidden !important; /* 改为hidden以显示圆角 */
  border-collapse: separate !important;
  border-spacing: 0 !important;
  border: 1px solid #e6e6e6 !important;
}

:deep(.rounded-table .el-table__inner-wrapper) {
  border-radius: 8px !important;
  overflow: hidden !important; /* 改为hidden以显示圆角 */
  border: 1px solid #e6e6e6 !important;
}

/* 表头圆角 */
:deep(.rounded-table .el-table__header-wrapper) {
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
  overflow: hidden !important;
}

:deep(.rounded-table .el-table__header tr:first-child th:first-child) {
  border-top-left-radius: 8px !important;
}

:deep(.rounded-table .el-table__header tr:first-child th:last-child) {
  border-top-right-radius: 8px !important;
}

/* 表体圆角 */
:deep(.rounded-table .el-table__body-wrapper) {
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

:deep(.rounded-table .el-table__body tr:last-child td:first-child) {
  border-bottom-left-radius: 8px !important;
}

:deep(.rounded-table .el-table__body tr:last-child td:last-child) {
  border-bottom-right-radius: 8px !important;
}

/* 彻底修复表格底部边框问题 */
:deep(.el-table__border-bottom-patch) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.el-table__fixed-right-patch) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.el-table::before),
:deep(.el-table::after) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 修复表格底边边框与实际表格位置的偏差 */
:deep(.el-table__body-wrapper) {
  overflow: auto;
  height: calc(100% - 32px);
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  position: relative !important;
}

/* 完全移除底部多余的边框 */
:deep(.el-table__border-bottom) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
}

:deep(.el-table__border-left) {
  height: auto !important;
  bottom: 0 !important;
}

:deep(.el-table__border-right) {
  height: auto !important;
  bottom: 0 !important;
}

:deep(.el-table__cell.gutter) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.el-table__footer-wrapper) {
  display: none !important;
}

/* 确保表格底部边框与实际表格对齐 */
:deep(.el-table__border) {
  height: auto !important;
  bottom: 0 !important;
  display: none !important;
}

:deep(.el-table__body) {
  border-bottom: none !important;
}

/* 修复固定列底部边框 */
:deep(.el-table__fixed-right::before),
:deep(.el-table__fixed-left::before) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.el-table__fixed-right) {
  bottom: 0 !important;
  height: 100% !important;
  border-bottom: none !important;
}

:deep(.el-table__fixed-right .el-table__fixed-body-wrapper) {
  height: auto !important;
  bottom: 0 !important;
}

/* 强制移除所有边框线 */
:deep(.el-table) *::before,
:deep(.el-table) *::after {
  display: none !important;
}

/* 显示水平滚动条 */
:deep(.el-scrollbar__bar.is-horizontal) {
  display: block !important;
  height: 8px !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 4px !important;
}

:deep(.el-scrollbar__thumb.is-horizontal) {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
}

:deep(.el-scrollbar__thumb.is-horizontal:hover) {
  background: rgba(0, 0, 0, 0.3) !important;
}

/* 修复表格底部区域 */
:deep(.el-table__append-wrapper) {
  display: none !important;
}

:deep(.el-table__empty-block) {
  border-bottom: none !important;
}

/* 工期单元格样式 */
.duration-cell {
  width: 100%;
  height: 32px !important;
  font-size: 12px;
  line-height: 32px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 只读日期单元格样式 */
.readonly-date {
  width: 100%;
  height: 32px !important;
  font-size: 12px;
  line-height: 32px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
}

/* 变更说明和审核意见列样式 */
.change-remark-cell,
.review-opinion-cell {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  word-break: break-all;
  white-space: pre-line;
  max-height: 60px;
  overflow-y: auto;
  padding: 2px 4px;
  min-height: 20px;
}

.change-remark-cell:empty::before,
.review-opinion-cell:empty::before {
  content: '--';
  color: #c0c4cc;
}

/* 新增项样式 */
.new-tag {
  margin-right: 6px;
  font-size: 11px;
}

.new-item {
  background-color: #f0f9ff !important;
  border-left: 3px solid #67c23a !important;
}

.new-item .task-name-text {
  font-weight: 500;
}

/* 删除项样式 */
.delete-tag {
  margin-right: 6px;
  font-size: 11px;
}

.deleted-text {
  text-decoration: line-through;
  color: #909399 !important;
  opacity: 0.6;
}

/* 删除行样式 */
:deep(.el-table__row) {
  &.deleted-row {
    background-color: #fef0f0 !important;
    border-left: 3px solid #f56c6c !important;
    opacity: 0.7;
  }

  &.deleted-row .cell {
    color: #909399 !important;
  }

  &.deleted-row .task-name-text {
    text-decoration: line-through;
  }
}

/* 变更摘要对话框样式 */
:deep(.change-summary-dialog) {
  .el-message-box__content {
    max-height: 400px;
    overflow-y: auto;
  }

  .el-message-box__message {
    white-space: pre-line;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #606266;
  }

  .el-message-box {
    width: 600px;
    max-width: 90vw;
  }
}

/* 工具栏按钮组样式 - 参考HTML样本 */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-right: 12px;
  border-right: 1px solid rgba(0,0,0,0.08);
  position: relative;
}

.toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
}

/* 工具栏按钮样式 - 完全按照HTML样本 */
.toolbar-btn {
  padding: 8px 16px;
  background: #ffffff;
  border: 1px solid rgba(0,0,0,0.08);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 90px;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  position: relative;
  overflow: hidden;
}

.toolbar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.toolbar-btn:hover {
  background: #f8f9fa;
  border-color: rgba(0,123,255,0.2);
  color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,123,255,0.1);
}

.toolbar-btn:hover::before {
  opacity: 1;
}

.toolbar-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.toolbar-btn i {
  font-size: 14px;
  width: 16px;
  text-align: center;
  transition: transform 0.2s ease;
}

.toolbar-btn:hover i {
  color: #007bff;
  transform: scale(1.1);
}

/* 取消按钮特殊样式 */
.cancel-btn {
  color: #6c757d;
  border-color: rgba(108, 117, 125, 0.2);
}

.cancel-btn:hover {
  background: #f8f9fa;
  border-color: rgba(108, 117, 125, 0.3);
  color: #6c757d;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.1);
}

.cancel-btn:hover i {
  color: #6c757d;
}

/* 提交按钮特殊样式 */
.submit-btn {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.submit-btn:hover {
  background: #0056b3;
  border-color: #0056b3;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.submit-btn:hover i {
  color: #ffffff;
}

.submit-btn:disabled {
  background: #6c757d;
  border-color: #6c757d;
  color: #ffffff;
  cursor: not-allowed;
  opacity: 0.7;
}

.submit-btn:disabled:hover {
  background: #6c757d;
  border-color: #6c757d;
  transform: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

/* 加载状态的旋转动画 */
.fa-spinner.fa-spin {
  animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 确保按钮组在header-buttons中的布局 */
.header-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-buttons .toolbar-group:last-child {
  margin-left: auto;
}
</style>