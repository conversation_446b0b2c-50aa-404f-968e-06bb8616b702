<template>
  <div class="tab-content">
    <!-- 基本信息页的按钮 - 只有项目经理且非只读模式才能看到 -->
    <div class="basic-info-header" v-if="canEdit">
      <el-button type="primary" @click="editProjectNickname" :loading="loading">编辑项目简称</el-button>
    </div>
    
    <div class="basic-info" v-loading="loading" element-loading-text="正在加载项目信息...">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ localProjectData.projectName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目简称">{{ localProjectData.projectShortName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{ localProjectData.contractNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目状态">
          <span class="status-tag" :class="getStatusClass(localProjectData.projectState)">
            {{ getProjectStateDisplay(localProjectData.projectState) || '-' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="项目类型">{{ getProjectTypeDisplay(localProjectData.projectType) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="甲方单位">{{ localProjectData.firstPartyName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="省份">{{ localProjectData.provinceName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="城市">{{ localProjectData.cityName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="合同签订时间">{{ formatDate(localProjectData.signDate) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验收时间">{{ formatDate(localProjectData.acceptDate) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="合同金额">{{ formatAmount(localProjectData.contractAmount) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="已收款金额">{{ formatAmount(localProjectData.payCountMoney) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目工期">{{ localProjectData.projectDuration || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目经理">{{ localProjectData.projectManagerName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(localProjectData.createTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ localProjectData.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 编辑项目简称弹窗 -->
    <el-dialog 
      v-model="nicknameDialogVisible" 
      title="编辑项目简称" 
      width="500px" 
      :close-on-click-modal="false"
      :close-on-press-escape="!editLoading"
      :show-close="!editLoading"
    >
      <div class="nickname-input-container">
        <el-input 
          v-model="tempNickname" 
          placeholder="请输入项目简称" 
          maxlength="50"
          show-word-limit
          :disabled="editLoading"
          @keyup.enter="confirmEdit"
        />
        <div class="input-tip">
          <el-text type="info" size="small">项目简称将用于快速识别项目</el-text>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEdit" :disabled="editLoading">取消</el-button>
          <el-button type="primary" @click="confirmEdit" :loading="editLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watchEffect, inject, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { editProject } from '@/api/project';
import { useUserStore } from '@/store/modules/user';

// 组件接收props
const props = defineProps({
  projectData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  getProjectTypeDisplay: {
    type: Function,
    default: (code) => code
  },
  getProjectStateDisplay: {
    type: Function,
    default: (code) => code
  },
  getStatusClass: {
    type: Function,
    default: () => 'status-default'
  },
  readOnly: {
    type: Boolean,
    default: false
  }
});

// 事件发射
const emit = defineEmits(['update:projectData']);

// 注入刷新侧边栏项目列表的方法
const refreshSidebarProjects = inject('refreshSidebarProjects', () => {});

// 用户store
const userStore = useUserStore();

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 权限控制：判断是否可以编辑（项目经理且不是只读模式）
const canEdit = computed(() => {
  return !props.readOnly && isProjectManager.value;
});

// 本地维护一份数据，用于展示
const localProjectData = ref({...props.projectData});

// 监听外部数据变化，同步到本地数据
watchEffect(() => {
  localProjectData.value = {...props.projectData};
});

// 弹窗控制
const nicknameDialogVisible = ref(false);
const tempNickname = ref('');
const editLoading = ref(false);

// 编辑项目简称 - 只有项目经理才能执行
const editProjectNickname = () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限编辑项目简称');
    return;
  }

  tempNickname.value = localProjectData.value.projectShortName || '';
  nicknameDialogVisible.value = true;
};

// 取消编辑
const cancelEdit = () => {
  if (editLoading.value) {
    return; // 如果正在编辑中，不允许取消
  }
  nicknameDialogVisible.value = false;
  tempNickname.value = ''; // 清空临时输入
};

// 确认编辑 - 只有项目经理才能执行
const confirmEdit = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限编辑项目简称');
    return;
  }

  if (!tempNickname.value.trim()) {
    ElMessage.warning('项目简称不能为空');
    return;
  }

  editLoading.value = true;
  
  try {
    // 根据API接口规范准备数据 - 严格按照接口文档的字段结构
    const updateData = {
      id: localProjectData.value.id?.toString() || '', // API要求字符串类型
      projectState: localProjectData.value.projectState || '', // 项目状态
      projectShortName: tempNickname.value.trim(), // 项目简称
      projectManagerId: localProjectData.value.projectManagerId || '', // 项目经理ID
      projectManagerName: localProjectData.value.projectManagerName || '' // 项目经理姓名
    };
    
        // 调用API更新项目信息
    const response = await editProject(updateData);

    // 检查响应 - 支持多种成功状态码 (0, 200)
    if (response && (response.code === 0 || response.code === 200)) {
  
  // 更新本地数据
      const updatedData = {
    ...localProjectData.value,
        projectShortName: tempNickname.value.trim()
  };

      
      localProjectData.value = updatedData;
  
  // 通过事件通知父组件更新数据
      emit('update:projectData', updatedData);

  
  // 关闭弹窗并提示
  nicknameDialogVisible.value = false;
      tempNickname.value = '';
  ElMessage.success('项目简称修改成功');
      
      // 刷新侧边栏项目列表
      refreshSidebarProjects();
      
    } else {
      console.error('API响应异常:', response);
      throw new Error(response?.msg || response?.message || `更新失败 (状态码: ${response?.code})`);
    }
    
  } catch (error) {
    console.error('修改项目简称失败:', error);
    ElMessage.error('修改项目简称失败: ' + (error.message || '网络错误'));
  } finally {
    editLoading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  // 处理不同的日期格式
  if (dateString.includes('T')) {
    // ISO格式：2020-01-01T00:00:00
    return dateString.split('T')[0];
  } else if (dateString.includes(' ')) {
    // 带时间格式：2020-01-01 00:00:00
    return dateString.split(' ')[0];
  }
  // 已经是日期格式：2020-01-01
  return dateString;
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '';
  return new Intl.NumberFormat('zh-CN').format(amount);
};
</script>

<style scoped>
.tab-content {
  padding-top: 16px;
}

.basic-info {
  width: 100%;
}

.basic-info-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 16px;
}

.nickname-input-container {
  padding: 20px 0;
}

.input-tip {
  margin-top: 8px;
  text-align: center;
}

:deep(.el-descriptions) {
  padding: 20px 0;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: normal;
}

:deep(.el-descriptions__content) {
  font-weight: normal;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.status-implementing {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #d4edda;
}

.status-paused {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-pending {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-default {
  background-color: #f5f5f5;
  color: #909399;
  border: 1px solid #d9d9d9;
}
</style> 