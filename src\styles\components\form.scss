@import "../variables.scss";
@import "../mixins.scss";

// 表单相关全局样式
.el-form {
  // 行内表单样式
  &.el-form--inline {
    .el-form-item {
      margin-right: $spacing-base;
      margin-bottom: $spacing-small;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 表单项间距
  .el-form-item {
    margin-bottom: $spacing-base;

    &.is-error {
      margin-bottom: $spacing-large;
    }

    // 标签样式
    .el-form-item__label {
      font-weight: 400;
      color: $text-regular;
      padding-right: $spacing-small;
    }

    // 错误提示样式
    .el-form-item__error {
      color: $danger-color;
      font-size: 12px;
      padding-top: 2px;
    }
  }
}

// 输入框样式
.el-input {
  .el-input__wrapper {
    padding: 0 11px;
    height: 32px;
    border-radius: $border-radius-base;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  // 尺寸变体
  &.el-input--small {
    .el-input__wrapper {
      padding: 0 7px;
      height: 24px;
    }

    .el-input__inner {
      height: 24px;
      line-height: 24px;
      font-size: $font-size-small;
    }
  }

  &.el-input--large {
    .el-input__wrapper {
      padding: 0 15px;
      height: 40px;
    }

    .el-input__inner {
      height: 40px;
      line-height: 40px;
      font-size: $font-size-medium;
    }
  }
}

// 文本域样式
.el-textarea {
  .el-textarea__inner {
    padding: 5px 11px;
    border-radius: $border-radius-base;
    min-height: 80px;
  }

  // 尺寸变体
  &.el-textarea--small {
    .el-textarea__inner {
      padding: 3px 7px;
      font-size: $font-size-small;
    }
  }

  &.el-textarea--large {
    .el-textarea__inner {
      padding: 7px 15px;
      font-size: $font-size-medium;
    }
  }
}

// 选择器样式
.el-select {
  width: 100%;

  .el-select__wrapper {
    height: 32px;
  }

  // 尺寸变体
  &.el-select--small {
    .el-select__wrapper {
      height: 24px;
    }
  }

  &.el-select--large {
    .el-select__wrapper {
      height: 40px;
    }
  }
}

// 日期选择器样式
.el-date-editor {
  width: 100%;

  &.el-input__wrapper {
    padding: 0 11px;
    height: 32px;
  }

  // 尺寸变体
  &.el-date-editor--small {
    .el-input__wrapper {
      padding: 0 7px;
      height: 24px;
    }
  }

  &.el-date-editor--large {
    .el-input__wrapper {
      padding: 0 15px;
      height: 40px;
    }
  }
}

// 开关样式
.el-switch {
  height: 20px;
  
  &.el-switch--small {
    height: 16px;
  }
  
  &.el-switch--large {
    height: 24px;
  }
}

// 表单按钮区域
.form-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: $spacing-small;
  padding-top: $spacing-base;
  margin-top: $spacing-base;
  border-top: 1px solid $border-color-lighter;

  // 按钮间距
  .el-button + .el-button {
    margin-left: 0;
  }
} 