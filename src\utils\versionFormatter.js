/**
 * 版本号格式化工具
 * 根据版本状态格式化版本号显示
 */

/**
 * 格式化版本号
 * @param {number} version - 版本号数字
 * @param {number} versionStatus - 版本状态 (0: 预审核阶段, 1: 正式审核阶段)
 * @returns {string} 格式化后的版本号
 */
export const formatVersionNumber = (version, versionStatus) => {
  if (typeof version !== 'number') {
    return 'v1.0'; // 默认版本号
  }

  // 根据版本状态格式化版本号
  if (versionStatus === 0) {
    // 预审核阶段：0.1、0.2、0.3...
    return `v0.${version}`;
  } else if (versionStatus === 1) {
    // 正式审核阶段：1.0、2.0、3.0...
    return `v${version}.0`;
  } else {
    // 未知状态，使用默认格式
    return `v${version}.0`;
  }
};

/**
 * 版本状态枚举
 */
export const VERSION_STATUS = {
  PREVIEW: 0,    // 预审核阶段
  FORMAL: 1      // 正式审核阶段
};

/**
 * 版本状态显示文本
 */
export const VERSION_STATUS_TEXT = {
  [VERSION_STATUS.PREVIEW]: '预审核阶段',
  [VERSION_STATUS.FORMAL]: '正式审核阶段'
};


