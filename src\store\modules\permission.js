import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getMenuTree, getMenuIdsByRole } from '@/api/user'

export const usePermissionStore = defineStore(
  'permission',
  () => {
    // 状态
    const menuTree = ref([])
    const userMenuIds = ref([])
    const loading = ref(false)

    // 计算属性 - 用户可访问的菜单
    const accessibleMenus = computed(() => {
      if (!menuTree.value.length || !userMenuIds.value.length) {
        return []
      }
      
      return filterMenusByPermission(menuTree.value, userMenuIds.value)
    })

    // 递归过滤菜单树，只保留用户有权限的菜单
    const filterMenusByPermission = (menus, permissionIds) => {
      return menus.filter(menu => {
        // 检查当前菜单是否有权限
        const hasPermission = permissionIds.includes(menu.id)
        
        // 如果有子菜单，递归过滤
        if (menu.children && menu.children.length > 0) {
          const filteredChildren = filterMenusByPermission(menu.children, permissionIds)
          // 如果有可访问的子菜单，则保留父菜单
          if (filteredChildren.length > 0) {
            return {
              ...menu,
              children: filteredChildren
            }
          }
        }
        
        // 返回有权限的菜单
        return hasPermission
      }).map(menu => {
        // 如果有子菜单，确保子菜单也被正确过滤
        if (menu.children && menu.children.length > 0) {
          return {
            ...menu,
            children: filterMenusByPermission(menu.children, permissionIds)
          }
        }
        return menu
      })
    }

    // 检查用户是否有特定菜单的权限
    const hasMenuPermission = (menuId) => {
      return userMenuIds.value.includes(menuId)
    }

    // 检查用户是否有访问特定路径的权限
    const hasPathPermission = (path) => {
      if (!menuTree.value.length || !userMenuIds.value.length) {
        return false
      }

      // 递归查找路径对应的菜单ID
      const findMenuByPath = (menus, targetPath) => {
        for (const menu of menus) {
          if (menu.path === targetPath) {
            return menu
          }
          if (menu.children && menu.children.length > 0) {
            const found = findMenuByPath(menu.children, targetPath)
            if (found) return found
          }
        }
        return null
      }

      // 特殊路径映射处理 - 前端路径到菜单路径的映射
      const pathMapping = {
        '/workbench': '/dashboard',     // 工作台
        '/projects': '/project',        // 项目管理
        '/projects/all': '/project',    // 全部项目 -> 项目管理
        '/users': '/user-manage',       // 用户管理
        '/users/role': '/user-manage'   // 角色管理 -> 用户管理
      }

      const mappedPath = pathMapping[path] || path
      const menu = findMenuByPath(menuTree.value, mappedPath)
      return menu ? hasMenuPermission(menu.id) : false
    }

    // 获取菜单树 - 使用固定数据
    const fetchMenuTree = async () => {
      try {
        loading.value = true

        // 使用固定的菜单数据
        const fixedMenuData = [
          {
            "id": 1,
            "createdTime": "2025-07-21T08:10:06.000+00:00",
            "createdBy": null,
            "updatedTime": null,
            "updatedBy": null,
            "menuName": "工作台",
            "menuCode": "dashboard",
            "parentId": 0,
            "path": "/dashboard",
            "icon": "icon-dashboard",
            "sort": 1,
            "type": 1,
            "permission": null,
            "status": 1,
            "children": null
          },
          {
            "id": 2,
            "createdTime": "2025-07-21T08:10:06.000+00:00",
            "createdBy": null,
            "updatedTime": null,
            "updatedBy": null,
            "menuName": "项目管理",
            "menuCode": "project",
            "parentId": 0,
            "path": "/project",
            "icon": "icon-project",
            "sort": 2,
            "type": 1,
            "permission": null,
            "status": 1,
            "children": null
          },
          {
            "id": 3,
            "createdTime": "2025-07-21T08:10:06.000+00:00",
            "createdBy": null,
            "updatedTime": null,
            "updatedBy": null,
            "menuName": "用户管理",
            "menuCode": "user_manage",
            "parentId": 0,
            "path": "/user-manage",
            "icon": "icon-user",
            "sort": 3,
            "type": 1,
            "permission": null,
            "status": 1,
            "children": null
          }
        ]

        menuTree.value = fixedMenuData
      } catch (error) {
        console.error('获取菜单树失败:', error)
        menuTree.value = []
      } finally {
        loading.value = false
      }
    }

    // 获取用户菜单权限
    const fetchUserMenuPermissions = async (roleId) => {
      if (!roleId) {
        console.warn('角色ID为空，无法获取菜单权限')
        userMenuIds.value = []
        return
      }

      try {
        loading.value = true

        const response = await getMenuIdsByRole(roleId)

        if (response && Array.isArray(response)) {
          userMenuIds.value = response
        } else if (response && response.data && Array.isArray(response.data)) {
          userMenuIds.value = response.data
        } else {
          console.warn('角色权限API响应格式异常:', response)
          userMenuIds.value = []
        }
      } catch (error) {
        console.error('获取用户菜单权限失败:', error)
        userMenuIds.value = []
      } finally {
        loading.value = false
      }
    }

    // 直接设置用户菜单权限
    const setUserMenuPermissions = (permissions) => {
      if (Array.isArray(permissions)) {
        userMenuIds.value = permissions
      }
    }

    // 初始化权限数据
    const initPermissions = async (roleId) => {
      await Promise.all([
        fetchMenuTree(),
        fetchUserMenuPermissions(roleId)
      ])
    }

    // 重置权限数据
    const resetPermissions = () => {
      menuTree.value = []
      userMenuIds.value = []
      loading.value = false
    }

    return {
      // 状态
      menuTree,
      userMenuIds,
      loading,
      
      // 计算属性
      accessibleMenus,
      
      // 方法
      hasMenuPermission,
      hasPathPermission,
      fetchMenuTree,
      fetchUserMenuPermissions,
      setUserMenuPermissions,
      initPermissions,
      resetPermissions
    }
  },
  {
    persist: {
      paths: ['menuTree', 'userMenuIds']
    }
  }
)
