@import "../variables.scss";
@import "../mixins.scss";

// 上传组件全局样式
.el-upload {
  // 上传按钮样式
  &.el-upload--text {
    width: 100%;
  }

  // 上传列表样式
  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;

    .el-upload-list__item {
      transition: $transition-base;
      font-size: $font-size-small;
      color: $text-regular;
      line-height: 1.8;
      margin-top: $spacing-mini;
      position: relative;
      box-sizing: border-box;
      border-radius: $border-radius-base;
      width: 100%;

      .el-upload-list__item-name {
        color: $text-regular;
        display: block;
        margin-right: 40px;
        overflow: hidden;
        padding-left: 4px;
        text-overflow: ellipsis;
        transition: color 0.3s;
        white-space: nowrap;

        &:hover {
          color: $primary-color;
        }
      }

      .el-upload-list__item-status-label {
        position: absolute;
        right: $spacing-mini;
        top: 0;
        line-height: inherit;
        display: block;
      }

      .el-icon--close {
        display: inline-block;
        position: absolute;
        top: $spacing-mini;
        right: $spacing-mini;
        cursor: pointer;
        opacity: 0.75;
        color: $text-regular;
        transition: $transition-base;

        &:hover {
          opacity: 1;
          color: $danger-color;
        }
      }

      .el-progress {
        position: absolute;
        top: 20px;
        width: 100%;
      }

      &.is-success {
        .el-upload-list__item-name {
          i {
            color: $success-color;
          }
        }

        .el-upload-list__item-status-label {
          color: $success-color;
        }
      }

      &.is-error {
        .el-upload-list__item-name {
          color: $danger-color;

          i {
            color: $danger-color;
          }
        }

        .el-upload-list__item-status-label {
          color: $danger-color;
        }
      }
    }
  }
}

// 自定义上传样式
.upload-box {
  width: 120px;
  height: 120px;
  border: 1px dashed $border-color;
  border-radius: $border-radius-base;
  position: relative;
  overflow: hidden;
  margin-right: $spacing-small;
  margin-bottom: $spacing-small;
  
  &:hover {
    border-color: $primary-color;
  }
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;

  .el-icon {
    font-size: 28px;
    color: $text-secondary;
    margin-bottom: $spacing-mini;
  }

  span {
    color: $text-secondary;
    font-size: $font-size-small;
  }
}

.upload-tip {
  margin-top: $spacing-mini;
  font-size: $font-size-small;
  color: $text-secondary;
  line-height: 1.5;
}

.upload-tip-inline {
  font-size: $font-size-small;
  color: $text-secondary;
  line-height: 1.5;
  margin-left: $spacing-base;
}

// 图片预览区样式
.image-preview-area {
  display: flex;
  flex-wrap: wrap;
  margin-top: $spacing-small;
}

.image-thumb-box {
  width: 120px;
  height: 120px;
  margin-right: $spacing-small;
  margin-bottom: $spacing-small;
  position: relative;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-base;
  overflow: hidden;

  .image-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-delete {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: $transition-base;

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
} 