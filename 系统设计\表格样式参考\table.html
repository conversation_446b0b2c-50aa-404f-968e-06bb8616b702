<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Viewer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            color: #495057;
        }

        .toolbar {
            margin-bottom: 16px;
            padding: 12px 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0,0,0,0.05);
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05), 0 1px 4px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .toolbar:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.08), 0 2px 8px rgba(0,0,0,0.06);
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding-right: 12px;
            border-right: 1px solid rgba(0,0,0,0.08);
            position: relative;
        }

        .toolbar-group:last-child {
            border-right: none;
            padding-right: 0;
        }

        .toolbar button {
            padding: 8px 16px;
            background: #ffffff;
            border: 1px solid rgba(0,0,0,0.08);
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 90px;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
            position: relative;
            overflow: hidden;
        }

        .toolbar button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .toolbar button:hover {
            background: #f8f9fa;
            border-color: rgba(0,123,255,0.2);
            color: #007bff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.1);
        }

        .toolbar button:hover::before {
            opacity: 1;
        }

        .toolbar button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }

        .toolbar button i {
            font-size: 14px;
            width: 16px;
            text-align: center;
            transition: transform 0.2s ease;
        }

        .toolbar button:hover i {
            color: #007bff;
            transform: scale(1.1);
        }

        .toolbar button.delete-btn {
            color: #dc3545;
            border-color: rgba(220,53,69,0.2);
            background: #fff5f5;
        }

        .toolbar button.delete-btn:hover {
            background: #fff5f5;
            color: #dc3545;
            border-color: rgba(220,53,69,0.3);
            box-shadow: 0 4px 12px rgba(220,53,69,0.1);
        }

        .toolbar button.delete-btn i {
            color: #dc3545;
        }

        .toolbar input[type="search"] {
            padding: 8px 16px;
            border: 1px solid rgba(0,0,0,0.08);
            border-radius: 8px;
            width: 240px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
        }

        .toolbar input[type="search"]:focus {
            border-color: rgba(0,123,255,0.3);
            box-shadow: 0 4px 12px rgba(0,123,255,0.1);
            outline: none;
            background: #ffffff;
        }

        .toolbar input[type="search"]::placeholder {
            color: #adb5bd;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
                padding: 16px;
            }

            .toolbar-group {
                border-right: none;
                padding-right: 0;
                border-bottom: 1px solid rgba(0,0,0,0.08);
                padding-bottom: 12px;
                flex-wrap: wrap;
            }

            .toolbar-group:last-child {
                border-bottom: none;
                padding-bottom: 0;
            }

            .toolbar input[type="search"] {
                width: 100%;
            }

            .toolbar button {
                flex: 1;
                min-width: 0;
            }
        }

        /* 添加加载动画 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .toolbar button.loading {
            animation: pulse 1.5s infinite;
            opacity: 0.8;
            pointer-events: none;
        }

        .toolbar button.loading i {
            animation: spin 1s linear infinite;
        }

        /* 表格基础样式 */
        .table-container {
            position: relative;
            height: calc(100vh - 120px);
            overflow: auto;
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.1);
            background: #ffffff;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05), 0 1px 4px rgba(0,0,0,0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 14px;
            background-color: #ffffff;
        }

        /* 表头样式 */
        #data-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f8f9fa;
        }

        #data-table th {
            position: sticky;
            top: 0;
            z-index: 10;
            padding: 4px 12px; /* 减小表头内边距 */
            text-align: center;
            font-weight: 600;
            color: #495057;
            background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid rgba(0,0,0,0.1);
            border-bottom: 2px solid rgba(0,0,0,0.15);
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            height: 28px; /* 表头保持稍高 */
        }

        /* 表格主体样式 */
        #data-table tbody tr {
            height: 24px; /* 减小行高到24px */
        }

        #data-table td {
            padding: 2px 12px; /* 减小上下内边距 */
            border: 1px solid rgba(0,0,0,0.1);
            vertical-align: middle;
            line-height: 1.2;
            height: 24px; /* 确保单元格高度一致 */
        }

        /* 编辑状态样式 */
        .editing {
            padding: 0 !important;
        }

        .editing input,
        .editing select {
            width: 100%;
            height: 24px !important; /* 编辑框高度匹配 */
            padding: 1px 8px; /* 减小编辑框内边距 */
            border: 1px solid #e0e0e0;
            border-radius: 3px;
            font-size: 14px;
            line-height: 1.2;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
        }

        /* 任务单元格样式 */
        .task-cell {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 图标和文本对齐 */
        .toggle-icon {
            font-size: 12px; /* 稍微减小图标大小 */
            vertical-align: middle;
            margin-right: 4px;
            line-height: 1.2;
        }

        .task-name {
            vertical-align: middle;
            line-height: 1.2;
        }

        /* 前置任务样式 */
        .pretask-content {
            line-height: 1.2;
            padding: 0;
            margin: 0;
        }

        .pretask-number {
            padding: 0;
            margin: 0;
            line-height: 1.2;
        }

        /* 设置特定列的文本对齐 */
        td:nth-child(1), /* 序号 */
        td:nth-child(3), /* 前置任务 */
        td:nth-child(5), /* 重要程度 */
        td:nth-child(6), /* 任务状态 */
        td:nth-child(7), /* 是否风险 */
        td:nth-child(8), /* 风险等级 */
        td:nth-child(12), /* 计划开始时间 */
        td:nth-child(13), /* 计划结束时间 */
        td:nth-child(14), /* 计划工期 */
        td:nth-child(16), /* 变更后计划开始时间 */
        td:nth-child(17), /* 变更后计划结束时间 */
        td:nth-child(18), /* 变更后计划工期 */
        td:nth-child(19), /* 实际开始时间 */
        td:nth-child(20), /* 实际结束时间 */
        td:nth-child(21), /* 实际工期 */
        td:nth-child(22), /* 启动偏差 */
        td:nth-child(23), /* 完成偏差 */
        td:nth-child(24) /* 工期偏差值 */ {
            text-align: center;
        }

        /* 可编辑单元格样式 */
        .editable {
            position: relative;
            cursor: pointer;
        }

        .editable:hover {
            background-color: #fff3cd;
        }

        /* 删除按钮样式 */
        .clear-field-btn {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 18px;
            line-height: 18px;
            text-align: center;
        }

        .toolbar input[type="search"] {
            padding: 8px 16px;
            border: 1px solid rgba(0,0,0,0.08);
            border-radius: 8px;
            width: 240px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
        }

        .toolbar input[type="search"]:focus {
            border-color: rgba(0,123,255,0.3);
            box-shadow: 0 4px 12px rgba(0,123,255,0.1);
            outline: none;
            background: #ffffff;
        }

        .toolbar input[type="search"]::placeholder {
            color: #adb5bd;
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
                padding: 16px;
            }

            .toolbar-group {
                border-right: none;
                padding-right: 0;
                border-bottom: 1px solid rgba(0,0,0,0.08);
                padding-bottom: 12px;
                flex-wrap: wrap;
            }

            .toolbar-group:last-child {
                border-bottom: none;
                padding-bottom: 0;
            }

            .toolbar input[type="search"] {
                width: 100%;
            }

            .toolbar button {
                flex: 1;
                min-width: 0;
            }
        }

        /* 添加加载动画 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .toolbar button.loading {
            animation: pulse 1.5s infinite;
            opacity: 0.8;
            pointer-events: none;
        }

        .toolbar button.loading i {
            animation: spin 1s linear infinite;
        }

        /* 添加虚拟滚动样式 */
        .virtual-scroll-container {
            position: relative;
            height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .virtual-scroll-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .virtual-scroll-item {
            position: absolute;
            width: 100%;
            left: 0;
        }

        /* 添加文本溢出和提示框样式 */
        .tooltip {
            position: fixed;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            backdrop-filter: blur(4px);
            pointer-events: none;
        }

        /* 添加前置任务字段的样式 */
        .pretask-content {
            display: inline-block;
            white-space: nowrap;
            padding: 0;
            margin: 0;
            line-height: 1.5;
            vertical-align: middle;
        }

        .pretask-number {
            display: inline-block;
            padding: 0;
            margin: 0;
            cursor: pointer;
            vertical-align: middle;
        }

        /* 设置特定列的最大宽度和文本溢出处理 */
        td:nth-child(4), /* 计划任务 */
        td:nth-child(5), /* 负责人 */
        td:nth-child(9), /* 风险问题 */
        td:nth-child(10), /* 风险解决途径 */
        td:nth-child(11), /* 风险解决方案 */
        td:nth-child(15) /* 备注 */ {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            text-align: left;
        }

        /* 短字段居中显示 */
        td:nth-child(1), /* 序号 */
        td:nth-child(3), /* 前置任务 */
        td:nth-child(5), /* 重要程度 */
        td:nth-child(6), /* 任务状态 */
        td:nth-child(7), /* 是否风险 */
        td:nth-child(8), /* 风险等级 */
        td:nth-child(12), /* 计划开始时间 */
        td:nth-child(13), /* 计划结束时间 */
        td:nth-child(14), /* 计划工期 */
        td:nth-child(16), /* 变更后计划开始时间 */
        td:nth-child(17), /* 变更后计划结束时间 */
        td:nth-child(18), /* 变更后计划工期 */
        td:nth-child(19), /* 实际开始时间 */
        td:nth-child(20), /* 实际结束时间 */
        td:nth-child(21), /* 实际工期 */
        td:nth-child(22), /* 启动偏差 */
        td:nth-child(23), /* 完成偏差 */
        td:nth-child(24) /* 工期偏差值 */ {
            text-align: center;
        }

        /* 计划任务列的特殊处理 */
        td:nth-child(2) {
            text-align: left;
            white-space: nowrap;
            overflow: visible;
            position: relative;
        }

        /* 编辑状态下的特殊处理 */
        .editing input {
            width: 100% !important;
            max-width: none !important;
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
        }

        /* 添加悬停效果 */
        td:nth-child(4):hover,
        td:nth-child(5):hover,
        td:nth-child(9):hover,
        td:nth-child(10):hover,
        td:nth-child(11):hover,
        td:nth-child(15):hover {
            cursor: pointer;
        }

        /* 状态相关字段样式优化 */
        td:nth-child(6), /* 任务状态 */
        td:nth-child(7), /* 是否风险 */
        td:nth-child(8) /* 风险等级 */ {
            font-weight: 500;
        }

        /* 日期字段样式优化 */
        td:nth-child(12), /* 计划开始时间 */
        td:nth-child(13), /* 计划结束时间 */
        td:nth-child(16), /* 变更后计划开始时间 */
        td:nth-child(17), /* 变更后计划结束时间 */
        td:nth-child(19), /* 实际开始时间 */
        td:nth-child(20) /* 实际结束时间 */ {
            color: #666;
        }

        /* 添加进度条样式 */
        .progress-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1), 0 1px 4px rgba(0,0,0,0.05);
            z-index: 10000;
            display: none;
            min-width: 300px;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: rgba(0,123,255,0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 16px 0;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff 0%, #00bfff 100%);
            width: 0%;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0,123,255,0.2);
        }

        .progress-text {
            text-align: center;
            color: #495057;
            font-size: 14px;
            font-weight: 500;
            margin-top: 12px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .edit-input {
            position: absolute;
            border: 2px solid #007bff;
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
            outline: none;
            box-shadow: 0 2px 8px rgba(0,123,255,0.2);
            background: white;
            z-index: 1000;
            transition: none;
        }

        /* 前置任务列可点击 */
        td:nth-child(3) {
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
        }

        /* 状态相关样式 */
        tr[data-status="已完成"] {
            background-color: #e8f5e9;  /* 绿色 */
        }

        tr[data-status="进行中"] {
            background-color: #e3f2fd;  /* 蓝色 */
        }

        tr[data-status="超期"] {
            background-color: #ffebee;  /* 红色 */
        }

        tr[data-status="已延期"] {
            background-color: #ffebee;  /* 红色 */
        }

        tr[data-status="超期完成"] {
            background-color: #c8e6c9;  /* 更深的绿色 */
            box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
            transition: background-color 0.3s ease;
        }

        tr[data-status="超期完成"]:hover {
            background-color: #b7d9b7;  /* 悬停时更深的绿色 */
        }

        tr[data-status="挂起"] {
            background-color: #f3e5f5;  /* 紫色 */
        }

        tr[data-status="未开始"] {
            background-color: #fff8e1;  /* 黄色 */
        }

        /* 自定义颜色样式 - 优先级高于状态颜色 */
        tr[data-cell-color="red"] {
            background-color: #ffebee !important;  /* 红色 */
        }

        tr[data-cell-color="yellow"] {
            background-color: #fff8e1 !important;  /* 黄色 */
        }

        tr[data-cell-color="green"] {
            background-color: #e8f5e9 !important;  /* 绿色 */
        }

        tr[data-cell-color="blue"] {
            background-color: #e3f2fd !important;  /* 蓝色 */
        }

        tr[data-cell-color="purple"] {
            background-color: #f3e5f5 !important;  /* 紫色 */
        }

        /* 确保文本在有色背景上清晰可见 */
        tr td {
            color: #333333;  /* 深灰色文本 */
        }

        /* 编辑状态下的样式 */
        .editing {
            background-color: #ffffff !important;  /* 编辑时使用白色背景 */
        }

        .editing input {
            background-color: #ffffff;
            color: #333333;
        }

        /* 序号列样式 */
        .f_serial {
            text-align: left !important;
            padding-left: 8px !important;
            position: relative;
        }

        /* 多级表单层级样式 */
        .f_serial[data-level="1"] {
            padding-left: 8px !important;
        }

        .f_serial[data-level="2"] {
            padding-left: 24px !important;
        }

        .f_serial[data-level="3"] {
            padding-left: 40px !important;
        }

        .f_serial[data-level="4"] {
            padding-left: 56px !important;
        }

        .f_serial[data-level="5"] {
            padding-left: 72px !important;
        }

        /* 添加层级指示线 */
        .f_serial[data-level="2"]::before,
        .f_serial[data-level="3"]::before,
        .f_serial[data-level="4"]::before,
        .f_serial[data-level="5"]::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: #e0e0e0;
        }

        /* 表格转换功能的样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
            gap: 15px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 123, 255, 0.1);
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            color: #495057;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 转换成功消息样式 */
        .conversion-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: #f0f9ff;
            border-left: 3px solid #4cc9f0;
            color: #3a0ca3;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1010;
            transform: translateX(150%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .conversion-message.show {
            transform: translateX(0);
        }

        .conversion-message svg {
            width: 20px;
            height: 20px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }
        .error-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 400px;
            border: 1px solid #f5c6cb;
            animation: fadeIn 0.3s;
        }

        .error-notification.fade-out {
            animation: fadeOut 0.3s forwards;
        }

        .error-notification i {
            font-size: 16px;
        }

        .context-menu div.color-option.active {
            font-weight: 500;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
            padding-left: 13px;
        }

        .context-menu div.color-option.active i {
            font-weight: bold;
        }

        .context-menu div.color-option.active::before {
            border-width: 2px;
        }

        .context-menu div.color-option:hover::before {
            transform: translateY(-50%) scale(1.15);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .context-menu .fa-paint-brush {
            color: rgba(0, 0, 0, 0.5);
            font-size: 13px;
        }

        .context-menu div.color-option[color="red"] .fa-paint-brush {
            color: #f44336;
        }

        .context-menu div.color-option[color="yellow"] .fa-paint-brush {
            color: #ffc107;
        }

        .context-menu div.color-option[color="green"] .fa-paint-brush {
            color: #4caf50;
        }

        .context-menu div.color-option[color="blue"] .fa-paint-brush {
            color: #2196f3;
        }

        .context-menu div.color-option[color="purple"] .fa-paint-brush {
            color: #9c27b0;
        }

        /* 添加菜单转场动画 */
        @keyframes menuAppear {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .context-menu {
            position: absolute;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 150px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            border: 1px solid rgba(0, 0, 0, 0.08);
            padding: 5px 0;
            animation: contextMenuFadeIn 0.15s ease-out;
            transform-origin: top left;
            overflow: hidden;
        }

        @keyframes contextMenuFadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .context-menu div {
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #333;
            display: flex;
            align-items: center;
            font-size: 14px;
            position: relative;
            white-space: nowrap;
        }

        .context-menu div:hover {
            background-color: #f5f5f5;
        }

        .context-menu div i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        .context-menu .menu-divider {
            height: 1px;
            margin: 5px 0;
            padding: 0;
            background-color: #f0f0f0;
            pointer-events: none;
        }

        .context-menu div.color-option {
            display: flex;
            align-items: center;
        }

        .context-menu div.color-option::before {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .context-menu div.color-option[color="red"]::before {
            background-color: #ffcdd2;
        }

        .context-menu div.color-option[color="yellow"]::before {
            background-color: #ffecb3;
        }

        .context-menu div.color-option[color="green"]::before {
            background-color: #c8e6c9;
        }

        .context-menu div.color-option[color="blue"]::before {
            background-color: #bbdefb;
        }

        .context-menu div.color-option[color="purple"]::before {
            background-color: #e1bee7;
        }

        .context-menu div.color-option[color="none"]::before {
            background-color: #ffffff;
            border: 1px dashed #ccc;
        }

        .context-menu div.delete-option {
            color: #dc3545;
        }

        .context-menu div.delete-option i {
            color: #dc3545;
        }

        .context-menu div.delete-option:hover {
            background-color: #ffebee;
            color: #c82333;
        }

        .context-menu div.delete-option:hover::after {
            background-color: rgba(220, 53, 69, 0.08);
        }

        .context-menu div.delete-option:active {
            background-color: #ffdde0;
        }

        .context-menu div:active {
            transform: scale(0.98);
            transition: transform 0.1s;
        }

        .context-menu div.delete-option:active {
            background-color: #ffdde0;
        }

        /* 下拉菜单样式 */
        .form-select.edit-input {
            padding: 2px 20px 2px 8px;
            font-size: 14px;
            border: 1px solid #007bff;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 4px center;
            background-size: 12px;
        }

        .form-select.edit-input:focus {
            border-color: #0056b3;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        /* 编辑状态下的单元格样式 */
        .editing {
            position: relative;
            padding: 0 !important;
        }

        /* 下拉菜单选项样式 */
        .form-select.edit-input option {
            padding: 4px 8px;
            font-size: 14px;
        }

        /* 下拉菜单和输入框样式 */
        .form-select-sm, .form-control-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
            height: calc(1.5em + 0.5rem + 2px);
            border: 1px solid #ced4da;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-select-sm {
            padding-right: 1.75rem;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.5rem center;
            background-size: 16px 12px;
            appearance: none;
        }

        .form-select-sm:focus, .form-control-sm:focus {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* 编辑状态下的单元格样式 */
        .editing {
            padding: 0 !important;
            position: relative;
        }

        /* 下拉菜单选项样式 */
        .form-select-sm option {
            padding: 0.25rem 0.5rem;
        }

        /* 状态相关的背景色 */
        tr[data-status="进行中"] { background-color: #e3f2fd !important; }
        tr[data-status="未开始"] { background-color: #fff8e1 !important; }
        tr[data-status="已完成"] { background-color: #e8f5e9 !important; }
        tr[data-status="超期"] { background-color: #ffebee !important; }
        tr[data-status="超期完成"] { background-color: #c8e6c9 !important; }
        tr[data-status="挂起"] { background-color: #f3e5f5 !important; }

        /* 添加单元格悬浮删除图标样式 */
        .editable {
            position: relative;
        }

        /* 优化删除图标样式 */
        .clear-field-btn {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;  /* 更改为柔和的灰色 */
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s ease;
            background: #f8f9fa;  /* 更柔和的背景色 */
            border-radius: 4px;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
            z-index: 2;
            border: 1px solid #e9ecef;  /* 添加柔和的边框 */
        }

        .editable:hover .clear-field-btn {
            opacity: 0.7;  /* 降低默认显示时的不透明度 */
        }

        .clear-field-btn:hover {
            opacity: 1 !important;  /* 悬浮时完全显示 */
            background-color: #e9ecef;  /* 更柔和的悬浮背景色 */
            color: #495057;  /* 更深的文字颜色 */
            border-color: #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            transform: translateY(-50%) scale(1.05);  /* 添加微小的放大效果 */
        }

        .clear-field-btn:active {
            transform: translateY(-50%) scale(0.95);  /* 点击时的缩小效果 */
            background-color: #dee2e6;
        }

        /* 编辑状态时隐藏删除按钮 */
        .editing .clear-field-btn {
            display: none;
        }

        /* 为不同类型的字段设置不同的删除按钮样式 */
        /* 时间相关字段 */
        td[class*="time"] .clear-field-btn {
            color: #0d6efd;
            background-color: #f1f8ff;
            border-color: #e1f0ff;
        }

        td[class*="time"] .clear-field-btn:hover {
            background-color: #e1f0ff;
            color: #0a58ca;
        }

        /* 状态相关字段 */
        td[class*="status"] .clear-field-btn,
        td[class*="risk"] .clear-field-btn {
            color: #198754;
            background-color: #f0fdf4;
            border-color: #e1f6e8;
        }

        td[class*="status"] .clear-field-btn:hover,
        td[class*="risk"] .clear-field-btn:hover {
            background-color: #e1f6e8;
            color: #157347;
        }

        /* 重要程度字段 */
        td[class*="degree"] .clear-field-btn {
            color: #dc3545;
            background-color: #fff5f5;
            border-color: #ffe8e8;
        }

        td[class*="degree"] .clear-field-btn:hover {
            background-color: #ffe8e8;
            color: #bb2d3b;
        }

        .context-menu div.color-option::before {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .context-menu div.color-option i {
            color: #666;
            margin-right: 8px;
        }

        .context-menu div.color-option[color="red"] i {
            color: #f44336;
        }

        .context-menu div.color-option[color="yellow"] i {
            color: #ffc107;
        }

        .context-menu div.color-option[color="green"] i {
            color: #4caf50;
        }

        .context-menu div.color-option[color="blue"] i {
            color: #2196f3;
        }

        .context-menu div.color-option[color="purple"] i {
            color: #9c27b0;
        }

        .context-menu div.color-option[color="none"] i {
            color: #757575;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <div class="toolbar-group">
            <button onclick="expandAllRows()">
                <i class="fas fa-expand-alt"></i> 全部展开
            </button>
            <button onclick="collapseAllRows()">
                <i class="fas fa-compress-alt"></i> 全部收起
            </button>
        </div>
        <div class="toolbar-group">
            <input type="file" id="convertFileInput" style="display: none;" accept=".xlsx,.xls" onchange="handleConvertFileSelect(event)">
            <button onclick="document.getElementById('convertFileInput').click()" title="将分级序号表格转换成可导入格式">
                <i class="fas fa-exchange-alt"></i> 表格转换
            </button>
            <button onclick="exportToExcel()">
                <i class="fas fa-file-export"></i> 导出Excel
            </button>
            <input type="file" id="fileInput" style="display: none;" accept=".xlsx,.xls" onchange="handleFileSelect(event)">
            <button onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-file-import"></i> 导入Excel
            </button>
        </div>
        <div class="toolbar-group">
            <button id="deleteAllBtn" class="delete-btn">
                <i class="fas fa-trash"></i>
                全部删除
            </button>
        </div>
        <div class="toolbar-group">
            <input type="search" placeholder="搜索..." onkeyup="filterTable(this.value)">
        </div>
    </div>
    <div id="excel-container">
        <div id="table-container"></div>
    </div>
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>
    <div id="error" class="error-message" style="display: none;"></div>
    <div id="debug" class="debug-info" style="display: none;"></div>
    <div id="progress-container" class="progress-container">
        <div class="progress-bar">
            <div id="progress-bar-fill" class="progress-bar-fill"></div>
        </div>
        <div id="progress-text" class="progress-text">正在删除数据...</div>
    </div>

    <script>
        // 修改调试信息显示函数，只在控制台输出
        function showDebugInfo(message, type = 'info') {
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }

        const API_URL = 'http://***************:13000/api';
        let API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NDQ2ODczMDAsImV4cCI6MzMzMDIyODczMDB9.ZmvCGakfgtXmkFaWlTEFKJK8oPrfVlsPYJFisM34VqU';
        const TABLE_NAME = 't_treetask';

        let tableData = [];
        let selectedRows = new Set();
        let contextMenu = null;
        let isFetching = false;

        // 添加删除全部数据的函数
        async function deleteAllData() {
            if (!confirm('确定要删除所有数据吗？此操作不可恢复！')) {
                return;
            }

            try {
                showDebugInfo('正在删除所有数据...');
                
                // 显示进度条
                const progressContainer = document.getElementById('progress-container');
                const progressBar = document.getElementById('progress-bar-fill');
                const progressText = document.getElementById('progress-text');
                progressContainer.style.display = 'block';
                
                // 首先获取所有数据的ID
                const response = await fetch(`${API_URL}/${TABLE_NAME}?page=1&pageSize=1000`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`获取数据失败: ${response.status}`);
                }

                const data = await response.json();
                if (!data || !Array.isArray(data.data)) {
                    throw new Error('服务器返回的数据格式不正确');
                }

                const records = data.data;
                const total = records.length;
                let completed = 0;

                showDebugInfo(`找到 ${total} 条数据需要删除`);

                // 批量删除所有数据
                for (const record of records) {
                    const deleteResponse = await fetch(`${API_URL}/${TABLE_NAME}/${record.id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${API_KEY}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!deleteResponse.ok) {
                        if (deleteResponse.status === 401) {
                            console.log('Token已过期，尝试重新登录...');
                            const loginSuccess = await login();
                            if (loginSuccess) {
                                return deleteAllData();
                            } else {
                                throw new Error('重新登录失败');
                            }
                        }
                        throw new Error(`删除数据失败: ${deleteResponse.status}`);
                    }

                    // 更新进度
                    completed++;
                    const progress = (completed / total) * 100;
                    progressBar.style.width = `${progress}%`;
                    progressText.textContent = `正在删除数据... ${completed}/${total}`;
                }

                showDebugInfo('所有数据已成功删除');
                // 隐藏进度条
                progressContainer.style.display = 'none';
                // 刷新数据
                await fetchData();
            } catch (error) {
                console.error('Delete error:', error);
                showErrorMessage(`删除失败: ${error.message}`);
                // 隐藏进度条
                document.getElementById('progress-container').style.display = 'none';
            }
        }

        // 添加登录函数
        async function login() {
            try {
                showDebugInfo('正在登录...');
                const response = await fetch(`${API_URL}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',  // 默认用户名
                        password: 'admin'   // 默认密码
                    })
                });

                if (!response.ok) {
                    throw new Error(`登录失败: ${response.status}`);
                }

                const data = await response.json();
                if (data && data.token) {
                    API_KEY = data.token;
                    showDebugInfo('登录成功，获取到新的API密钥');
                    return true;
                } else {
                    throw new Error('登录响应中没有找到token');
                }
            } catch (error) {
                console.error('Login error:', error);
                showDebugInfo(`登录失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 添加事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const deleteAllBtn = document.getElementById('deleteAllBtn');
            if (deleteAllBtn) {
                deleteAllBtn.addEventListener('click', deleteAllData);
            }
        });

        // 添加性能监控
        const perfMetrics = {
            lastRenderTime: 0,
            renderCount: 0,
            averageRenderTime: 0
        };

        // 添加节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        // 优化后的fetchData函数
        async function fetchData(scrollToId = null, isPartialUpdate = false) {
            if (isFetching) {
                console.log('正在获取数据中，跳过重复请求');
                return;
            }
            isFetching = true;

            try {
                const startTime = window.performance.now();
                showDebugInfo('开始获取数据...');
                
                const response = await fetch(`${API_URL}/${TABLE_NAME}?page=1&pageSize=1000`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`获取数据失败: ${response.status}`);
                }

                const result = await response.json();
                if (!result || !Array.isArray(result.data)) {
                    throw new Error('服务器返回的数据格式不正确');
                }

                tableData = result.data;
                showDebugInfo(`获取到 ${tableData.length} 条数据`);

                // 使用requestAnimationFrame优化渲染
                requestAnimationFrame(() => {
                    createTable(tableData);
                    
                    if (scrollToId) {
                        const targetRow = document.querySelector(`#data-table tr[data-id="${scrollToId}"]`);
                        if (targetRow) {
                            targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            targetRow.classList.add('selected');
                        }
                    }
                });

                const endTime = window.performance.now();
                console.log(`数据获取和渲染总耗时: ${(endTime - startTime).toFixed(2)}ms`);

            } catch (error) {
                console.error('Error fetching data:', error);
                showErrorMessage(`获取数据失败: ${error.message}`);
            } finally {
                isFetching = false;
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        }

        // 局部更新表格行的函数
        function updateTableRows(data, expandedRows, scrollToId = null) {
            const tbody = document.querySelector('#data-table tbody');
            if (!tbody) return;

            // 构建树形结构
            const buildTreeData = (items, parentId = null, level = 0) => {
                return items
                    .filter(item => {
                        const itemParentId = item.parentId === null ? null : Number(item.parentId);
                        return itemParentId === parentId;
                    })
                    .map(item => ({
                        ...item,
                        level,
                        children: buildTreeData(items, Number(item.id), level + 1)
                    }));
            };

            const treeData = buildTreeData(data);
            
            // 创建行HTML
            const createRowHtml = (item, number) => {
                const indent = '&nbsp;'.repeat(item.level * 4);
                const hasChildren = item.children && item.children.length > 0;
                const isExpanded = expandedRows.has(item.id.toString());
                const toggleIcon = hasChildren
                    ? `<i class="fas fa-caret-${isExpanded ? 'down' : 'right'} toggle-icon"></i>`
                    : '<i class="fas fa-minus toggle-icon" style="visibility: hidden;"></i>';

                return `
                    <tr data-id="${item.id}" data-level="${item.level}" data-parent-id="${item.parentId || ''}" data-has-children="${hasChildren}" data-status="${item.f_status || ''}">
                        <td class="f_serial" data-level="${item.level}">${number}</td>
                        <td class="editable task-cell" ondblclick="startEditing(this, 'f_task')">
                            ${indent}${toggleIcon}
                            <span class="task-name">${item.f_task || ''}</span>
                        </td>
                        <td class="editable" ondblclick="startEditing(this, 'f_pretask')">
                            <span class="pretask-content">${(item.f_pretask || '').split('、').map(num => `<span class="pretask-number" onclick="handlePretaskClick(event, this)">${num.replace(/\s+/g, '').trim()}</span>`).join('、')}</span>
                        </td>
                        <td class="editable" ondblclick="startEditing(this, 'f_assigner')">${item.f_assigner || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_degree')">${item.f_degree || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_status')">${item.f_status || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_risk')">${item.f_risk || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_risk_level')">${item.f_risk_level || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_riskQ')">${item.f_riskQ || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_riskS')">${item.f_riskS || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_riskSL')">${item.f_riskSL || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_starttime')">${item.f_starttime || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_endtime')">${item.f_endtime || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_time')">${item.f_time ? item.f_time + '天' : ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_mark')">${item.f_mark || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_c_start_time')">${item.f_c_start_time || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_c_end_time')">${item.f_c_end_time || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_c_time')">${item.f_c_time ? item.f_c_time + '天' : ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_r_start_time')">${item.f_r_start_time || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_r_end_time')">${item.f_r_end_time || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_r_time')">${item.f_r_time ? item.f_r_time + '天' : ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_s_dev')">${item.f_s_dev || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_e_dev')">${item.f_e_dev || ''}</td>
                        <td class="editable" ondblclick="startEditing(this, 'f_dev')">${item.f_dev || ''}</td>
                    </tr>
                `;
            };

            // 递归渲染树形结构
            const renderRows = (items, parentNumber = '') => {
                let html = '';
                items.forEach((item, index) => {
                    const currentNumber = parentNumber ? `${parentNumber}.${index + 1}` : `${index + 1}`;
                    html += createRowHtml(item, currentNumber);
                    
                    // 如果节点是展开的，渲染其子节点
                    if (item.children && item.children.length > 0 && expandedRows.has(item.id.toString())) {
                        html += renderRows(item.children, currentNumber);
                    }
                });
                return html;
            };

            // 保存当前滚动位置
            const container = document.querySelector('.table-container');
            const scrollPosition = container ? container.scrollTop : 0;

            // 更新表格内容
            tbody.innerHTML = renderRows(treeData);

            // 恢复滚动位置
            if (container && !scrollToId) {
                container.scrollTop = scrollPosition;
            }

            // 如果需要滚动到特定行
            if (scrollToId) {
                const targetRow = tbody.querySelector(`tr[data-id="${scrollToId}"]`);
                if (targetRow) {
                    targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    targetRow.classList.add('selected');
                }
            }
        }

        // 优化后的createTable函数
        function createTable(data) {
            const startTime = window.performance.now();
            
            const container = document.getElementById('table-container');
            if (!container) {
                console.error('找不到表格容器');
                return;
            }

            // 使用DocumentFragment减少重绘
            const fragment = document.createDocumentFragment();
            const table = document.createElement('table');
            table.id = 'data-table';
            
            // 创建表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            const headers = [
                { text: '序号', minWidth: 60 },
                { text: '计划任务', minWidth: 150 },
                { text: '前置任务', minWidth: 80 },
                { text: '负责人', minWidth: 80 },
                { text: '重要程度', minWidth: 80 },
                { text: '任务状态', minWidth: 80 },
                { text: '是否风险', minWidth: 80 },
                { text: '风险等级', minWidth: 80 },
                { text: '风险问题', minWidth: 150 },
                { text: '风险解决途径', minWidth: 150 },
                { text: '风险解决方案', minWidth: 150 },
                { text: '计划开始时间', minWidth: 100 },
                { text: '计划结束时间', minWidth: 100 },
                { text: '计划工期', minWidth: 80 },
                { text: '备注', minWidth: 150 },
                { text: '变更后计划开始时间', minWidth: 140 },
                { text: '变更后计划结束时间', minWidth: 140 },
                { text: '变更后计划工期', minWidth: 120 },
                { text: '实际开始时间', minWidth: 100 },
                { text: '实际结束时间', minWidth: 100 },
                { text: '实际工期', minWidth: 80 },
                { text: '启动偏差', minWidth: 80 },
                { text: '完成偏差', minWidth: 80 },
                { text: '工期偏差值', minWidth: 80 }
            ];

            headers.forEach(header => {
                const th = document.createElement('th');
                th.style.minWidth = `${header.minWidth}px`;
                th.style.position = 'sticky';
                th.style.top = '0';
                th.style.zIndex = '2';
                th.textContent = header.text;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 创建表格主体
            const tbody = document.createElement('tbody');
            const treeData = buildTreeData(data);
            
            // 使用模板字符串一次性生成所有行
            const renderRows = (items, parentNumber = '') => {
                let html = '';
                items.forEach((item, index) => {
                    const currentNumber = parentNumber ? `${parentNumber}.${index + 1}` : `${index + 1}`;
                    const indent = '&nbsp;'.repeat(item.level * 4);
                    const hasChildren = item.children && item.children.length > 0;
                    const toggleIcon = hasChildren ? 
                        '<i class="fas fa-caret-down toggle-icon"></i>' : 
                        '<i class="fas fa-minus toggle-icon" style="visibility: hidden;"></i>';

                    html += `
                        <tr data-id="${item.id}" data-level="${item.level}" data-parent-id="${item.parentId || ''}" 
                            data-has-children="${hasChildren}" data-status="${item.f_status || ''}" 
                            data-cell-color="${item.f_cell_color || ''}">
                            <td class="f_serial" data-level="${item.level}">${currentNumber}</td>
                            <td class="editable task-cell" ondblclick="startEditing(this, 'f_task')">
                                ${indent}${toggleIcon}
                                <span class="task-name">${item.f_task || ''}</span>
                            </td>
                            <td class="editable" ondblclick="startEditing(this, 'f_pretask')">
                                <span class="pretask-content">${(item.f_pretask || '').split('、').map(num => 
                                    `<span class="pretask-number" onclick="handlePretaskClick(event, this)">${num.replace(/\s+/g, '').trim()}</span>`
                                ).join('、')}</span>
                            </td>
                            <td class="editable" ondblclick="startEditing(this, 'f_assigner')">${item.f_assigner || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_degree')">${item.f_degree || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_status')">${item.f_status || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_risk')">${item.f_risk || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_risk_level')">${item.f_risk_level || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_riskQ')">${item.f_riskQ || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_riskS')">${item.f_riskS || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_riskSL')">${item.f_riskSL || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_starttime')">${item.f_starttime || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_endtime')">${item.f_endtime || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_time')">${item.f_time ? item.f_time + '天' : ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_mark')">${item.f_mark || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_c_start_time')">${item.f_c_start_time || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_c_end_time')">${item.f_c_end_time || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_c_time')">${item.f_c_time ? item.f_c_time + '天' : ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_r_start_time')">${item.f_r_start_time || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_r_end_time')">${item.f_r_end_time || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_r_time')">${item.f_r_time ? item.f_r_time + '天' : ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_s_dev')">${item.f_s_dev || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_e_dev')">${item.f_e_dev || ''}</td>
                            <td class="editable" ondblclick="startEditing(this, 'f_dev')">${item.f_dev || ''}</td>
                        </tr>`;

                    if (hasChildren) {
                        html += renderRows(item.children, currentNumber);
                    }
                });
                return html;
            };

            tbody.innerHTML = renderRows(treeData);
            table.appendChild(tbody);
            
            fragment.appendChild(table);
            container.innerHTML = '';
            container.appendChild(fragment);

            // 初始化表格颜色
            setTimeout(() => {
                const rows = tbody.querySelectorAll('tr');
                rows.forEach(row => {
                    updateRowBackgroundColor(row);
                });
            }, 0);

            const endTime = window.performance.now();
            console.log(`创建表格总耗时: ${(endTime - startTime).toFixed(2)}ms`);
        }

        // 开始编辑单元格
        function startEditing(cell, fieldName) {
            if (cell.classList.contains('editing')) return;
            
            const originalValue = cell.textContent.trim();
            cell.classList.add('editing');
            
            let input;
            
            // 根据字段类型创建不同的输入控件
            if (fieldName === 'f_degree') {
                // 重要程度下拉菜单
                input = document.createElement('select');
                input.className = 'form-select form-select-sm';
                const options = ['高', '中', '低'];
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    if (originalValue === opt) option.selected = true;
                    input.appendChild(option);
                });
            } else if (fieldName === 'f_status') {
                // 任务状态下拉菜单
                input = document.createElement('select');
                input.className = 'form-select form-select-sm';
                const options = ['进行中', '未开始', '已完成', '超期', '超期完成', '挂起'];
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    if (originalValue === opt) option.selected = true;
                    input.appendChild(option);
                });
            } else if (fieldName === 'f_risk') {
                // 是否风险下拉菜单
                input = document.createElement('select');
                input.className = 'form-select form-select-sm';
                const options = ['是', '否'];
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    if (originalValue === opt) option.selected = true;
                    input.appendChild(option);
                });
            } else if (fieldName === 'f_risk_level') {
                // 风险等级下拉菜单
                input = document.createElement('select');
                input.className = 'form-select form-select-sm';
                const options = ['高', '中', '低'];
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    if (originalValue === opt) option.selected = true;
                    input.appendChild(option);
                });
            } else if (fieldName === 'f_task') {
                // 任务名称的特殊处理
                const taskName = cell.querySelector('.task-name');
                if (!taskName) return;
                
                input = document.createElement('input');
                input.type = 'text';
                input.value = taskName.textContent;
                input.className = 'form-control form-control-sm';
            } else if (fieldName === 'f_pretask') {
                // 前置任务的特殊处理
                const pretaskContent = cell.querySelector('.pretask-content');
                if (!pretaskContent) return;
                
                input = document.createElement('input');
                input.type = 'text';
                input.value = pretaskContent.textContent;
                input.className = 'form-control form-control-sm';
            } else if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                // 工期输入框
                input = document.createElement('input');
                input.type = 'number';
                input.min = '1';
                input.className = 'form-control form-control-sm';
                input.value = originalValue.replace('天', '');
            } else if (fieldName.includes('time')) {
                // 日期输入框
                input = document.createElement('input');
                input.type = 'date';
                input.className = 'form-control form-control-sm';
                input.value = originalValue;
            } else {
                // 默认文本输入框
                input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm';
                input.value = originalValue.replace('天', '');
            }

            // 设置通用属性
            input.dataset.field = fieldName;
            input.style.width = '100%';
            input.style.maxWidth = 'none';
            input.style.boxSizing = 'border-box';
            
            // 添加事件监听器
            if (input.tagName === 'SELECT') {
                input.addEventListener('change', () => {
                    finishEditing(input);
                });
            }
            input.addEventListener('blur', () => finishEditing(input));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    finishEditing(input);
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    restoreOriginalValue(input, cell, fieldName, originalValue);
                    cell.classList.remove('editing');
                }
            });
            
            // 清空单元格并添加输入控件
            if (fieldName === 'f_task') {
                const taskName = cell.querySelector('.task-name');
                taskName.innerHTML = '';
                taskName.appendChild(input);
            } else if (fieldName === 'f_pretask') {
                const pretaskContent = cell.querySelector('.pretask-content');
                pretaskContent.innerHTML = '';
                pretaskContent.appendChild(input);
            } else {
                cell.innerHTML = '';
                cell.appendChild(input);
            }
            
            input.focus();
        }

        function finishEditing(input) {
            const fieldName = input.dataset.field;
            let newValue = input.value.trim();
            const cell = fieldName === 'f_pretask' ? 
                input.closest('.editable') : 
                input.parentElement;
            const row = cell.closest('tr');
            const id = row.dataset.id;
            
            // 保存原始值，确保可以在验证失败时恢复
            let originalValue;
            if (input.defaultValue !== undefined) {
                // 如果输入框有默认值，使用它
                originalValue = input.defaultValue;
            } else {
                // 否则从单元格内容中获取原始值
                if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                    originalValue = cell.textContent.replace('天', '');
                } else if (fieldName.includes('starttime') || fieldName.includes('endtime')) {
                    originalValue = cell.textContent;
                } else if (fieldName === 'f_task') {
                    const taskName = cell.querySelector('.task-name');
                    originalValue = taskName ? taskName.textContent : cell.textContent;
                } else if (fieldName === 'f_pretask') {
                    originalValue = cell.querySelector('.pretask-content').textContent;
                } else {
                    originalValue = cell.textContent;
                }
            }

            // 验证输入
            if (!validateInput(fieldName, newValue)) {
                showErrorMessage(`输入值 "${newValue}" 对于字段 "${fieldName}" 无效`);
                restoreOriginalValue(input, cell, fieldName, originalValue);
                updateTableAfterValidationFailure(row);
                return;
            }

            // 验证时间范围（对于时间字段）
            if ((fieldName.includes('starttime') || fieldName.includes('endtime')) && newValue) {
                if (!validateTimeRange(row, fieldName, newValue)) {
                    restoreOriginalValue(input, cell, fieldName, originalValue);
                    updateTableAfterValidationFailure(row);
                return;
                }
            }

            // 准备更新数据
            const updateData = {
                [fieldName]: newValue || null  // 如果值为空字符串，则设置为 null
            };
            
            // 如果是时间相关字段，需要触发时间计算
            if (fieldName === 'f_time' || fieldName === 'f_starttime' || fieldName === 'f_endtime' ||
                fieldName === 'f_c_time' || fieldName === 'f_c_start_time' || fieldName === 'f_c_end_time' ||
                fieldName === 'f_r_time' || fieldName === 'f_r_start_time' || fieldName === 'f_r_end_time') {
                
                // 获取当前行的所有时间相关数据
                const timeData = {
                    f_time: row.querySelector('td:nth-child(14)')?.textContent?.replace('天', ''),
                    f_starttime: row.querySelector('td:nth-child(12)')?.textContent,
                    f_endtime: row.querySelector('td:nth-child(13)')?.textContent,
                    f_c_time: row.querySelector('td:nth-child(18)')?.textContent?.replace('天', ''),
                    f_c_start_time: row.querySelector('td:nth-child(16)')?.textContent,
                    f_c_end_time: row.querySelector('td:nth-child(17)')?.textContent,
                    f_r_time: row.querySelector('td:nth-child(21)')?.textContent?.replace('天', ''),
                    f_r_start_time: row.querySelector('td:nth-child(19)')?.textContent,
                    f_r_end_time: row.querySelector('td:nth-child(20)')?.textContent
                };

                // 更新当前修改的字段
                timeData[fieldName] = newValue;

                // 根据修改的字段类型进行相应的计算
                if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                    // 修改工期
                    const prefix = fieldName.split('_')[1] || '';
                    const startField = `f_${prefix}starttime`;
                    const endField = `f_${prefix}endtime`;
                    
                    if (timeData[startField]) {
                        // 有开始日期，计算结束日期
                        const calculatedEndDate = calculateEndDate(timeData[startField], newValue);
                        
                        // 验证计算出的结束日期是否在父任务范围内
                        const validation = validateCalculatedTime(row, endField, calculatedEndDate);
                        if (validation.valid === false) {
                            showErrorMessage(validation.message);
                            restoreOriginalValue(input, cell, fieldName, originalValue);
                            updateTableAfterValidationFailure(row);
                            return;
                        }
                        
                        updateData[endField] = calculatedEndDate;
                    } else if (timeData[endField]) {
                        // 有结束日期，计算开始日期
                        const calculatedStartDate = calculateStartDate(timeData[endField], newValue);
                        
                        // 验证计算出的开始日期是否在父任务范围内
                        const validation = validateCalculatedTime(row, startField, calculatedStartDate);
                        if (validation.valid === false) {
                            showErrorMessage(validation.message);
                            restoreOriginalValue(input, cell, fieldName, originalValue);
                            updateTableAfterValidationFailure(row);
                            return;
                        }
                        
                        updateData[startField] = calculatedStartDate;
                    }
                } else if (fieldName.includes('starttime')) {
                    // 修改开始日期
                    const prefix = fieldName.split('_')[1] || '';
                    const timeField = `f_${prefix}time`;
                    const endField = `f_${prefix}endtime`;
                    
                    if (timeData[endField]) {
                        // 有结束日期，计算工期
                        updateData[timeField] = calculateDuration(newValue, timeData[endField]);
                    }
                    // 不再根据工期计算结束时间
                } else if (fieldName.includes('endtime')) {
                    // 修改结束日期
                    const prefix = fieldName.split('_')[1] || '';
                    const timeField = `f_${prefix}time`;
                    const startField = `f_${prefix}starttime`;
                    
                    if (timeData[startField]) {
                        // 有开始日期，计算工期
                        updateData[timeField] = calculateDuration(timeData[startField], newValue);
                    }
                    // 不再反算开始时间
                }
            }

            // 发送更新请求
            updateRecord(id, updateData).then(() => {
                if (fieldName === 'f_pretask') {
                    // 对于前置任务列，保持span结构以支持点击跳转
                    const pretaskContent = cell.querySelector('.pretask-content');
                    pretaskContent.innerHTML = newValue.split('、').map(num => 
                        `<span class="pretask-number" onclick="handlePretaskClick(event, this)">${num.replace(/\s+/g, '').trim()}</span>`
                    ).join('、');
                } else if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                    // 如果是工期字段，显示时加上"天"字
                    cell.textContent = newValue ? newValue + '天' : '';
                } else if (fieldName === 'f_starttime' || 
                          fieldName === 'f_endtime' || 
                          fieldName === 'f_c_start_time' || 
                          fieldName === 'f_c_end_time' || 
                          fieldName === 'f_r_start_time' || 
                          fieldName === 'f_r_end_time') {
                    // 对于日期字段，如果值为空，则显示为空
                    cell.textContent = newValue || '';
                } else {
                    cell.textContent = newValue;
                }

                // 如果是状态字段更新，立即更新行的背景色
                if (fieldName === 'f_status') {
                    row.dataset.status = newValue;
                    updateRowBackgroundColor(row);
                }

                cell.classList.remove('editing');

                // 如果是计划任务字段，编辑完成后刷新页面
                if (fieldName === 'f_task') {
                    fetchData();
                }
            }).catch(error => {
                showErrorMessage(error.message);
                restoreOriginalValue(input, cell, fieldName, originalValue);
            });
        }

        // 添加此辅助函数，用于在验证失败后更新表格
        function updateTableAfterValidationFailure(row) {
            refreshRowDisplay(row);
            
            // 额外的处理：如果常规方法不起作用，尝试更激进的方式
            setTimeout(() => {
                // 如果是子任务，尝试折叠再展开父任务来强制刷新
                const parentId = row.dataset.parentId;
                if (parentId && parentId !== 'null') {
                    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
                    if (parentRow) {
                        const toggleIcon = parentRow.querySelector('.toggle-icon');
                        if (toggleIcon && toggleIcon.classList.contains('fa-caret-right')) {
                            toggleIcon.classList.remove('fa-caret-right');
                            toggleIcon.classList.add('fa-caret-down');
                        }
                    }
                }
                
                // 尝试直接使用fetch API重新获取并显示该行数据
                const id = row.dataset.id;
                if (id) {
                    fetch(`${API_URL}/${TABLE_NAME}/${id}`, {
                        method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                            } else {
                            throw new Error('获取数据失败');
                        }
                    })
                    .then(data => {
                        if (data && data.data) {
                            // 更新行数据
                            const rowData = data.data;
                            
                            // 重设各个单元格的内容
                            const startTimeCell = row.querySelector('td:nth-child(12)');
                            if (startTimeCell) startTimeCell.textContent = rowData.f_starttime || '';
                            
                            const endTimeCell = row.querySelector('td:nth-child(13)');
                            if (endTimeCell) endTimeCell.textContent = rowData.f_endtime || '';
                            
                            const timeCell = row.querySelector('td:nth-child(14)');
                            if (timeCell) timeCell.textContent = rowData.f_time ? rowData.f_time + '天' : '';
                            
                            const cStartTimeCell = row.querySelector('td:nth-child(16)');
                            if (cStartTimeCell) cStartTimeCell.textContent = rowData.f_c_start_time || '';
                            
                            const cEndTimeCell = row.querySelector('td:nth-child(17)');
                            if (cEndTimeCell) cEndTimeCell.textContent = rowData.f_c_end_time || '';
                            
                            const cTimeCell = row.querySelector('td:nth-child(18)');
                            if (cTimeCell) cTimeCell.textContent = rowData.f_c_time ? rowData.f_c_time + '天' : '';
                            
                            const rStartTimeCell = row.querySelector('td:nth-child(19)');
                            if (rStartTimeCell) rStartTimeCell.textContent = rowData.f_r_start_time || '';
                            
                            const rEndTimeCell = row.querySelector('td:nth-child(20)');
                            if (rEndTimeCell) rEndTimeCell.textContent = rowData.f_r_end_time || '';
                            
                            const rTimeCell = row.querySelector('td:nth-child(21)');
                            if (rTimeCell) rTimeCell.textContent = rowData.f_r_time ? rowData.f_r_time + '天' : '';
                            
                            // 再次强制重绘
                            refreshRowDisplay(row);
                        }
                    })
                    .catch(err => {
                        console.error('获取行数据失败:', err);
                    });
                }
            }, 100);
        }

        // 修改删除行函数
        async function deleteRow(rowId) {
            try {
                // 先隐藏右键菜单
                hideContextMenu();
                
                // 检查是否有子节点
                const hasChildren = document.querySelector(`#data-table tr[data-parent-id="${rowId}"]`);
                if (hasChildren) {
                    if (!confirm('此节点包含子节点，删除后将同时删除所有子节点。是否继续？')) {
                        return;
                    }
                }

                const loadingOverlay = document.getElementById('loading-overlay');
                loadingOverlay.style.display = 'flex';

                const response = await fetch(`${API_URL}/${TABLE_NAME}/${rowId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`删除失败: ${response.status}`);
                }

                // 使用局部更新刷新数据
                await fetchData(null, true);

            } catch (error) {
                console.error('Error deleting row:', error);
                showErrorMessage(`删除失败: ${error.message}`);
            } finally {
                const loadingOverlay = document.getElementById('loading-overlay');
                loadingOverlay.style.display = 'none';
            }
        }

        // 添加全部展开和全部收起功能
        function expandAllRows() {
            const tbody = document.querySelector('#data-table tbody');
            const rows = tbody.getElementsByTagName('tr');
            const icons = tbody.querySelectorAll('.toggle-icon');
            
            // 展开所有行
            Array.from(rows).forEach(row => {
                row.style.display = '';
            });
            
            // 更新所有图标为展开状态
            icons.forEach(icon => {
                if (icon.classList.contains('fa-caret-right')) {
                    icon.classList.remove('fa-caret-right');
                    icon.classList.add('fa-caret-down');
                }
            });
        }

        function collapseAllRows() {
            const tbody = document.querySelector('#data-table tbody');
            const rows = tbody.getElementsByTagName('tr');
            const icons = tbody.querySelectorAll('.toggle-icon');
            
            // 只显示顶级行（level=0），隐藏其他行
            Array.from(rows).forEach(row => {
                if (parseInt(row.dataset.level) === 0) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 更新所有图标为收起状态
            icons.forEach(icon => {
                if (icon.classList.contains('fa-caret-down')) {
                    icon.classList.remove('fa-caret-down');
                    icon.classList.add('fa-caret-right');
                }
            });
        }

        // 修改导出功能
        async function exportToExcel() {
            try {
                // 检查 XLSX 是否已加载
                if (typeof XLSX === 'undefined') {
                    throw new Error('Excel 处理库未加载，请刷新页面重试');
                }

                showDebugInfo('开始导出数据...');
                
                // 获取表格数据
                const table = document.getElementById('data-table');
                const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
                const rows = Array.from(table.querySelectorAll('tbody tr')).map(row => {
                    return Array.from(row.cells).map(cell => {
                        // 对于任务列，只获取文本内容，去除缩进和图标
                        if (cell.classList.contains('task-cell')) {
                            const taskName = cell.querySelector('.task-name');
                            return taskName ? taskName.textContent.trim() : '';
                        }
                        return cell.textContent.trim();
                    });
                });

                // 创建工作表数据
                const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows]);
                
                // 设置列宽
                const colWidths = [
                    { wch: 8 },  // 序号
                    { wch: 40 }, // 计划任务
                    { wch: 20 }, // 前置任务
                    { wch: 15 }, // 负责人
                    { wch: 12 }, // 重要程度
                    { wch: 12 }, // 任务状态
                    { wch: 12 }, // 是否风险
                    { wch: 12 }, // 风险等级
                    { wch: 30 }, // 风险问题
                    { wch: 30 }, // 风险解决途径
                    { wch: 30 }, // 风险解决方案
                    { wch: 15 }, // 计划开始时间
                    { wch: 15 }, // 计划结束时间
                    { wch: 12 }, // 计划工期
                    { wch: 30 }, // 备注
                    { wch: 15 }, // 变更后计划开始时间
                    { wch: 15 }, // 变更后计划结束时间
                    { wch: 12 }, // 变更后计划工期
                    { wch: 15 }, // 实际开始时间
                    { wch: 15 }, // 实际结束时间
                    { wch: 12 }, // 实际工期
                    { wch: 12 }, // 启动偏差
                    { wch: 12 }, // 完成偏差
                    { wch: 12 }  // 工期偏差值
                ];
                worksheet['!cols'] = colWidths;
                
                // 创建工作簿
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "任务列表");

                // 导出文件
                const date = new Date().toISOString().split('T')[0];
                XLSX.writeFile(workbook, `任务列表_${date}.xlsx`);
                
                showDebugInfo('数据导出成功');
            } catch (error) {
                console.error('Export error:', error);
                showErrorMessage(`导出失败: ${error.message}`);
            }
        }

        // 修改导入功能
        function formatExcelDate(value) {
            if (!value) return null;
            
            try {
                // 如果是数字（Excel序列号日期）
                if (typeof value === 'number') {
                    // Excel日期是从1900年1月1日开始的天数
                    // 需要减去Excel和JavaScript日期系统的差异（25569天）
                    const jsDate = new Date((value - 25569) * 86400 * 1000);
                    // 检查是否是有效日期
                    if (!isNaN(jsDate.getTime())) {
                        return jsDate.toISOString().split('T')[0];
                    }
                    return null;
                }
                
                // 如果是字符串，尝试解析
                if (typeof value === 'string') {
                    // 移除可能的多余空格
                    value = value.trim();
                    
                    // 尝试解析常见的日期格式
                    let date;
                    
                    // 检查是否是 "YYYY-MM-DD" 格式
                    if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
                        date = new Date(value);
                    }
                    // 检查是否是 "YYYY/MM/DD" 格式
                    else if (/^\d{4}\/\d{2}\/\d{2}$/.test(value)) {
                        const [year, month, day] = value.split('/');
                        date = new Date(year, month - 1, day);
                    }
                    // 检查是否是 "DD/MM/YYYY" 格式
                    else if (/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                        const [day, month, year] = value.split('/');
                        date = new Date(year, month - 1, day);
                    }
                    // 检查是否是 "YYYY.MM.DD" 格式
                    else if (/^\d{4}\.\d{2}\.\d{2}$/.test(value)) {
                        const [year, month, day] = value.split('.');
                        date = new Date(year, month - 1, day);
                    }
                    
                    // 检查日期是否有效
                    if (date && !isNaN(date.getTime())) {
                        return date.toISOString().split('T')[0];
                    }
                }
                
                return null;
            } catch (error) {
                console.warn('日期格式化失败:', error);
                return null;
            }
        }

        async function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) {
                showErrorMessage('请选择文件');
                return;
            }

            try {
                showDebugInfo('正在读取Excel文件...');
                const data = await file.arrayBuffer();
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // 转换为数组格式
                const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                // 跳过表头
                const dataRows = rows.slice(1);
                
                // 显示进度条
                const progressContainer = document.getElementById('progress-container');
                const progressBar = document.getElementById('progress-bar-fill');
                const progressText = document.getElementById('progress-text');
                progressContainer.style.display = 'block';
                
                // 存储新旧ID的映射关系
                const idMapping = new Map();
                
                // 按层级分组数据
                const levelGroups = new Map(); // 存储不同层级的数据
                
                // 第一遍扫描：按层级分组
                for (const row of dataRows) {
                    // 跳过空行
                    if (row.every(cell => !cell || cell.toString().trim() === '')) {
                        continue;
                    }
                    
                    const parentId = row[0]?.toString().trim() || null;
                    const id = row[1]?.toString().trim();
                    
                    if (!id) {
                        console.warn('跳过无效ID的行:', row);
                        continue;
                    }
                    
                    // 确定当前行的层级
                    let level = 0;
                    if (parentId === null || parentId === '' || parentId === 'null') {
                        level = 0; // 顶级节点
                    } else {
                        // 遍历已处理的数据找到父节点的层级
                        for (const [existingId, existingLevel] of levelGroups.entries()) {
                            if (existingId === parentId) {
                                level = existingLevel + 1;
                                break;
                            }
                        }
                    }
                    
                    // 将数据添加到对应层级组
                    if (!levelGroups.has(level)) {
                        levelGroups.set(level, []);
                    }
                    levelGroups.get(level).push({
                        originalId: id,
                        parentId: parentId,
                        data: {
                            f_task: row[2]?.toString().trim() || '',
                            f_pretask: row[3]?.toString().trim() || '',
                            f_assigner: row[4]?.toString().trim() || '',
                            f_degree: row[5]?.toString().trim() || '',
                            f_status: row[6]?.toString().trim() || '',
                            f_risk: row[7]?.toString().trim() || '',
                            f_risk_level: row[8]?.toString().trim() || '',
                            f_riskQ: row[9]?.toString().trim() || '',
                            f_riskS: row[10]?.toString().trim() || '',
                            f_riskSL: row[11]?.toString().trim() || '',
                            f_starttime: formatExcelDate(row[12]),
                            f_endtime: formatExcelDate(row[13]),
                            f_time: row[14] ? parseInt(row[14]) : null,
                            f_mark: row[15]?.toString().trim() || '',
                            f_c_start_time: formatExcelDate(row[16]),
                            f_c_end_time: formatExcelDate(row[17]),
                            f_c_time: row[18] ? parseInt(row[18]) : null,
                            f_r_start_time: formatExcelDate(row[19]),
                            f_r_end_time: formatExcelDate(row[20]),
                            f_r_time: row[21] ? parseInt(row[21]) : null,
                            f_s_dev: row[22] ? parseInt(row[22]) : null,
                            f_e_dev: row[23] ? parseInt(row[23]) : null,
                            f_dev: row[24] ? parseInt(row[24]) : null
                        }
                    });
                }
                
                // 获取最大层级
                const maxLevel = Math.max(...levelGroups.keys());
                let totalProcessed = 0;
                const totalToProcess = dataRows.length;
                
                // 按层级顺序处理数据
                for (let currentLevel = 0; currentLevel <= maxLevel; currentLevel++) {
                    const levelData = levelGroups.get(currentLevel) || [];
                    showDebugInfo(`正在处理第 ${currentLevel + 1} 级数据，共 ${levelData.length} 条记录...`);
                    
                    for (const item of levelData) {
                        try {
                            // 构建创建数据的对象
                            const createData = {
                                ...item.data
                            };
                            
                            // 如果不是顶级节点，查找父节点的新ID
                            if (item.parentId && item.parentId !== 'null') {
                                const newParentId = idMapping.get(item.parentId);
                                if (!newParentId) {
                                    console.warn(`找不到父节点的新ID: ${item.parentId}`);
                                    continue;
                                }
                                createData.parentId = newParentId;
                            }
                            
                            // 创建记录
                            const response = await fetch(`${API_URL}/${TABLE_NAME}`, {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${API_KEY}`,
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json'
                                },
                                body: JSON.stringify(createData)
                            });
                            
                            if (!response.ok) {
                                if (response.status === 401) {
                                    console.log('Token已过期，尝试重新登录...');
                                    const loginSuccess = await login();
                                    if (!loginSuccess) {
                                        throw new Error('重新登录失败');
                                    }
                                    // 重试当前记录
                                    continue;
                                }
                                throw new Error(`创建记录失败: ${response.status}`);
                            }
                            
                            const result = await response.json();
                            // 保存新旧ID的映射关系
                            idMapping.set(item.originalId, result.data.id);
                            
                            // 更新进度
                            totalProcessed++;
                            const progress = (totalProcessed / totalToProcess) * 100;
                            progressBar.style.width = `${progress}%`;
                            progressText.textContent = `正在导入数据... ${totalProcessed}/${totalToProcess}`;
                            
                            console.log(`成功导入记录: ${item.originalId} -> ${result.data.id}`);
                        } catch (error) {
                            console.error(`处理记录失败:`, error);
                            showErrorMessage(`导入记录失败: ${error.message}`);
                        }
                    }
                }
                
                showDebugInfo('数据导入完成');
                progressContainer.style.display = 'none';
                // 刷新数据显示
                await fetchData();
                
            } catch (error) {
                console.error('Import error:', error);
                showErrorMessage(`导入失败: ${error.message}`);
                document.getElementById('progress-container').style.display = 'none';
            }
        }

        // 添加日期格式化函数
        function formatDateForImport(value) {
            if (!value) return null;
            // 如果是日期对象，转换为ISO字符串
            if (value instanceof Date) {
                return value.toISOString().split('T')[0];
            }
            // 如果是数字（Excel日期），转换为日期字符串
            if (typeof value === 'number') {
                const date = new Date((value - 25569) * 86400 * 1000);
                return date.toISOString().split('T')[0];
            }
            // 如果是字符串，尝试解析
            if (typeof value === 'string') {
                const date = new Date(value);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            }
            return null;
        }

        // 添加滚动加载处理
        function handleScroll() {
            const container = document.querySelector('.table-container');
            if (!container) return;

            const { scrollTop, scrollHeight, clientHeight } = container;
            const scrollBottom = scrollHeight - scrollTop - clientHeight;

            // 当滚动到底部时加载下一页
            if (scrollBottom < 100 && currentPage < totalPages && !isFetching) {
                currentPage++;
                fetchData(null, currentPage);
            }
        }

        // 修改初始化函数
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // 显示加载动画
                const loadingOverlay = document.getElementById('loading-overlay');
                loadingOverlay.style.display = 'flex';

                // 预加载字体图标
                const fontAwesomeLink = document.createElement('link');
                fontAwesomeLink.rel = 'stylesheet';
                fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
                document.head.appendChild(fontAwesomeLink);

                // 预加载 XLSX 库
                const xlsxScript = document.createElement('script');
                xlsxScript.src = 'https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js';
                document.head.appendChild(xlsxScript);

                // 等待关键资源加载完成
                await Promise.all([
                    new Promise(resolve => fontAwesomeLink.onload = resolve),
                    new Promise(resolve => xlsxScript.onload = resolve)
                ]);

                // 获取数据
                await fetchData();

                // 隐藏加载动画
                loadingOverlay.style.display = 'none';

                // 显示表格容器
                const container = document.querySelector('.table-container');
                if (container) {
                    container.classList.add('loaded');
                }

                // 创建浮窗元素
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                document.body.appendChild(tooltip);

                // 添加鼠标悬停事件处理
                const table = document.getElementById('data-table');
                if (table) {
                    table.addEventListener('mouseover', (e) => {
                        const cell = e.target.closest('td');
                        if (!cell) return;

                        const columnIndex = Array.from(cell.parentElement.children).indexOf(cell);
                        // 只对特定列显示提示框
                        if ([3, 4, 8, 9, 10, 14].includes(columnIndex)) { // 对应第4、5、9、10、11、15列
                            const content = cell.textContent;
                            if (cell.scrollWidth > cell.clientWidth) {
                                tooltip.textContent = content;
                                tooltip.style.display = 'block';
                                
                                // 计算提示框位置，确保不会超出视口
                                const rect = cell.getBoundingClientRect();
                                const tooltipRect = tooltip.getBoundingClientRect();
                                const viewportWidth = window.innerWidth;
                                const viewportHeight = window.innerHeight;
                                
                                let top = e.clientY + 10;
                                let left = e.clientX + 10;
                                
                                // 如果提示框会超出视口右侧，则向左偏移
                                if (left + tooltipRect.width > viewportWidth) {
                                    left = viewportWidth - tooltipRect.width - 10;
                                }
                                
                                // 如果提示框会超出视口底部，则向上偏移
                                if (top + tooltipRect.height > viewportHeight) {
                                    top = e.clientY - tooltipRect.height - 10;
                                }
                                
                                tooltip.style.top = `${top}px`;
                                tooltip.style.left = `${left}px`;
                            }
                        }
                    });

                    table.addEventListener('mousemove', (e) => {
                        if (tooltip.style.display === 'block') {
                            const tooltipRect = tooltip.getBoundingClientRect();
                            const viewportWidth = window.innerWidth;
                            const viewportHeight = window.innerHeight;
                            
                            let top = e.clientY + 10;
                            let left = e.clientX + 10;
                            
                            // 如果提示框会超出视口右侧，则向左偏移
                            if (left + tooltipRect.width > viewportWidth) {
                                left = viewportWidth - tooltipRect.width - 10;
                            }
                            
                            // 如果提示框会超出视口底部，则向上偏移
                            if (top + tooltipRect.height > viewportHeight) {
                                top = e.clientY - tooltipRect.height - 10;
                            }
                            
                            tooltip.style.top = `${top}px`;
                            tooltip.style.left = `${left}px`;
                        }
                    });

                    table.addEventListener('mouseout', () => {
                        tooltip.style.display = 'none';
                    });
                }
            } catch (error) {
                console.error('Initialization error:', error);
                showErrorMessage(`初始化失败: ${error.message}`);
                // 隐藏加载动画
                document.getElementById('loading-overlay').style.display = 'none';
            }
        });

        // 修复设置单元格颜色的函数
        async function setCellColor(option, rowId) {
            try {
                // 获取颜色值
                const color = option.getAttribute('color');
                console.log('设置颜色:', color, '行ID:', rowId, '选项元素:', option);
                
                const row = document.querySelector(`#data-table tr[data-id="${rowId}"]`);
                if (!row) {
                    console.error('找不到行:', rowId);
                    hideContextMenu();
                    return;
                }
                
                // 清除之前的背景样式
                row.style.backgroundColor = '';
                row.style.cssText = row.style.cssText.replace(/background-color:[^;]+!important;?/gi, '');
                
                // 设置新的颜色属性
                if (color === 'none') {
                    // 清除颜色
                    row.dataset.cellColor = '';
                    console.log('清除行颜色:', rowId);
                } else {
                    // 设置新颜色
                    row.dataset.cellColor = color;
                    console.log('设置行颜色:', rowId, '为:', color);
                }
                
                // 隐藏上下文菜单
                hideContextMenu();
                
                // 保存颜色信息到服务器
                try {
                const updateData = {
                    f_cell_color: color === 'none' ? null : color
                };
                    
                    console.log('发送更新请求:', updateData);

                // 发送更新请求
                const response = await fetch(`${API_URL}/${TABLE_NAME}/${rowId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (!response.ok) {
                    throw new Error(`更新颜色失败: ${response.status}`);
                }

                    console.log('颜色更新成功:', color);
                } catch (error) {
                    console.error('设置颜色时发生错误:', error);
                    showErrorMessage(`设置颜色失败: ${error.message}`);
                    // 失败时还原颜色
                    row.dataset.cellColor = '';
                }
            } catch (error) {
                console.error('设置颜色时出错:', error);
                hideContextMenu();
                showErrorMessage(`设置颜色失败: ${error.message}`);
            }
        }

        // 添加获取颜色值的辅助函数
        function getColorValue(color) {
            switch (color) {
                case 'red': return '#ffebee';     // 更柔和的红色
                case 'yellow': return '#fff8e1';   // 更柔和的黄色
                case 'green': return '#e8f5e9';    // 更柔和的绿色
                case 'blue': return '#e3f2fd';     // 更柔和的蓝色
                case 'purple': return '#f3e5f5';   // 更柔和的紫色
                case 'none': return '';
                default: return '';
            }
        }

        // 修改构建树形数据的函数
        function buildTreeData(items, parentId = null, level = 0) {
            const startTime = window.performance.now();
            
            // 使用Map优化查找性能
            const itemsMap = new Map(items.map(item => [item.id, item]));
            const parentMap = new Map();
            
            // 一次性构建父子关系
            items.forEach(item => {
                const itemParentId = item.parentId?.toString() || null;
                if (!parentMap.has(itemParentId)) {
                    parentMap.set(itemParentId, []);
                }
                parentMap.get(itemParentId).push(item);
            });

            // 递归构建树形结构
            const buildTree = (currentParentId, currentLevel) => {
                const children = parentMap.get(currentParentId?.toString() || null) || [];
                return children.map(item => ({
                    ...item,
                    level: currentLevel,
                    children: buildTree(item.id, currentLevel + 1)
                }));
            };

            const result = buildTree(parentId, level);
            
            const endTime = window.performance.now();
            const renderTime = endTime - startTime;
            perfMetrics.renderCount++;
            perfMetrics.averageRenderTime = (perfMetrics.averageRenderTime * (perfMetrics.renderCount - 1) + renderTime) / perfMetrics.renderCount;
            
            console.log(`构建树形数据耗时: ${renderTime.toFixed(2)}ms, 平均耗时: ${perfMetrics.averageRenderTime.toFixed(2)}ms`);
            
            return result;
        }

        // 添加双击编辑功能
        function addDoubleClickEdit() {
            const table = document.getElementById('data-table');
            if (!table) return;

            let currentEditCell = null;
            let currentEditInput = null;
            let isEditing = false;
            let debounceTimer = null;

            // 创建输入框的缓存
            const inputCache = document.createElement('input');
            inputCache.className = 'edit-input';
            inputCache.style.display = 'none';
            document.body.appendChild(inputCache);

            // 创建样式元素
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .edit-input {
                    position: absolute;
                    border: 2px solid #007bff;
                    border-radius: 4px;
                    padding: 8px;
                    font-size: 14px;
                    outline: none;
                    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
                    background: white;
                    z-index: 1000;
                    transition: none;
                }
            `;
            document.head.appendChild(styleElement);

            // 优化的事件委托处理
            table.addEventListener('dblclick', (e) => {
                if (isEditing) return;
                
                const cell = e.target.closest('td');
                if (!cell || cell.classList.contains('serial-column')) return;

                const row = cell.closest('tr');
                const rowId = row.dataset.id;
                const columnName = cell.dataset.column;
                
                if (!rowId || !columnName) return;

                startEdit(cell, rowId, columnName);
            });

            // 优化输入框创建
            function startEdit(cell, rowId, columnName) {
                if (isEditing) return;
                isEditing = true;

                // 使用缓存的输入框
                const input = inputCache.cloneNode(true);
                input.value = cell.textContent.trim();
                input.dataset.rowId = rowId;
                input.dataset.column = columnName;

                // 设置输入框位置和大小
                const rect = cell.getBoundingClientRect();
                input.style.left = `${rect.left}px`;
                input.style.top = `${rect.top}px`;
                input.style.width = `${rect.width}px`;
                input.style.height = `${rect.height}px`;
                input.style.display = 'block';
                input.style.transition = 'none';

                document.body.appendChild(input);
                input.focus();
                input.select();

                currentEditCell = cell;
                currentEditInput = input;

                // 优化事件监听
                const handleKeyDown = (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        saveEdit();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        cancelEdit();
                    }
                };

                const handleBlur = () => {
                    if (debounceTimer) clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(saveEdit, 100);
                };

                input.addEventListener('keydown', handleKeyDown);
                input.addEventListener('blur', handleBlur);
            }

            // 优化保存编辑
            async function saveEdit() {
                if (!currentEditInput || !currentEditCell) return;

                const rowId = currentEditInput.dataset.rowId;
                const columnName = currentEditInput.dataset.column;
                const newValue = currentEditInput.value.trim();

                try {
                    const response = await fetch(`${API_URL}/${TABLE_NAME}/${rowId}`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${API_KEY}`
                        },
                        body: JSON.stringify({
                            [columnName]: newValue
                        })
                    });

                    if (!response.ok) {
                        throw new Error('更新失败');
                    }

                    currentEditCell.textContent = newValue;
                } catch (error) {
                    console.error('更新数据失败:', error);
                    showErrorMessage('更新数据失败，请重试');
                } finally {
                    cancelEdit();
                }
            }

            // 优化取消编辑
            function cancelEdit() {
                if (currentEditInput) {
                    currentEditInput.style.display = 'none';
                    currentEditInput = null;
                }
                currentEditCell = null;
                isEditing = false;
            }

            // 清理函数
            return () => {
                if (currentEditInput) {
                    currentEditInput.remove();
                }
                styleElement.remove();
                inputCache.remove();
            };
        }

        // 在初始化时调用
        document.addEventListener('DOMContentLoaded', () => {
            const cleanupEdit = addDoubleClickEdit();
        });

        // 修改前置任务点击处理函数
        function handlePretaskClick(event, span) {
            // 阻止事件冒泡，避免触发双击编辑
            event.stopPropagation();
            
            // 如果正在编辑，不执行跳转
            if (span.closest('.editable').classList.contains('editing')) {
                return;
            }
            
            const pretaskNumber = span.textContent.trim();
            if (!pretaskNumber) return;
            
            // 查找包含该序号的单元格
            const rows = document.querySelectorAll('#data-table tbody tr');
            for (const row of rows) {
                const numberCell = row.querySelector('td:first-child');
                if (numberCell && numberCell.textContent.trim() === pretaskNumber) {
                    // 滚动到目标行
                    row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // 高亮显示目标行
                    row.classList.add('selected');
                    setTimeout(() => row.classList.remove('selected'), 2000);
                    break;
                }
            }
        }

        // 表格转换功能
        let convertedData = null;

        // 处理表格转换文件选择
        async function handleConvertFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                showLoadingOverlay('正在读取文件...');
                const data = await file.arrayBuffer();
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // 转换为数组格式
                showLoadingOverlay('正在处理数据...');
                const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                // 转换数据
                convertedData = convertToParentChildFormat(rows);
                
                // 直接导出
                showLoadingOverlay('正在导出文件...');
                await exportConvertedToExcel();
                
                showConversionMessage('表格转换成功！已自动导出为Excel文件。');
                hideLoadingOverlay();
            } catch (error) {
                console.error('Error processing file:', error);
                alert('处理文件时出错：' + error.message);
                hideLoadingOverlay();
            } finally {
                // 重置文件输入，允许再次选择同一文件
                document.getElementById('convertFileInput').value = '';
            }
        }

        // 转换为父子格式
        function convertToParentChildFormat(rows) {
            if (!rows || rows.length < 2) return [];

            const headers = rows[0];
            const dataRows = rows.slice(1);
            const idMap = new Map();
            let currentId = 1;

            // 创建新的表头
            const newHeaders = ['父记录ID', 'ID', ...headers.slice(1)];

            // 处理数据行
            const convertedRows = dataRows.map(row => {
                if (!row[0]) return null; // 跳过空行

                const serialNumber = row[0].toString().trim();
                const parts = serialNumber.split('.');
                const parentSerial = parts.slice(0, -1).join('.');
                const parentId = parentSerial ? idMap.get(parentSerial) : null;

                // 存储当前序号和ID的映射
                idMap.set(serialNumber, currentId);

                // 创建新行数据
                const newRow = [parentId, currentId, ...row.slice(1)];
                currentId++;

                return newRow;
            }).filter(row => row !== null);

            return [newHeaders, ...convertedRows];
        }

        // 导出转换后的Excel
        async function exportConvertedToExcel() {
            if (!convertedData) return;

            try {
                // 创建工作表
                const ws = XLSX.utils.aoa_to_sheet(convertedData);
                
                // 创建工作簿
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "转换结果");
                
                // 导出文件
                const date = new Date().toISOString().split('T')[0];
                XLSX.writeFile(wb, `转换结果_${date}.xlsx`);
                
                return new Promise(resolve => setTimeout(resolve, 500)); // 给予写入时间
            } catch (error) {
                console.error('Error exporting file:', error);
                alert('导出文件时出错：' + error.message);
                throw error;
            }
        }

        // 显示加载层
        function showLoadingOverlay(text = '处理中...') {
            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingText = document.querySelector('#loading-overlay .loading-text');
            if (loadingText) loadingText.textContent = text;
            loadingOverlay.style.display = 'flex';
        }

        // 隐藏加载层
        function hideLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'none';
        }
        
        // 显示转换成功消息
        function showConversionMessage(message) {
            // 检查是否已有消息元素
            let messageEl = document.querySelector('.conversion-message');
            
            // 如果没有则创建一个
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.className = 'conversion-message';
                messageEl.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>${message}</span>
                `;
                document.body.appendChild(messageEl);
            } else {
                // 更新现有消息
                messageEl.querySelector('span').textContent = message;
            }
            
            // 显示消息
            setTimeout(() => {
                messageEl.classList.add('show');
            }, 100);
            
            // 5秒后自动隐藏
            setTimeout(() => {
                messageEl.classList.remove('show');
                // 动画完成后删除元素
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, 5000);
        }

        // 添加验证函数，检查子任务的时间范围是否在父任务的时间范围内
        function validateTimeRange(row, fieldName, newValue) {
            // 获取父任务ID
            const parentId = row.dataset.parentId;
            if (!parentId || parentId === 'null') return true; // 没有父任务，不需要验证
            
            // 获取父任务行
            const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
            if (!parentRow) return true; // 找不到父任务，不验证
            
            // 判断修改的是哪类时间字段
            let prefix = '';
            if (fieldName.includes('c_')) {
                prefix = 'c_';
            } else if (fieldName.includes('r_')) {
                prefix = 'r_';
            }
            
            // 获取父任务的开始和结束时间
            const parentStartField = `f_${prefix}starttime`;
            const parentEndField = `f_${prefix}endtime`;
            
            let parentStartIndex, parentEndIndex;
            
            // 根据前缀确定父任务时间字段在表格中的位置
            if (prefix === '') {
                parentStartIndex = 12; // 计划开始时间列索引
                parentEndIndex = 13;   // 计划结束时间列索引
            } else if (prefix === 'c_') {
                parentStartIndex = 16; // 变更后计划开始时间列索引
                parentEndIndex = 17;   // 变更后计划结束时间列索引
            } else if (prefix === 'r_') {
                parentStartIndex = 19; // 实际开始时间列索引
                parentEndIndex = 20;   // 实际结束时间列索引
            }
            
            const parentStartTime = parentRow.querySelector(`td:nth-child(${parentStartIndex})`)?.textContent;
            const parentEndTime = parentRow.querySelector(`td:nth-child(${parentEndIndex})`)?.textContent;
            
            // 如果父任务没有设置时间范围，允许子任务任意设置
            if (!parentStartTime || !parentEndTime) return true;
            
            // 转换为日期对象进行比较
            const parentStart = new Date(parentStartTime);
            const parentEnd = new Date(parentEndTime);
            const newDate = new Date(newValue);
            
            // 验证新值是否在父任务范围内
            if (fieldName.includes('starttime')) {
                // 检查开始时间是否不早于父任务开始时间
                if (newDate < parentStart) {
                    showErrorMessage(`子任务的开始时间不能早于父任务的开始时间 (${parentStartTime})`);
                    return false;
                }
            } else if (fieldName.includes('endtime')) {
                // 检查结束时间是否不晚于父任务结束时间
                if (newDate > parentEnd) {
                    showErrorMessage(`子任务的结束时间不能晚于父任务的结束时间 (${parentEndTime})`);
                    return false;
                }
            }
            
            return true;
        }

        // 在验证失败时恢复原始值
        function restoreOriginalValue(input, cell, fieldName, originalValue) {
            if (fieldName === 'f_task') {
                // 特殊处理任务名称
                const taskName = input.closest('.task-name');
                if (taskName) {
                    taskName.innerHTML = '';
                    taskName.textContent = originalValue || '';
                }
            } else if (fieldName === 'f_pretask') {
                // 特殊处理前置任务
                const pretaskContent = input.closest('.pretask-content');
                if (pretaskContent) {
                    pretaskContent.innerHTML = '';
                    pretaskContent.textContent = originalValue || '';
                }
            } else if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                // 工期字段
                cell.textContent = originalValue ? originalValue + '天' : '';
            } else {
                // 其他字段
                cell.textContent = originalValue || '';
            }
            
            cell.classList.remove('editing');
        }
        
        // 验证计算出的时间是否在父任务范围内
        function validateCalculatedTime(row, fieldName, calculatedValue) {
            if (!row || !fieldName || !calculatedValue) {
                return { valid: true };
            }
            
            // 获取父任务ID
            const parentId = row.dataset.parentId;
            if (!parentId || parentId === 'null') {
                return { valid: true }; // 没有父任务，不需要验证
            }
            
            // 获取父任务行
            const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
            if (!parentRow) {
                return { valid: true }; // 找不到父任务，不验证
            }
            
            // 判断是哪类时间字段
            let prefix = '';
            if (fieldName.includes('c_')) {
                prefix = 'c_';
            } else if (fieldName.includes('r_')) {
                prefix = 'r_';
            }
            
            // 确定是开始时间还是结束时间
            const isStartTime = fieldName.includes('start');
            
            // 获取父任务的对应时间范围
            let parentStartIndex, parentEndIndex;
            
            // 根据前缀确定父任务时间字段在表格中的位置
            if (prefix === '') {
                parentStartIndex = 12; // 计划开始时间列索引
                parentEndIndex = 13;   // 计划结束时间列索引
            } else if (prefix === 'c_') {
                parentStartIndex = 16; // 变更后计划开始时间列索引
                parentEndIndex = 17;   // 变更后计划结束时间列索引
            } else if (prefix === 'r_') {
                parentStartIndex = 19; // 实际开始时间列索引
                parentEndIndex = 20;   // 实际结束时间列索引
            }
            
            const parentStartTime = parentRow.querySelector(`td:nth-child(${parentStartIndex})`)?.textContent;
            const parentEndTime = parentRow.querySelector(`td:nth-child(${parentEndIndex})`)?.textContent;
            
            // 如果父任务没有设置时间范围，允许子任务任意设置
            if (!parentStartTime || !parentEndTime) {
                return { valid: true };
            }
            
            // 执行验证
            if (isStartTime) {
                // 验证开始时间：子任务开始时间不能早于父任务开始时间
                if (new Date(calculatedValue) < new Date(parentStartTime)) {
                    return {
                        valid: false,
                        message: `计算出的开始时间 ${calculatedValue} 早于父任务的开始时间 ${parentStartTime}`
                    };
                }
            } else {
                // 验证结束时间：子任务结束时间不能晚于父任务结束时间
                if (new Date(calculatedValue) > new Date(parentEndTime)) {
                    return {
                        valid: false,
                        message: `计算出的结束时间 ${calculatedValue} 晚于父任务的结束时间 ${parentEndTime}`
                    };
                }
            }
            
            return { valid: true };
        }

        // 添加一个辅助函数来恢复原始值
        function restoreOriginalValue(input, cell, fieldName, originalValue) {
            // 移除编辑状态
            cell.classList.remove('editing');
            
            // 根据字段类型处理原始值的显示
            if (fieldName === 'f_pretask') {
                const pretaskContent = cell.querySelector('.pretask-content');
                if (pretaskContent) {
                    pretaskContent.innerHTML = originalValue.split('、').map(num => 
                        `<span class="pretask-number" onclick="handlePretaskClick(event, this)">${num.replace(/\s+/g, '').trim()}</span>`
                    ).join('、');
                } else {
                    cell.innerHTML = `<span class="pretask-content">${originalValue}</span>`;
                }
            } else if (fieldName === 'f_task') {
                const taskName = cell.querySelector('.task-name');
                if (taskName) {
                    taskName.textContent = originalValue;
                } else {
                    // 保持任务行的缩进和图标
                    const level = cell.closest('tr').dataset.level || 0;
                    const indent = '&nbsp;'.repeat(level * 4);
                    const hasChildren = cell.closest('tr').dataset.hasChildren === 'true';
                    const toggleIcon = hasChildren ? 
                        '<i class="fas fa-caret-down toggle-icon"></i>' : 
                        '<i class="fas fa-minus toggle-icon" style="visibility: hidden;"></i>';
                    
                    cell.innerHTML = `${indent}${toggleIcon}<span class="task-name">${originalValue}</span>`;
                }
            } else if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                // 工期字段需要添加"天"字
                cell.textContent = originalValue ? originalValue + '天' : '';
            } else if (fieldName.includes('starttime') || fieldName.includes('endtime')) {
                // 日期字段直接显示原始值
                cell.textContent = originalValue || '';
            } else {
                cell.textContent = originalValue || '';
            }
            
            // 直接调用表格刷新函数
            const row = cell.closest('tr');
            if (row) {
                refreshRowDisplay(row);
            }
        }

        // 用于刷新整行显示的函数
        function refreshRowDisplay(row) {
            // 获取所有需要处理的时间相关字段及其索引位置
            const timeFields = [
                { index: 12, field: 'f_starttime' }, // 计划开始时间
                { index: 13, field: 'f_endtime' },   // 计划结束时间
                { index: 14, field: 'f_time' },      // 计划工期
                { index: 16, field: 'f_c_start_time' }, // 变更后计划开始时间
                { index: 17, field: 'f_c_end_time' },   // 变更后计划结束时间
                { index: 18, field: 'f_c_time' },       // 变更后计划工期
                { index: 19, field: 'f_r_start_time' }, // 实际开始时间
                { index: 20, field: 'f_r_end_time' },   // 实际结束时间
                { index: 21, field: 'f_r_time' }        // 实际工期
            ];

            // 先从DOM中移除整行再放回，强制重绘
            const parent = row.parentNode;
            const next = row.nextSibling;
            if (parent) {
                parent.removeChild(row);
                setTimeout(() => {
                    parent.insertBefore(row, next);
                    
                    // 然后强制重绘每个单元格
                    requestAnimationFrame(() => {
                        // 遍历所有时间相关字段
                        timeFields.forEach(({index, field}) => {
                            const cell = row.querySelector(`td:nth-child(${index})`);
                            if (!cell) return;
                            
                            // 确保单元格不在编辑状态
                            if (cell.classList.contains('editing')) {
                                cell.classList.remove('editing');
                            }
                            
                            // 保存当前内容
                            let content = cell.textContent;
                            
                            // 根据字段类型设置内容
                            if (field.includes('time') && !field.endsWith('time')) {
                                // 工期字段
                                const value = content.replace('天', '').trim();
                                if (value) {
                                    content = value + '天';
                                } else {
                                    content = '';
                                }
                            }
                            
                            // 创建新元素替换现有单元格内容
                            const tempDiv = document.createElement('div');
                            tempDiv.textContent = content;
                            cell.innerHTML = '';
                            cell.appendChild(tempDiv);
                            
                            // 使用强制重绘技术
                            cell.style.display = 'none';
                            cell.offsetHeight; // 强制浏览器重新计算布局
                            cell.style.display = '';
                        });
                        
                        // 额外措施：变更表格class强制更新
                        row.classList.add('refreshed');
                        setTimeout(() => {
                            row.classList.remove('refreshed');
                        }, 50);
                    });
                }, 10);
            }
        }

        // 添加validateInput函数，用于输入验证
        function validateInput(fieldName, value) {
            // 根据字段类型进行验证
            switch (fieldName) {
                case 'f_task':
                    return value.trim().length > 0;
                case 'f_starttime':
                case 'f_endtime':
                case 'f_c_start_time':
                case 'f_c_end_time':
                case 'f_r_start_time':
                case 'f_r_end_time':
                    return !value || /^\d{4}-\d{2}-\d{2}$/.test(value);
                case 'f_time':
                case 'f_c_time':
                case 'f_r_time':
                case 'f_s_dev':
                case 'f_e_dev':
                case 'f_dev':
                    return !value || !isNaN(value);
                default:
                    return true;
            }
        }

        // 添加更新记录的函数
        async function updateRecord(id, data) {
            try {
                showDebugInfo(`开始更新记录 ID: ${id}`);
                
                // 获取当前行的数据
                const currentRow = document.querySelector(`#data-table tr[data-id="${id}"]`);
                if (!currentRow) return;
                
                // 计划开始时间、计划结束时间和计划工期的计算
                if (data.f_starttime || data.f_endtime || data.f_time) {
                    const startTime = data.f_starttime || currentRow.querySelector('td:nth-child(12)')?.textContent;
                    const endTime = data.f_endtime || currentRow.querySelector('td:nth-child(13)')?.textContent;
                    let duration = data.f_time ? data.f_time.replace('天', '') : currentRow.querySelector('td:nth-child(14)')?.textContent?.replace('天', '');
                    
                    // 如果修改的是工期
                    if (data.f_time) {
                        if (startTime) {
                            // 有开始日期和工期，计算结束日期
                            data.f_endtime = calculateEndDate(startTime, data.f_time);
                        } else if (endTime) {
                            // 有结束日期和工期，计算开始日期
                            data.f_starttime = calculateStartDate(endTime, data.f_time);
                        }
                    } 
                    // 如果修改的是开始日期
                    else if (data.f_starttime) {
                        if (endTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_time = calculateDuration(data.f_starttime, endTime);
                        }
                        // 不再根据工期计算结束日期
                    }
                    // 如果修改的是结束日期
                    else if (data.f_endtime) {
                        if (startTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_time = calculateDuration(startTime, data.f_endtime);
                        }
                    }
                }
                
                // 变更后计划开始时间、变更后计划结束时间和变更后计划工期的计算
                if (data.f_c_start_time || data.f_c_end_time || data.f_c_time) {
                    const startTime = data.f_c_start_time || currentRow.querySelector('td:nth-child(16)')?.textContent;
                    const endTime = data.f_c_end_time || currentRow.querySelector('td:nth-child(17)')?.textContent;
                    let duration = data.f_c_time ? data.f_c_time.replace('天', '') : currentRow.querySelector('td:nth-child(18)')?.textContent?.replace('天', '');
                    
                    // 如果修改的是工期
                    if (data.f_c_time) {
                        if (startTime) {
                            // 有开始日期和工期，计算结束日期
                            data.f_c_end_time = calculateEndDate(startTime, data.f_c_time);
                        } else if (endTime) {
                            // 有结束日期和工期，计算开始日期
                            data.f_c_start_time = calculateStartDate(endTime, data.f_c_time);
                        }
                    } 
                    // 如果修改的是开始日期
                    else if (data.f_c_start_time) {
                        if (endTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_c_time = calculateDuration(data.f_c_start_time, endTime);
                        }
                        // 不再根据工期计算结束日期
                    }
                    // 如果修改的是结束日期
                    else if (data.f_c_end_time) {
                        if (startTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_c_time = calculateDuration(startTime, data.f_c_end_time);
                        }
                    }
                }
                
                // 实际开始时间、实际结束时间和实际工期的计算
                if (data.f_r_start_time || data.f_r_end_time || data.f_r_time) {
                    const startTime = data.f_r_start_time || currentRow.querySelector('td:nth-child(19)')?.textContent;
                    const endTime = data.f_r_end_time || currentRow.querySelector('td:nth-child(20)')?.textContent;
                    let duration = data.f_r_time ? data.f_r_time.replace('天', '') : currentRow.querySelector('td:nth-child(21)')?.textContent?.replace('天', '');
                    
                    // 如果修改的是工期
                    if (data.f_r_time) {
                        if (startTime) {
                            // 有开始日期和工期，计算结束日期
                            data.f_r_end_time = calculateEndDate(startTime, data.f_r_time);
                        } else if (endTime) {
                            // 有结束日期和工期，计算开始日期
                            data.f_r_start_time = calculateStartDate(endTime, data.f_r_time);
                        }
                    } 
                    // 如果修改的是开始日期
                    else if (data.f_r_start_time) {
                        if (endTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_r_time = calculateDuration(data.f_r_start_time, endTime);
                        }
                    }
                    // 如果修改的是结束日期
                    else if (data.f_r_end_time) {
                        if (startTime) {
                            // 有开始日期和结束日期，计算工期
                            data.f_r_time = calculateDuration(startTime, data.f_r_end_time);
                        }
                    }
                }

                const response = await fetch(`${API_URL}/${TABLE_NAME}/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`更新失败: ${response.status}`);
                }

                showDebugInfo('记录更新成功');
                
                // 更新表格显示
                const updatedRow = document.querySelector(`#data-table tr[data-id="${id}"]`);
                if (updatedRow) {
                    // 更新直接修改的字段
                    for (const [field, value] of Object.entries(data)) {
                        let cell;
                        switch (field) {
                            case 'f_time':
                                cell = updatedRow.querySelector('td:nth-child(14)');
                                break;
                            case 'f_c_time':
                                cell = updatedRow.querySelector('td:nth-child(18)');
                                break;
                            case 'f_r_time':
                                cell = updatedRow.querySelector('td:nth-child(21)');
                                break;
                            case 'f_starttime':
                                cell = updatedRow.querySelector('td:nth-child(12)');
                                break;
                            case 'f_endtime':
                                cell = updatedRow.querySelector('td:nth-child(13)');
                                break;
                            case 'f_c_start_time':
                                cell = updatedRow.querySelector('td:nth-child(16)');
                                break;
                            case 'f_c_end_time':
                                cell = updatedRow.querySelector('td:nth-child(17)');
                                break;
                            case 'f_r_start_time':
                                cell = updatedRow.querySelector('td:nth-child(19)');
                                break;
                            case 'f_r_end_time':
                                cell = updatedRow.querySelector('td:nth-child(20)');
                                break;
                            case 'f_status':
                                cell = updatedRow.querySelector('td:nth-child(6)');
                                // 更新状态数据属性
                                updatedRow.dataset.status = value || '';
                                // 更新行背景色
                                updateRowBackgroundColor(updatedRow);
                                break;
                            case 'f_cell_color':
                                // 更新颜色数据属性
                                updatedRow.dataset.cellColor = value || '';
                                // 更新行背景色
                                updateRowBackgroundColor(updatedRow);
                                break;
                            default:
                                continue;
                        }
                        if (cell) {
                            if (field === 'f_time' || field === 'f_c_time' || field === 'f_r_time') {
                                cell.textContent = value ? value + '天' : '';
                            } else {
                                cell.textContent = value || '';
                            }
                        }
                    }
                    
                    // 使用我们的刷新函数确保显示更新
                    refreshRowDisplay(updatedRow);
                }
                
                return response.json();
            } catch (error) {
                console.error('Error updating record:', error);
                showDebugInfo(`更新失败: ${error.message}`, 'error');
                showErrorMessage(`更新失败: ${error.message}`);
                throw error;
            }
        }

        // 添加显示错误消息的函数
        function showErrorMessage(message, duration = 3000) {
            // 创建错误消息元素
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            errorDiv.style.position = 'fixed';
            errorDiv.style.top = '20px';
            errorDiv.style.left = '50%';
            errorDiv.style.transform = 'translateX(-50%)';
            errorDiv.style.backgroundColor = '#f44336';
            errorDiv.style.color = 'white';
            errorDiv.style.padding = '10px 20px';
            errorDiv.style.borderRadius = '4px';
            errorDiv.style.zIndex = '1000';
            errorDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
            errorDiv.style.animation = 'fadeIn 0.3s';
            
            // 添加到文档
            document.body.appendChild(errorDiv);
            
            // 添加CSS动画
            if (!document.getElementById('error-message-style')) {
                const style = document.createElement('style');
                style.id = 'error-message-style';
                style.textContent = `
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translate(-50%, -20px); }
                        to { opacity: 1; transform: translate(-50%, 0); }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; transform: translate(-50%, 0); }
                        to { opacity: 0; transform: translate(-50%, -20px); }
                    }
                    .error-message.hiding {
                        animation: fadeOut 0.3s forwards;
                    }
                    .error-field {
                        animation: shake 0.5s;
                        border-color: #f44336 !important;
                    }
                    @keyframes shake {
                        0%, 100% { transform: translateX(0); }
                        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                        20%, 40%, 60%, 80% { transform: translateX(5px); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 定时移除
            setTimeout(() => {
                errorDiv.classList.add('hiding');
                setTimeout(() => {
                    document.body.removeChild(errorDiv);
                }, 300);
            }, duration);
            
            // 如果有当前编辑的元素，添加抖动效果
            const currentEditingCell = document.querySelector('.editing-cell');
            if (currentEditingCell) {
                const input = currentEditingCell.querySelector('input');
                if (input) {
                    input.classList.add('error-field');
                    setTimeout(() => {
                        input.classList.remove('error-field');
                    }, 500);
                }
            }
        }

        // 添加日期计算相关函数
        function calculateDuration(startDate, endDate) {
            if (!startDate || !endDate) return '';
            
            try {
                const start = new Date(startDate);
                const end = new Date(endDate);
                
                // 检查日期是否有效
                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                    showDebugInfo('计算工期失败：无效的日期格式', 'error');
                    return '';
                }
                
                // 计算相差的毫秒数
                const diffTime = Math.abs(end - start);
                // 转换为天数并加1（包含开始日和结束日）
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                
                showDebugInfo(`计算工期: ${startDate} 至 ${endDate} = ${diffDays}天`);
                return diffDays.toString();
            } catch (error) {
                showDebugInfo(`计算工期发生错误: ${error.message}`, 'error');
                return '';
            }
        }

        function calculateEndDate(startDate, duration) {
            if (!startDate || !duration) return '';
            
            try {
                const start = new Date(startDate);
                
                // 检查日期是否有效
                if (isNaN(start.getTime())) {
                    showDebugInfo('计算结束日期失败：无效的开始日期格式', 'error');
                    return '';
                }
                
                // 将工期转换为数字（去掉可能的"天"单位）
                let days = parseInt(duration.toString().replace('天', ''));
                if (isNaN(days)) {
                    showDebugInfo('计算结束日期失败：无效的工期格式', 'error');
                    return '';
                }
                
                // 减去1（因为包含开始日和结束日）
                days = days - 1;
                
                // 计算结束日期
                const end = new Date(start);
                end.setDate(start.getDate() + days);
                
                // 格式化为YYYY-MM-DD
                const year = end.getFullYear();
                const month = String(end.getMonth() + 1).padStart(2, '0');
                const day = String(end.getDate()).padStart(2, '0');
                const result = `${year}-${month}-${day}`;
                
                showDebugInfo(`计算结束日期: ${startDate} + ${duration}天 = ${result}`);
                return result;
            } catch (error) {
                showDebugInfo(`计算结束日期发生错误: ${error.message}`, 'error');
                return '';
            }
        }

        function calculateStartDate(endDate, duration) {
            if (!endDate || !duration) return '';
            
            try {
                const end = new Date(endDate);
                
                // 检查日期是否有效
                if (isNaN(end.getTime())) {
                    showDebugInfo('计算开始日期失败：无效的结束日期格式', 'error');
                    return '';
                }
                
                // 将工期转换为数字（去掉可能的"天"单位）
                let days = parseInt(duration.toString().replace('天', ''));
                if (isNaN(days)) {
                    showDebugInfo('计算开始日期失败：无效的工期格式', 'error');
                    return '';
                }
                
                // 减去1（因为包含开始日和结束日）
                days = days - 1;
                
                // 计算开始日期
                const start = new Date(end);
                start.setDate(end.getDate() - days);
                
                // 格式化为YYYY-MM-DD
                const year = start.getFullYear();
                const month = String(start.getMonth() + 1).padStart(2, '0');
                const day = String(start.getDate()).padStart(2, '0');
                const result = `${year}-${month}-${day}`;
                
                showDebugInfo(`计算开始日期: ${endDate} - ${duration}天 = ${result}`);
                return result;
            } catch (error) {
                showDebugInfo(`计算开始日期发生错误: ${error.message}`, 'error');
                return '';
            }
        }

        // 添加右键菜单功能
        function showContextMenu(e, rowId, position) {
            // 如果已有菜单，先移除
            hideContextMenu();
            
            // 获取行数据
            const row = document.querySelector(`#data-table tr[data-id="${rowId}"]`);
            if (!row) return;
            
            // 获取当前行的颜色状态
            const currentColor = row.dataset.cellColor || '';
            
            // 创建右键菜单元素
            const menu = document.createElement('div');
            menu.className = 'context-menu';
            
            // 添加菜单项 - 完全替换之前的菜单HTML
            menu.innerHTML = `
                <div onclick="addChild('${rowId}')">
                    <i class="fas fa-plus-circle"></i> 添加子任务
                </div>
                <div onclick="addSibling('${rowId}')">
                    <i class="fas fa-arrows-alt-h"></i> 添加同级任务
                </div>
                <div class="menu-divider"></div>
                <div class="color-option ${currentColor === 'red' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="red">
                    <i class="fas fa-pencil-alt" style="color: #f44336;"></i> 红色标记
                </div>
                <div class="color-option ${currentColor === 'yellow' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="yellow">
                    <i class="fas fa-pencil-alt" style="color: #ffc107;"></i> 黄色标记
                </div>
                <div class="color-option ${currentColor === 'green' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="green">
                    <i class="fas fa-pencil-alt" style="color: #4caf50;"></i> 绿色标记
                </div>
                <div class="color-option ${currentColor === 'blue' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="blue">
                    <i class="fas fa-pencil-alt" style="color: #2196f3;"></i> 蓝色标记
                </div>
                <div class="color-option ${currentColor === 'purple' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="purple">
                    <i class="fas fa-pencil-alt" style="color: #9c27b0;"></i> 紫色标记
                </div>
                <div class="color-option ${currentColor === '' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="none">
                    <i class="fas fa-eraser"></i> 清除标记
                </div>
                <div class="menu-divider"></div>
                <div class="delete-option" onclick="deleteRow('${rowId}')">
                    <i class="fas fa-trash-alt"></i> 删除
                </div>
            `;
            
            // 先添加到DOM但隐藏，以便获取尺寸
            menu.style.visibility = 'hidden';
            document.body.appendChild(menu);
            
            // 保存菜单引用
            contextMenu = menu;
            
            // 获取菜单尺寸和视口尺寸
            const menuRect = menu.getBoundingClientRect();
            const menuWidth = menuRect.width;
            const menuHeight = menuRect.height;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            // 使用传入的精确位置
            let top = position.y;
            let left = position.x;
            
            // 确保菜单不会超出右侧边界
            if (left + menuWidth > viewportWidth) {
                left = Math.max(0, viewportWidth - menuWidth);
            }
            
            // 确保菜单不会超出底部边界
            if (top + menuHeight > viewportHeight) {
                top = Math.max(0, viewportHeight - menuHeight);
            }
            
            // 设置最终位置并显示菜单
            menu.style.top = `${top}px`;
            menu.style.left = `${left}px`;
            menu.style.visibility = 'visible';
            
            console.log('显示右键菜单在点击位置:', {top, left});
        }

        // 隐藏右键菜单
        function hideContextMenu() {
            if (contextMenu) {
                contextMenu.remove();
                contextMenu = null;
            }
        }

        // 获取颜色值
        function getColorValue(color) {
            switch (color) {
                case 'red': return '#ffebee';
                case 'yellow': return '#fff8e1';
                case 'green': return '#e8f5e9';
                case 'blue': return '#e3f2fd';
                case 'purple': return '#f3e5f5';
                case 'none': case null: return '';
                default: return '';
            }
        }

        // 添加子任务
        async function addChild(parentId) {
            try {
                hideContextMenu();
                
                const parentRow = document.querySelector(`#data-table tr[data-id="${parentId}"]`);
                if (!parentRow) return;
                
                const parentLevel = parseInt(parentRow.dataset.level);
                
                // 准备新记录数据
                const newRecord = {
                    f_content: "新子任务",
                    f_description: "",
                    f_solution: "",
                    parentId: parentId,
                    f_level: parentLevel + 1
                };
                
                // 继承父任务的时间范围
                const parentStartTime = parentRow.querySelector('td:nth-child(12)')?.textContent;
                const parentEndTime = parentRow.querySelector('td:nth-child(13)')?.textContent;
                
                if (parentStartTime) {
                    newRecord.f_starttime = parentStartTime;
                }
                
                if (parentEndTime) {
                    newRecord.f_endtime = parentEndTime;
                }
                
                if (parentStartTime && parentEndTime) {
                    newRecord.f_time = calculateDuration(parentStartTime, parentEndTime);
                }
                
                // 创建新记录
                const response = await fetch(`${API_URL}/${TABLE_NAME}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(newRecord)
                });
                
                if (!response.ok) {
                    throw new Error(`创建子任务失败: ${response.status}`);
                }
                
                // 使用局部更新刷新数据
                await fetchData(null, true);
                
                // 确保父节点展开
                const toggleIcon = parentRow.querySelector('.toggle-icon');
                if (toggleIcon && toggleIcon.classList.contains('fa-caret-right')) {
                    toggleIcon.click();
                }
                
            } catch (error) {
                console.error('Error adding child:', error);
                showErrorMessage(`添加子任务失败: ${error.message}`);
            }
        }

        // 添加同级任务
        async function addSibling(siblingId) {
            try {
                hideContextMenu();
                
                const siblingRow = document.querySelector(`#data-table tr[data-id="${siblingId}"]`);
                if (!siblingRow) return;
                
                const parentId = siblingRow.dataset.parentId;
                const level = parseInt(siblingRow.dataset.level);
                
                // 准备新记录数据
                const newRecord = {
                    f_content: "新任务",
                    f_description: "",
                    f_solution: "",
                    parentId: parentId === "null" ? null : parentId,
                    f_level: level
                };
                
                // 继承同级任务的时间范围
                const siblingStartTime = siblingRow.querySelector('td:nth-child(12)')?.textContent;
                const siblingEndTime = siblingRow.querySelector('td:nth-child(13)')?.textContent;
                
                if (siblingStartTime) {
                    newRecord.f_starttime = siblingStartTime;
                }
                
                if (siblingEndTime) {
                    newRecord.f_endtime = siblingEndTime;
                }
                
                if (siblingStartTime && siblingEndTime) {
                    newRecord.f_time = calculateDuration(siblingStartTime, siblingEndTime);
                }
                
                // 创建新记录
                const response = await fetch(`${API_URL}/${TABLE_NAME}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(newRecord)
                });
                
                if (!response.ok) {
                    throw new Error(`创建同级任务失败: ${response.status}`);
                }
                
                // 使用局部更新刷新数据
                await fetchData(null, true);
                
            } catch (error) {
                console.error('Error adding sibling:', error);
                showErrorMessage(`添加同级任务失败: ${error.message}`);
            }
        }

        // 添加点击文档时隐藏右键菜单的事件
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.context-menu')) {
                hideContextMenu();
            }
        });

        // 添加右键点击表格行的事件监听
        document.addEventListener('DOMContentLoaded', () => {
            const tableContainer = document.getElementById('table-container');
            if (tableContainer) {
                tableContainer.addEventListener('contextmenu', (e) => {
                    e.preventDefault(); // 阻止默认右键菜单
                    
                    // 获取被点击的行
                    const row = e.target.closest('tr');
                    if (row && row.dataset.id) {
                        // 直接使用鼠标点击坐标
                        showContextMenu(e, row.dataset.id, {
                            x: e.clientX,
                            y: e.clientY
                        });
                    }
                });
            }
        });

        // 添加处理编辑时键盘事件的函数
        function handleEditKeydown(e, input) {
            // 按下回车键时完成编辑
            if (e.key === 'Enter') {
                e.preventDefault();
                input.blur();
            }
            
            // 按下Escape键时取消编辑
            if (e.key === 'Escape') {
                e.preventDefault();
                const cell = input.parentElement;
                const fieldName = input.dataset.field;
                
                // 恢复原始值
                if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                    cell.textContent = input.defaultValue ? input.defaultValue + '天' : '';
                } else if (fieldName === 'f_task') {
                    // 特殊处理任务名称列
                    if (cell.classList.contains('has-task-number')) {
                        const taskNumber = cell.querySelector('.task-number')?.textContent || '';
                        cell.innerHTML = `<span class="task-number">${taskNumber}</span><span class="task-name">${input.defaultValue || ''}</span>`;
                    } else {
                        cell.innerHTML = `<span class="task-name">${input.defaultValue || ''}</span>`;
                    }
                } else if (fieldName === 'f_pretask') {
                    // 特殊处理前置任务列
                    const pretaskContent = cell.querySelector('.pretask-content');
                    if (pretaskContent) {
                        pretaskContent.innerHTML = input.defaultValue.split('、').map(num => 
                            `<span class="pretask-number" onclick="handlePretaskClick(event, this)">${num.replace(/\s+/g, '').trim()}</span>`
                        ).join('、');
                    }
                } else {
                    cell.textContent = input.defaultValue || '';
                }
                
                cell.classList.remove('editing');
            }
        }

        // 添加更新行背景色的函数
        function updateRowBackgroundColor(row) {
            if (!row) return;
            
            // 首先检查是否有自定义颜色 - 这应该有最高优先级
            const cellColor = row.dataset.cellColor;
            if (cellColor && cellColor !== 'none') {
                console.log('应用自定义颜色(最高优先级):', cellColor, '到行:', row.dataset.id);
                row.style.backgroundColor = getColorValue(cellColor);
                // 确保自定义颜色具有最高优先级，强制添加!important
                row.style.cssText += `background-color: ${getColorValue(cellColor)} !important;`;
                return; // 如果应用了自定义颜色，不再应用状态颜色
            }
            
            // 如果没有自定义颜色，则根据状态设置颜色
            const status = row.dataset.status;
            // 根据状态设置行的背景色
            switch (status) {
                case '已完成':
                    row.style.backgroundColor = '#e8f5e9'; // 绿色
                    break;
                case '进行中':
                    row.style.backgroundColor = '#e3f2fd'; // 蓝色
                    break;
                case '未开始':
                    row.style.backgroundColor = '#fff8e1'; // 黄色
                    break;
                case '已延期':
                case '超期':
                    row.style.backgroundColor = '#ffebee'; // 红色
                    break;
                case '超期完成':
                    row.style.backgroundColor = '#c8e6c9'; // 更深的绿色
                    break;
                case '挂起':
                    row.style.backgroundColor = '#f3e5f5'; // 紫色
                    break;
                case '已取消':
                    row.style.backgroundColor = '#f5f5f5'; // 灰色
                    break;
                default:
                    // 保持原有的背景色（根据层级）
                    const level = parseInt(row.dataset.level || '0');
                    switch (level) {
                        case 0:
                            row.style.backgroundColor = '#dee2e6'; // 更深的灰色
                            break;
                        case 1:
                            row.style.backgroundColor = '#e9ecef'; // 二级菜单使用原来一级菜单的灰色
                            break;
                        case 2:
                            row.style.backgroundColor = '#f5f5f5'; // 三级菜单使用原来二级菜单的灰色
                            break;
                        case 3:
                        default:
                            row.style.backgroundColor = '#ffffff'; // 四级菜单及以下保持白色
                            break;
                    }
            }
            
            console.log('应用状态颜色:', status, '到行:', row.dataset.id, '背景色:', row.style.backgroundColor);
        }

        // 添加初始化表格颜色的函数
        function initializeTableColors() {
            const table = document.getElementById('data-table');
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                updateRowBackgroundColor(row);
            });
        }

        // 在fetchData函数中添加对initializeTableColors的调用
        const originalFetchData = fetchData;
        fetchData = async function(filter, silent) {
            await originalFetchData(filter, silent);
            
            // 表格加载完成后，初始化行颜色
            setTimeout(initializeTableColors, 100);
        };

        // 添加一个直接应用颜色的函数，不通过API调用
        function applyDirectColor(row, color) {
            if (!row) return;
            
            // 移除之前的所有颜色类
            row.classList.remove('row-red', 'row-yellow', 'row-green', 'row-blue', 'row-purple');
            
            // 应用新颜色
            if (color && color !== 'none') {
                // 添加样式类
                row.classList.add(`row-${color}`);
                
                // 保存颜色到数据属性
                row.dataset.cellColor = color;
                
                // 使用内联样式并添加!important确保最高优先级
                const colorValue = getColorValue(color);
                if (colorValue) {
                    row.style.cssText += `background-color: ${colorValue} !important;`;
                    console.log(`为行 ${row.dataset.id} 设置颜色: ${color}, 值: ${colorValue}`);
                }
            } else {
                // 清除颜色
                row.dataset.cellColor = '';
                // 移除内联样式
                row.style.backgroundColor = '';
                // 确保完全移除样式
                row.style.cssText = row.style.cssText.replace(/background-color:[^;]+!important;?/gi, '');
            }
        }

        // 定期检查表格并应用颜色
        function fixTableColors() {
            const table = document.querySelector('#data-table');
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    // 首先检查是否有自定义颜色 - 这应该有最高优先级
                    const cellColor = row.dataset.cellColor;
                    if (cellColor && cellColor !== 'none') {
                        const colorMap = {
                            'red': '#ffebee',     // 更柔和的红色
                            'yellow': '#fff8e1',   // 更柔和的黄色
                            'green': '#e8f5e9',    // 更柔和的绿色
                            'blue': '#e3f2fd',     // 更柔和的蓝色
                            'purple': '#f3e5f5'    // 更柔和的紫色
                        };
                        const colorValue = colorMap[cellColor] || '';
                        if (colorValue) {
                            // 使用 !important 确保优先级最高
                            row.style.cssText += `background-color: ${colorValue} !important;`;
                            console.log('应用自定义颜色(最高优先级):', cellColor, '到行:', row.dataset.id);
                            return; // 如果应用了自定义颜色，不再应用状态颜色
                        }
                    }
                    
                    // 如果没有自定义颜色，则根据状态设置颜色
                    const status = row.dataset.status;
                    if (status) {
                        switch (status) {
                            case '已完成':
                                row.style.backgroundColor = '#e8f5e9'; // 绿色
                                break;
                            case '进行中':
                                row.style.backgroundColor = '#e3f2fd'; // 蓝色
                                break;
                            case '未开始':
                                row.style.backgroundColor = '#fff8e1'; // 黄色
                                break;
                            case '已延期':
                            case '超期':
                                row.style.backgroundColor = '#ffebee'; // 红色
                                break;
                            case '超期完成':
                                row.style.backgroundColor = '#c8e6c9'; // 更深的绿色
                                break;
                            case '挂起':
                                row.style.backgroundColor = '#f3e5f5'; // 紫色
                                break;
                            case '已取消':
                                row.style.backgroundColor = '#f5f5f5'; // 灰色
                                break;
                        }
                    }
                });
                        
                // 为所有编辑输入添加键盘事件处理
                document.querySelectorAll('.editing input').forEach(input => {
                    if (!input.hasKeyHandler) {
                        input.hasKeyHandler = true;
                        input.addEventListener('keydown', function(e) {
                            // 按下回车键时完成编辑
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                this.blur();
                            }
                            
                            // 按下Escape键时取消编辑
                            if (e.key === 'Escape') {
                                e.preventDefault();
                                const cell = this.parentElement;
                                const fieldName = this.dataset.field;
                                
                                // 恢复原始值并移除编辑状态
                                if (fieldName === 'f_time' || fieldName === 'f_c_time' || fieldName === 'f_r_time') {
                                    cell.textContent = this.defaultValue ? this.defaultValue + '天' : '';
                                } else {
                                    cell.textContent = this.defaultValue || '';
                                }
                                
                                cell.classList.remove('editing');
                            }
                        });
                    }
                });
            }
        }
        
        // 页面加载后执行修复
        document.addEventListener('DOMContentLoaded', function() {
            // 初始执行一次
            setTimeout(fixTableColors, 1000);
            
            // 每3秒检查一次表格颜色
            setInterval(fixTableColors, 3000);
        });

        // 重写右键菜单显示逻辑
        document.addEventListener('DOMContentLoaded', () => {
            document.addEventListener('contextmenu', function(e) {
                const row = e.target.closest('#data-table tbody tr');
                if (!row || !row.dataset.id) return;
                
                e.preventDefault();
                e.stopPropagation();
                
                if (contextMenu) {
                    contextMenu.remove();
                    contextMenu = null;
                }
                
                const currentColor = row.dataset.cellColor || '';
                const rowId = row.dataset.id;
                
                const menu = document.createElement('div');
                menu.className = 'context-menu';
                menu.style.position = 'fixed';
                
                menu.innerHTML = `
                    <div onclick="addChild('${rowId}')">
                        <i class="fas fa-plus-circle"></i> 添加子任务
                    </div>
                    <div onclick="addSibling('${rowId}')">
                        <i class="fas fa-arrows-alt-h"></i> 添加同级任务
                    </div>
                    <div class="menu-divider"></div>
                    <div class="color-option ${currentColor === 'red' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="red">
                        <i class="fas fa-pencil-alt" style="color: #f44336;"></i> 红色标记
                    </div>
                    <div class="color-option ${currentColor === 'yellow' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="yellow">
                        <i class="fas fa-pencil-alt" style="color: #ffc107;"></i> 黄色标记
                    </div>
                    <div class="color-option ${currentColor === 'green' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="green">
                        <i class="fas fa-pencil-alt" style="color: #4caf50;"></i> 绿色标记
                    </div>
                    <div class="color-option ${currentColor === 'blue' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="blue">
                        <i class="fas fa-pencil-alt" style="color: #2196f3;"></i> 蓝色标记
                    </div>
                    <div class="color-option ${currentColor === 'purple' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="purple">
                        <i class="fas fa-pencil-alt" style="color: #9c27b0;"></i> 紫色标记
                    </div>
                    <div class="color-option ${currentColor === '' ? 'active' : ''}" onclick="setCellColor(this, '${rowId}')" color="none">
                        <i class="fas fa-eraser"></i> 清除标记
                    </div>
                    <div class="menu-divider"></div>
                    <div class="delete-option" onclick="deleteRow('${rowId}')">
                        <i class="fas fa-trash-alt"></i> 删除
                    </div>
                `;
                
                document.body.appendChild(menu);
                contextMenu = menu;
                
                const menuWidth = menu.offsetWidth;
                const menuHeight = menu.offsetHeight;
                
                let posX = e.clientX;
                let posY = e.clientY;
                
                if (posX + menuWidth > window.innerWidth) {
                    posX = window.innerWidth - menuWidth - 5;
                }
                
                if (posY + menuHeight > window.innerHeight) {
                    posY = window.innerHeight - menuHeight - 5;
                }
                
                menu.style.top = posY + 'px';
                menu.style.left = posX + 'px';
                
                const closeMenu = function(e) {
                    if (contextMenu && !contextMenu.contains(e.target)) {
                        contextMenu.remove();
                        contextMenu = null;
                        document.removeEventListener('click', closeMenu);
                    }
                };
                
                setTimeout(() => {
                    document.addEventListener('click', closeMenu);
                }, 100);
            });
            
            window.hideContextMenu = function() {
                if (contextMenu) {
                    contextMenu.remove();
                    contextMenu = null;
                }
            };
        });

        // 添加搜索过滤功能
        function filterTable(searchText) {
            const table = document.getElementById('data-table');
            if (!table) return;

            const tbody = table.querySelector('tbody');
            if (!tbody) return;

            const rows = tbody.getElementsByTagName('tr');
            
            // 如果搜索文本为空，显示所有行
            if (!searchText || searchText.trim() === '') {
                for (const row of rows) {
                    row.style.display = '';
                }
                return;
            }

            const searchLower = searchText.toLowerCase();

            for (const row of rows) {
                let found = false;
                const cells = row.getElementsByTagName('td');
                
                for (const cell of cells) {
                    // 获取单元格的文本内容
                    let cellText = '';
                    if (cell.classList.contains('task-cell')) {
                        // 对于任务单元格，只搜索任务名称
                        const taskName = cell.querySelector('.task-name');
                        cellText = taskName ? taskName.textContent : '';
                    } else {
                        cellText = cell.textContent;
                    }

                    if (cellText.toLowerCase().includes(searchLower)) {
                        found = true;
                        break;
                    }
                }

                // 显示或隐藏行
                row.style.display = found ? '' : 'none';
            }
        }

        // 添加搜索框的事件监听器
        document.addEventListener('DOMContentLoaded', () => {
            const searchInput = document.querySelector('.toolbar input[type="search"]');
            if (searchInput) {
                // 监听输入事件，包括删除和粘贴
                searchInput.addEventListener('input', (e) => {
                    filterTable(e.target.value);
                });
                
                // 监听清除按钮点击事件
                searchInput.addEventListener('search', (e) => {
                    filterTable(e.target.value);
                });
            }
        });

        // 添加删除图标到可编辑单元格
        function addClearButtons() {
            const editableCells = document.querySelectorAll('.editable');
            editableCells.forEach(cell => {
                // 跳过已经有删除按钮的单元格
                if (cell.querySelector('.clear-field-btn')) return;
                
                // 跳过任务名称列（第二列）
                if (cell.classList.contains('task-cell')) return;
                
                const clearBtn = document.createElement('span');
                clearBtn.className = 'clear-field-btn';
                clearBtn.innerHTML = '<i class="fas fa-times"></i>';
                clearBtn.title = '清空字段';
                
                clearBtn.addEventListener('click', async (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    
                    const row = cell.closest('tr');
                    if (!row) return;
                    
                    const rowId = row.dataset.id;
                    if (!rowId) return;
                    
                    // 获取字段名
                    let fieldName = '';
                    const cellIndex = Array.from(row.cells).indexOf(cell);
                    
                    // 根据列索引确定字段名
                    switch(cellIndex) {
                        case 2: fieldName = 'f_pretask'; break;
                        case 3: fieldName = 'f_assigner'; break;
                        case 4: fieldName = 'f_degree'; break;
                        case 5: fieldName = 'f_status'; break;
                        case 6: fieldName = 'f_risk'; break;
                        case 7: fieldName = 'f_risk_level'; break;
                        case 8: fieldName = 'f_riskQ'; break;
                        case 9: fieldName = 'f_riskS'; break;
                        case 10: fieldName = 'f_riskSL'; break;
                        case 11: fieldName = 'f_starttime'; break;
                        case 12: fieldName = 'f_endtime'; break;
                        case 13: fieldName = 'f_time'; break;
                        case 14: fieldName = 'f_mark'; break;
                        case 15: fieldName = 'f_c_start_time'; break;
                        case 16: fieldName = 'f_c_end_time'; break;
                        case 17: fieldName = 'f_c_time'; break;
                        case 18: fieldName = 'f_r_start_time'; break;
                        case 19: fieldName = 'f_r_end_time'; break;
                        case 20: fieldName = 'f_r_time'; break;
                        case 21: fieldName = 'f_s_dev'; break;
                        case 22: fieldName = 'f_e_dev'; break;
                        case 23: fieldName = 'f_dev'; break;
                        default: return;
                    }
                    
                    try {
                        // 准备更新数据
                        const updateData = {
                            [fieldName]: null
                        };
                        
                        // 如果是时间相关字段，需要同时更新相关字段
                        if (fieldName === 'f_starttime' || fieldName === 'f_endtime') {
                            updateData.f_time = null;
                        } else if (fieldName === 'f_c_start_time' || fieldName === 'f_c_end_time') {
                            updateData.f_c_time = null;
                        } else if (fieldName === 'f_r_start_time' || fieldName === 'f_r_end_time') {
                            updateData.f_r_time = null;
                        }
                        
                        // 发送更新请求
                        const response = await fetch(`${API_URL}/${TABLE_NAME}/${rowId}`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${API_KEY}`,
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify(updateData)
                        });

                        if (!response.ok) {
                            throw new Error(`清空字段失败: ${response.status}`);
                        }

                        // 更新UI显示
                        if (fieldName === 'f_pretask') {
                            cell.innerHTML = '<span class="pretask-content"></span>';
                        } else if (fieldName.includes('time')) {
                            cell.textContent = '';
                        } else {
                            cell.textContent = '';
                        }

                        // 如果是状态字段，更新行的状态
                        if (fieldName === 'f_status') {
                            row.dataset.status = '';
                            updateRowBackgroundColor(row);
                        }

                    } catch (error) {
                        console.error('清空字段失败:', error);
                        showErrorMessage(`清空字段失败: ${error.message}`);
                    }
                });
                
                cell.appendChild(clearBtn);
            });
        }

        // 在表格创建完成后添加删除按钮
        const originalCreateTable = createTable;
        createTable = function(data) {
            originalCreateTable(data);
            addClearButtons();
        };

        // 在初始化时添加删除按钮
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(addClearButtons, 1000);
        });

        // 添加性能监控对象
        const performanceMetrics = {
            renderTime: [],
            dataFetchTime: [],
            maxSamples: 10,

            addRenderTime(time) {
                this.renderTime.push(time);
                if (this.renderTime.length > this.maxSamples) {
                    this.renderTime.shift();
                }
            },

            addFetchTime(time) {
                this.dataFetchTime.push(time);
                if (this.dataFetchTime.length > this.maxSamples) {
                    this.dataFetchTime.shift();
                }
            },

            getAverageRenderTime() {
                return this.renderTime.reduce((a, b) => a + b, 0) / this.renderTime.length;
            },

            getAverageFetchTime() {
                return this.dataFetchTime.reduce((a, b) => a + b, 0) / this.dataFetchTime.length;
            },

            logMetrics() {
                console.log(`平均渲染时间: ${this.getAverageRenderTime().toFixed(2)}ms`);
                console.log(`平均获取数据时间: ${this.getAverageFetchTime().toFixed(2)}ms`);
            }
        };

        // 移除之前的optimizeScroll函数
        function optimizeScroll() {
            const container = document.getElementById('table-container');
            if (!container) return;

            // 只保留性能监控相关的代码
            container.addEventListener('scroll', throttle(() => {
                // 记录滚动性能
                performanceMetrics.addRenderTime(performance.now());
            }, 100));
        }

        // 在初始化时调用优化函数
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化性能监控
            optimizeScroll();
            
            // 使用事件委托处理表格点击事件
            const tableContainer = document.getElementById('table-container');
            if (tableContainer) {
                tableContainer.addEventListener('click', throttle((e) => {
                    const toggleIcon = e.target.closest('.toggle-icon');
                    if (toggleIcon) {
                        const row = toggleIcon.closest('tr');
                        const level = parseInt(row.dataset.level);
                        const isExpanded = toggleIcon.classList.contains('fa-caret-down');
                        
                        toggleIcon.classList.toggle('fa-caret-down');
                        toggleIcon.classList.toggle('fa-caret-right');
                        
                        let nextRow = row.nextElementSibling;
                        while (nextRow && parseInt(nextRow.dataset.level) > level) {
                            nextRow.style.display = isExpanded ? 'none' : '';
                            nextRow = nextRow.nextElementSibling;
                        }
                    }
                }, 100));
            }
        });
    </script>
</body>
</html> 