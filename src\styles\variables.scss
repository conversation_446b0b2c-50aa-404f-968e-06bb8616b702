// 颜色系统
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// 边框颜色
$border-color: #DCDFE6;
$border-color-light: #E0E3E9;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;

// 背景颜色
$background-color-base: #F5F7FA;
$background-color-white: #FFFFFF;
$table-header-bg: #f1f5f9;

// 表格相关颜色
$table-border-color: #cbd5e1;
$table-row-hover-bg: #f8fafc;
$table-stripe-bg: #fafafa;

// 尺寸
$font-size-small: 12px;
$font-size-base: 13px; // 表格默认使用13px
$font-size-medium: 14px;
$font-size-large: 16px;

// 边框圆角
$border-radius-small: 4px;
$border-radius-base: 4px;
$border-radius-medium: 8px;
$border-radius-large: 12px;

// 边距
$spacing-mini: 4px;
$spacing-small: 8px;
$spacing-base: 16px;
$spacing-large: 24px;
$spacing-extra-large: 32px;

// 阴影
$box-shadow-light: 0 2px 12px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12);
$box-shadow-header: 0 2px 12px rgba(0, 0, 0, 0.04); // 用于页头阴影

// 过渡
$transition-all: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-base: all 0.2s ease-in-out;

// z-index
$z-index-normal: 1;
$z-index-dropdown: 10;
$z-index-sticky: 20;
$z-index-fixed: 30;
$z-index-modal-mask: 40;
$z-index-modal: 50;
$z-index-popover: 60;
$z-index-tooltip: 70;
$z-index-notification: 80; 