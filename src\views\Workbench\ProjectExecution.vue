<template>
  <div class="execution-section">
    <div class="block-container">
      <div class="block-header">{{ userStore.projectExecutionTitle }}</div>
      <div class="block-content">
        <div class="execution-content">
          <div class="execution-item clickable" @click="showTaskDetails('today')">
            <div class="item-icon">
              <el-icon><List /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ executionData.todayTaskCount }}<span class="item-unit">个</span></div>
              <div class="item-label">当日任务</div>
            </div>
          </div>
          <div class="execution-item clickable" @click="showTaskDetails('overdue')">
            <div class="item-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ executionData.overdueTaskCount }}<span class="item-unit">个</span></div>
              <div class="item-label">逾期任务</div>
            </div>
          </div>
          <div class="execution-item clickable" @click="showTaskDetails('dueToday')">
            <div class="item-icon">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ executionData.dueTodayTaskCount }}<span class="item-unit">个</span></div>
              <div class="item-label">当日到期任务</div>
            </div>
          </div>
          <div class="execution-item clickable" @click="showTaskDetails('dueThisWeek')">
            <div class="item-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ executionData.dueThisWeekTaskCount }}<span class="item-unit">个</span></div>
              <div class="item-label">本周到期任务</div>
            </div>
          </div>
          <div class="execution-item clickable" @click="showTaskDetails('dueThisMonth')">
            <div class="item-icon">
              <el-icon><DocumentChecked /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">{{ executionData.dueThisMonthTaskCount }}<span class="item-unit">个</span></div>
              <div class="item-label">本月到期任务</div>
            </div>
          </div>
          <div class="execution-item">
            <div class="item-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">¥{{ formatAmount(executionData.totalContractAmount) }}</div>
              <div class="item-label">关注总金额</div>
            </div>
          </div>
          <div class="execution-item">
            <div class="item-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-value">¥{{ formatAmount(executionData.totalReceivedAmount) }}</div>
              <div class="item-label">关注回款总金额</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 逾期项目抽屉 -->
  <el-drawer
    v-model="drawerVisible"
    direction="rtl"
    size="75%"
    :before-close="handleDrawerClose"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="drawerVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon :class="['drawer-icon', `icon-${drawerIcon.toLowerCase()}`]">
            <List v-if="drawerIcon === 'List'" />
            <Warning v-else-if="drawerIcon === 'Warning'" />
            <Bell v-else-if="drawerIcon === 'Bell'" />
            <Calendar v-else-if="drawerIcon === 'Calendar'" />
            <DocumentChecked v-else-if="drawerIcon === 'DocumentChecked'" />
            <Warning v-else />
          </el-icon>
          {{ drawerTitle }}
        </div>
      </div>
    </template>
    <div class="drawer-content">
      <el-table
        :data="taskProjects"
        border
        style="width: 100%"
        v-loading="drawerLoading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '8px 0' }"
      >
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="projectName" label="项目名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="projectManagerName" label="项目经理" width="120" align="center" />
        <el-table-column prop="totalTaskCount" label="总任务数量" width="120" align="center" />
        <el-table-column :prop="currentTaskCountField" :label="currentTaskCountLabel" width="140" align="center" />
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="viewProject(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>

  <!-- 二级抽屉：项目逾期任务详情 -->
  <el-drawer
    v-model="detailDrawerVisible"
    direction="rtl"
    size="65%"
    :before-close="handleDetailDrawerClose"
    :show-close="false"
    :append-to-body="true"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="detailDrawerVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon :class="['drawer-icon', `icon-${drawerIcon.toLowerCase()}`]">
            <List v-if="drawerIcon === 'List'" />
            <Warning v-else-if="drawerIcon === 'Warning'" />
            <Bell v-else-if="drawerIcon === 'Bell'" />
            <Calendar v-else-if="drawerIcon === 'Calendar'" />
            <DocumentChecked v-else-if="drawerIcon === 'DocumentChecked'" />
            <Warning v-else />
          </el-icon>
          {{ currentProjectInfo?.projectName || '项目' }} - {{ currentTaskCountLabel }}详情
        </div>
      </div>
    </template>

    <div class="drawer-content">
      <el-table
        :data="overdueTasksData"
        border
        style="width: 100%"
        v-loading="detailDrawerLoading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '8px 0' }"
      >
        <el-table-column prop="seriNum" label="序号" width="80" align="center" />
        <el-table-column prop="planName" label="任务名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ownerName" label="责任人" width="100" align="center" />
        <el-table-column prop="executorName" label="执行人" width="100" align="center" />
        <el-table-column prop="planStartTime" label="计划开始时间" width="140" align="center">
          <template #default="scope">
            <span>{{ scope.row.planStartTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="planEndTime" label="计划完成时间" width="140" align="center">
          <template #default="scope">
            <span>{{ scope.row.planEndTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="变更次数" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.row.version || 0 }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Calendar, Warning, Close, Bell, DocumentChecked, List, Money, CreditCard } from '@element-plus/icons-vue';
import { getFollowedProjectsSummary, getWorkbenchFollowedSummaryList, getWorkbenchFollowedProjectTasks, getWorkbenchPlanFilterTypes } from '@/api/project';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';

// 使用全局用户状态
const userStore = useUserStore();

const executionData = ref({
  todayTaskCount: 0,
  overdueTaskCount: 0,
  dueTodayTaskCount: 0,
  dueThisWeekTaskCount: 0,
  dueThisMonthTaskCount: 0,
  totalContractAmount: 0,
  totalReceivedAmount: 0
});

// 抽屉相关数据
const drawerVisible = ref(false);
const drawerLoading = ref(false);
const taskProjects = ref([]);
const drawerTitle = ref('');
const currentTaskType = ref('');
const currentTaskCountField = ref('');
const currentTaskCountLabel = ref('');
const drawerIcon = ref('Warning'); // 动态图标名称

// 任务类型枚举映射 - 动态从接口获取
const taskTypeEnumMap = ref({});

// 二级抽屉相关数据
const detailDrawerVisible = ref(false);
const detailDrawerLoading = ref(false);
const overdueTasksData = ref([]);
const currentProjectInfo = ref(null);

// 获取任务类型枚举映射
const getTaskTypeEnumMap = async () => {
  try {
    const response = await getWorkbenchPlanFilterTypes();

    if (response && (response.code === 200 || response.code === 0)) {
      const enumData = response.data || {};

      // 根据接口返回的数据结构创建映射
      // 接口返回格式: { "0": "当日任务", "1": "逾期任务", ... }
      taskTypeEnumMap.value = {
        today: 0,        // 当日任务
        overdue: 1,      // 逾期任务
        dueToday: 2,     // 当日到期任务
        dueThisWeek: 3,  // 本周到期任务
        dueThisMonth: 4  // 本月到期任务
      };

      // 验证接口返回的枚举值是否完整
      const expectedKeys = ['0', '1', '2', '3', '4'];
      const missingKeys = expectedKeys.filter(key => !enumData.hasOwnProperty(key));

      if (missingKeys.length > 0) {
        console.warn('接口返回的枚举值不完整，缺少:', missingKeys);
      }
    } else {
      // 使用默认枚举值作为备选方案
      taskTypeEnumMap.value = {
        today: 0,
        overdue: 1,
        dueToday: 2,
        dueThisWeek: 3,
        dueThisMonth: 4
      };
    }
  } catch (error) {
    console.error('获取任务类型枚举失败:', error);
    // 使用默认枚举值作为备选方案
    taskTypeEnumMap.value = {
      today: 0,
      overdue: 1,
      dueToday: 2,
      dueThisWeek: 3,
      dueThisMonth: 4
    };
  }
};

// 格式化金额显示
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0';

  const num = parseFloat(amount);

  // 大于1亿，显示为 X.XX亿
  if (num >= 100000000) {
    return (num / 100000000).toFixed(2) + '亿';
  }
  // 大于1万，显示为 X.XX万
  else if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万';
  }
  // 小于1万，显示原数值，添加千分位分隔符
  else {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
};

// 获取项目执行情况数据
const fetchExecutionData = async () => {
  try {
    const res = await getFollowedProjectsSummary();
    if (res && (res.code === 200 || res.code === 0) && res.data) {
      // 检查数据类型
      if (Array.isArray(res.data)) {
        // 如果是数组，汇总所有关注项目的任务数据和金额数据
        const summaryData = res.data.reduce((acc, project) => {
          acc.todayTaskCount += project.todayTaskCount || 0;
          acc.overdueTaskCount += project.overdueTaskCount || 0;
          acc.dueTodayTaskCount += project.dueTodayTaskCount || 0;
          acc.dueThisWeekTaskCount += project.dueThisWeekTaskCount || 0;
          acc.dueThisMonthTaskCount += project.dueThisMonthTaskCount || 0;
          acc.totalContractAmount += project.totalContractAmount || 0;
          acc.totalReceivedAmount += project.totalReceivedAmount || 0;
          return acc;
        }, {
          todayTaskCount: 0,
          overdueTaskCount: 0,
          dueTodayTaskCount: 0,
          dueThisWeekTaskCount: 0,
          dueThisMonthTaskCount: 0,
          totalContractAmount: 0,
          totalReceivedAmount: 0
        });
        executionData.value = summaryData;
      } else if (typeof res.data === 'object') {
        // 如果是对象，直接使用返回的数据
        executionData.value = {
          todayTaskCount: res.data.todayTaskCount || 0,
          overdueTaskCount: res.data.overdueTaskCount || 0,
          dueTodayTaskCount: res.data.dueTodayTaskCount || 0,
          dueThisWeekTaskCount: res.data.dueThisWeekTaskCount || 0,
          dueThisMonthTaskCount: res.data.dueThisMonthTaskCount || 0,
          totalContractAmount: res.data.totalContractAmount || 0,
          totalReceivedAmount: res.data.totalReceivedAmount || 0
        };
      } else {
        ElMessage.error('数据格式错误');
      }
    } else {
      ElMessage.error(res?.msg || res?.message || '获取项目执行情况失败');
    }
  } catch (e) {
    console.error('获取项目执行情况失败:', e);
    ElMessage.error('获取项目执行情况失败: ' + (e.message || '网络错误'));
  }
};

// 获取任务项目列表
const getTaskProjectsList = async () => {
  try {
    drawerLoading.value = true;
    const response = await getWorkbenchFollowedSummaryList();

    if (response && (response.code === 200 || response.code === 0)) {
      taskProjects.value = response.data || [];
    } else {
      ElMessage.error(response?.msg || response?.message || '获取项目列表失败');
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);

    // 如果是接口未实现的错误，给出友好提示
    if (error.message && error.message.includes('系统错误')) {
      ElMessage.warning('该功能正在开发中，请稍后再试');
    } else {
      ElMessage.error('获取项目列表失败: ' + (error.message || '网络错误'));
    }
  } finally {
    drawerLoading.value = false;
  }
};

// 显示任务详情抽屉
const showTaskDetails = async (taskType) => {
  currentTaskType.value = taskType;

  // 根据任务类型设置抽屉标题、表格列和图标
  const taskTypeConfig = {
    today: {
      title: '当日任务情况',
      field: 'todayTaskCount',
      label: '当日任务数量',
      icon: 'List'  // 📋 任务列表图标，更直观表示当日要处理的任务
    },
    overdue: {
      title: '逾期任务情况',
      field: 'overdueTaskCount',
      label: '逾期任务数量',
      icon: 'Warning'  // ⚠️ 警告图标，表示需要紧急处理的逾期任务
    },
    dueToday: {
      title: '当日到期任务情况',
      field: 'dueTodayTaskCount',
      label: '当日到期任务数量',
      icon: 'Bell'  // 🔔 铃铛图标，提醒今天到期需要关注
    },
    dueThisWeek: {
      title: '本周到期任务情况',
      field: 'dueThisWeekTaskCount',
      label: '本周到期任务数量',
      icon: 'Calendar'  // 📅 日历图标，表示本周时间范围内的任务
    },
    dueThisMonth: {
      title: '本月到期任务情况',
      field: 'dueThisMonthTaskCount',
      label: '本月到期任务数量',
      icon: 'DocumentChecked'  // 📄✓ 文档检查图标，表示本月需要完成的任务
    }
  };

  const config = taskTypeConfig[taskType];
  if (config) {
    drawerTitle.value = config.title;
    currentTaskCountField.value = config.field;
    currentTaskCountLabel.value = config.label;
    drawerIcon.value = config.icon;
  }

  drawerVisible.value = true;
  await getTaskProjectsList();
};

// 关闭抽屉
const handleDrawerClose = (done) => {
  taskProjects.value = [];
  done();
};

// 获取项目任务列表
const getProjectTasksList = async (projectId, taskType) => {
  try {
    detailDrawerLoading.value = true;

    // 根据任务类型获取对应的枚举值
    const typeEnum = taskTypeEnumMap.value[taskType];
    if (typeEnum === undefined) {
      throw new Error('无效的任务类型');
    }

    const response = await getWorkbenchFollowedProjectTasks(projectId, typeEnum);

    if (response && (response.code === 200 || response.code === 0)) {
      overdueTasksData.value = response.data || [];
    } else {
      ElMessage.error(response?.msg || response?.message || '获取任务列表失败');
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
    ElMessage.error('获取任务列表失败: ' + (error.message || '网络错误'));
  } finally {
    detailDrawerLoading.value = false;
  }
};

// 查看项目详情
const viewProject = async (row) => {
  currentProjectInfo.value = row;
  detailDrawerVisible.value = true;
  await getProjectTasksList(row.projectId, currentTaskType.value);
};

// 关闭二级抽屉
const handleDetailDrawerClose = (done) => {
  overdueTasksData.value = [];
  currentProjectInfo.value = null;
  done();
};

onMounted(async () => {
  // 先获取枚举值，再获取执行数据
  await getTaskTypeEnumMap();
  await fetchExecutionData();
});
</script>

<style scoped>
/* 项目执行情况区域 */
.execution-section {
  margin-bottom: 8px; /* 减少底部间距 */
}

.block-container {
  background-color: #ffffff;
  border-radius: 6px; /* 减少圆角 */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06); /* 减少阴影 */
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.block-header {
  padding: 8px 16px; /* 减少上下内边距 */
  font-size: 15px; /* 减少字体大小 */
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.loading-placeholder {
  color: #909399;
  font-weight: normal;
  opacity: 0.8;
}

.block-content {
  padding: 8px 16px; /* 减少内边距 */
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 项目执行情况内容区域 */
.execution-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 2px 0; /* 减少内边距 */
  gap: 4px; /* 添加项目间距 */
}

.execution-item {
  flex: 1;
  min-width: 140px; /* 进一步减少最小宽度以适应7个项目 */
  max-width: calc(14.28% - 4px); /* 7个项目平均分配宽度 */
  padding: 4px 6px; /* 减少内边距 */
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  border-radius: 6px; /* 减少圆角 */
  transition: all 0.3s ease;
}

/* 悬浮效果 */
.execution-item:hover {
  background: linear-gradient(135deg, rgba(91, 123, 250, 0.03) 0%, rgba(91, 123, 250, 0.01) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(91, 123, 250, 0.1);
}

.execution-item:hover .item-icon {
  background-color: rgba(91, 123, 250, 0.12);
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.15);
}

.execution-item:hover .item-value {
  color: #5B7BFA;
}

.execution-item:hover .item-label {
  color: #606266;
}

.execution-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #ebeef5;
}

.item-icon {
  font-size: 24px; /* 减少图标大小 */
  color: #5B7BFA;
  margin-right: 12px; /* 减少右边距 */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px; /* 减少宽度 */
  height: 40px; /* 减少高度 */
  background-color: rgba(91, 123, 250, 0.1);
  border-radius: 6px; /* 减少圆角 */
  transition: all 0.3s ease;
}

.item-info {
  display: flex;
  flex-direction: column;
}

.item-value {
  font-size: 22px; /* 减少字体大小 */
  color: #303133;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 2px; /* 减少底部间距 */
  transition: all 0.3s ease;
}

.item-unit {
  font-size: 12px; /* 减少字体大小 */
  font-weight: normal;
  margin-left: 2px;
  transition: color 0.3s ease;
}

.item-label {
  font-size: 12px; /* 减少字体大小 */
  color: #909399;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .execution-item {
    min-width: 120px;
    padding: 10px 0;
  }

  .item-value {
    font-size: 20px;
  }
}

/* 不同项目的主题色设计 */
.execution-item:nth-child(1) .item-icon {
  background-color: rgba(91, 123, 250, 0.1);
  color: #5B7BFA;
}

.execution-item:nth-child(2) .item-icon {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.execution-item:nth-child(3) .item-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.execution-item:nth-child(4) .item-icon {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.execution-item:nth-child(5) .item-icon {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

/* 悬浮时保持对应的主题色 */
.execution-item:nth-child(1):hover .item-value { color: #5B7BFA; }
.execution-item:nth-child(2):hover .item-value { color: #ef4444; }
.execution-item:nth-child(3):hover .item-value { color: #f59e0b; }
.execution-item:nth-child(4):hover .item-value { color: #10b981; }
.execution-item:nth-child(5):hover .item-value { color: #8b5cf6; }

/* 可点击的执行项样式 */
.execution-item.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.execution-item.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 各个执行项的悬浮效果 */
.execution-item.clickable:nth-child(1):hover {
  background-color: rgba(91, 123, 250, 0.05);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.15);
}

.execution-item.clickable:nth-child(2):hover {
  background-color: rgba(239, 68, 68, 0.05);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
}

.execution-item.clickable:nth-child(3):hover {
  background-color: rgba(245, 158, 11, 0.05);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
}

.execution-item.clickable:nth-child(4):hover {
  background-color: rgba(16, 185, 129, 0.05);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

.execution-item.clickable:nth-child(5):hover {
  background-color: rgba(139, 92, 246, 0.05);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
}

/* 抽屉样式 */
.drawer-content {
  padding: 0;
}

/* 自定义抽屉头部样式 */
:deep(.el-drawer__header) {
  padding: 0 !important;
  margin-bottom: 0;
}

.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px 16px 8px;
  gap: 6px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

.drawer-close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  font-size: 16px;
  transition: color 0.3s;
}

.drawer-close-btn:hover {
  color: #606266;
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.drawer-icon {
  font-size: 18px;
  transition: color 0.3s ease;
  color: #ef4444; /* 默认红色 */
}

/* 根据图标类型设置不同颜色 */
.drawer-icon.icon-list {
  color: #3b82f6; /* 当日任务 - 蓝色，表示今日要处理的任务列表 */
}

.drawer-icon.icon-warning {
  color: #ef4444; /* 逾期任务 - 红色，表示紧急需要处理 */
}

.drawer-icon.icon-bell {
  color: #f59e0b; /* 当日到期任务 - 橙色，表示提醒和警示 */
}

.drawer-icon.icon-calendar {
  color: #10b981; /* 本周到期任务 - 绿色，表示计划中的时间安排 */
}

.drawer-icon.icon-documentchecked {
  color: #8b5cf6; /* 本月到期任务 - 紫色，表示需要完成的文档任务 */
}

/* 抽屉中的表格样式 */
:deep(.el-drawer__body) {
  padding: 20px;
}

:deep(.el-drawer .el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-drawer .el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-drawer .el-table__row:hover) {
  background-color: #f8faff;
}

:deep(.el-drawer .el-button--link) {
  color: #5B7BFA;
}

:deep(.el-drawer .el-button--link:hover) {
  color: #4a67d6;
}
</style>
