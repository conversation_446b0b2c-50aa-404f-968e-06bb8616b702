<template>
  <div class="tab-content">
    <div class="daily-report">
      <div class="daily-header">
        <h3><el-icon><Calendar /></el-icon> 项目日报</h3>
        <div class="daily-search">
          <span class="label">日期：</span>
          <el-date-picker
            v-model="reportDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 260px"
            @change="handleDateChange"
          />
          <el-button size="small" @click="resetDailySearch">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
          <!-- 填写日报按钮 - 只有项目经理且非只读模式才能看到 -->
          <el-button v-if="canEdit" type="primary" size="small" @click="addDailyReport">
            <el-icon><Plus /></el-icon> 填写日报
          </el-button>
        </div>
      </div>
      
      <div class="table-container">
        <div class="table-wrapper">
          <el-table
            :data="paginatedReports"
            border
            style="width: 100%"
            :stripe="true"
            :highlight-current-row="true">
            <el-table-column label="序号" type="index" width="60" align="center" :index="indexMethod" class-name="serial-column" />
            <el-table-column prop="reportDate" label="日期" width="110" align="center" show-overflow-tooltip />
            <el-table-column label="当日工作内容" min-width="250" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="work-content-cell">
                  {{ scope.row.workContent || '暂无内容' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="其他工作内容" min-width="250" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="work-content-cell">
                  {{ scope.row.otherWorkContent || '暂无内容' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="completionStatus" label="完成情况" width="120" align="left" show-overflow-tooltip />
            <el-table-column prop="problemsEncountered" label="遇到的问题" min-width="200" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="problem-cell">
                  {{ scope.row.problemsEncountered || '无' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="solutions" label="解决方案" min-width="200" align="left" show-overflow-tooltip>
              <template #default="scope">
                <div class="solution-cell">
                  {{ scope.row.solutions || '无' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="图片" width="70" align="center" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.imageCount > 0"
                  :content="`共${row.imageCount}张图片`"
                  placement="top">
                  <span class="image-count">{{ row.imageCount }}</span>
                </el-tooltip>
                <span v-else class="no-image">0</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="90" align="center" fixed="right">
              <template #default="{ row }">
                <div class="operation-column">
                  <el-button type="primary" link size="small" class="op-btn" @click="viewReport(row)">
                    <el-icon><Document /></el-icon>查看
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页组件 - 与项目干系人页面完全一致 -->
        <div class="pagination-container">
          <div class="pagination-info">
            共 {{ totalReports }} 条
          </div>
          <el-pagination
            v-model:current-page="currentReportPage"
            :page-size="reportsPageSize"
            :total="totalReports"
            :pager-count="5"
            layout="prev, pager, next"
            @current-change="handleReportPageChange"
          />
        </div>
      </div>
    </div>
  </div>
  
  <!-- 日报填写抽屉 -->
  <el-drawer
    v-model="reportDialogVisible"
    size="60%"
    direction="rtl"
    destroy-on-close
    class="report-drawer"
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="reportDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Document /></el-icon>
          {{ isViewMode ? '查看日报' : (currentEditingReport.isEdit ? '编辑日报' : '填写日报') }} - {{ currentProject?.projectName || '项目日报' }}
        </div>
      </div>
    </template>

    <div class="report-tabs">
      <el-tabs v-model="activeReportTab" class="drawer-tabs">
        <!-- 今日开工信息标签页 -->
        <el-tab-pane label="今日开工信息" name="workInfo">
          <div class="tab-content work-info-content drawer-optimized">
            <!-- 开工说明区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Edit /></el-icon>
                  <span class="section-title">开工说明</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.workDescription"
                    type="textarea"
                    :rows="4"
                    placeholder="请详细描述今日开工情况、工作内容等..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Picture /></el-icon>
                  <span class="section-title">现场图片</span>
                  <span class="section-subtitle">最多上传3张照片</span>
                </div>
                <div class="section-content">
                  <div class="upload-area-drawer">
                    <div class="upload-container-horizontal">
                      <el-upload
                        v-if="!isViewMode"
                        class="upload-box-drawer"
                        action="#"
                        :auto-upload="false"
                        :on-change="(file) => handleFileChange(file, 'work')"
                        :show-file-list="false"
                        :disabled="reportForm.workImages.length >= 3"
                      >
                        <div class="upload-trigger-drawer">
                          <el-icon class="upload-icon"><Plus /></el-icon>
                          <div class="upload-text">上传图片</div>
                        </div>
                      </el-upload>

                      <div v-if="reportForm.workImages.length > 0" class="image-list-horizontal">
                        <div v-for="(img, index) in reportForm.workImages" :key="index" class="image-item-horizontal">
                          <div class="image-container">
                            <img :src="img.url || img.preview || ''" alt="图片预览" class="image-thumb-drawer" v-if="img.url || img.preview" @click="handlePreviewImage(img)" />
                            <div class="image-thumb-empty" v-else>无预览</div>

                            <!-- 状态标识 -->
                            <div v-if="img.uploadError" class="upload-status-mask error">
                              <el-icon><Close /></el-icon>
                              <div class="upload-text">上传失败</div>
                            </div>
                            <div v-else-if="img.uploadSuccess && !img.isExisting" class="upload-success-badge">
                              <el-icon><Check /></el-icon>
                            </div>
                            <div v-else-if="img.isExisting" class="existing-image-badge">
                              <el-icon><Document /></el-icon>
                            </div>

                            <!-- 删除按钮 -->
                            <el-button
                              v-if="!isViewMode"
                              class="delete-btn-drawer"
                              size="small"
                              type="danger"
                              :icon="Close"
                              circle
                              @click="removeImage('work', index)"
                            />
                          </div>
                        </div>
                      </div>

                      <!-- 无图片时的提示 -->
                      <div v-if="isViewMode && reportForm.workImages.length === 0" class="no-images-tip">
                        <span style="color: #909399; font-size: 14px;">暂无图片</span>
                      </div>
                    </div>

                    <!-- 上传提示 -->
                    <div class="upload-tip" v-if="!isViewMode">
                      <el-icon><InfoFilled /></el-icon>
                      支持文件格式: .jpg, .png，单个文件不能超过50MB
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 当日计划进展标签页 -->
        <el-tab-pane label="当日计划进展" name="planProgress">
          <div class="tab-content drawer-optimized">
            <div class="form-section">
              <div class="section-layout">
                <div class="section-content">
            <el-table 
              :data="reportForm.taskProgress" 
              border 
              style="width: 100%"
              size="small"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '8px 0' }"
            >
              <el-table-column type="index" label="序号" width="50" align="center" />
              <el-table-column prop="taskName" label="工作项名称" width="100" show-overflow-tooltip />
              <el-table-column prop="planTime" label="计划时间" width="160" show-overflow-tooltip />
              <el-table-column prop="responsible" label="责任人" width="70" align="center" />
              <el-table-column prop="executorName" label="执行人" width="70" align="center" />
              <el-table-column prop="progress" label="当日进展" min-width="120" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="progress-input">
                    <el-input v-model="row.progressDesc" placeholder="点击添加进展" size="small" :disabled="isViewMode" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="任务状态" width="120" align="center">
                <template #default="{ row }">
                  <el-select v-model="row.status" placeholder="选择状态" size="small" :disabled="isViewMode" style="width: 90px; margin: 0 auto; display: block;">
                    <el-option
                      v-for="(label, code) in planStatusEnum"
                      :key="code"
                      :label="label"
                      :value="label"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="images" label="图片" width="120" align="center">
                <template #default="{ row, $index }">
                  <div v-if="row.images && row.images.length">
                    <div class="task-image-container">
                      <el-button type="primary" link size="small" @click="handlePreviewImage(row.images[0])">
                        {{ row.images[0].name }}
                      </el-button>
                      <!-- 上传错误状态显示 -->
                      <div v-if="row.images[0].uploadError" class="upload-status-inline error">
                        <el-icon><Close /></el-icon>
                        <span class="upload-text-small">失败</span>
                      </div>
                      <div v-else-if="row.images[0].uploadSuccess" class="upload-status-inline success">
                        <el-icon><Check /></el-icon>
                        <span class="upload-text-small">成功</span>
                      </div>
                    </div>
                  </div>
                  <el-upload
                    v-else-if="!isViewMode"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="(file) => handleTaskFileChange(file, $index)"
                  >
                    <el-button class="upload-image-btn" size="small">上传图片</el-button>
                  </el-upload>
                  <span v-else>无</span>
                </template>
              </el-table-column>
                </el-table>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 其他工作说明标签页 -->
        <el-tab-pane label="其他工作说明" name="otherWorkInfo">
          <div class="tab-content drawer-optimized">
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Document /></el-icon>
                  <span class="section-title">其他工作内容</span>
                </div>
                <div class="section-content">
                  <el-input v-model="reportForm.otherWorkContent" type="textarea" :rows="3" placeholder="请详细描述其他工作内容..." :disabled="isViewMode" />
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Check /></el-icon>
                  <span class="section-title">完成情况</span>
                </div>
                <div class="section-content">
                  <el-input v-model="reportForm.completionStatus" placeholder="请输入完成情况..." :disabled="isViewMode" />
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><InfoFilled /></el-icon>
                  <span class="section-title">遇到的问题</span>
                </div>
                <div class="section-content">
                  <el-input v-model="reportForm.problemsEncountered" placeholder="请输入遇到的问题..." :disabled="isViewMode" />
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Plus /></el-icon>
                  <span class="section-title">解决方案</span>
                </div>
                <div class="section-content">
                  <el-input v-model="reportForm.solutions" placeholder="请输入解决方案..." :disabled="isViewMode" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer v-if="!isViewMode && canEdit">
      <div class="drawer-footer">
        <el-button type="primary" @click="submitReport">提交</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 添加图片预览对话框 -->
  <el-dialog v-model="previewVisible" title="图片预览" width="800px" center>
    <img :src="previewUrl" alt="预览图片" style="width: 100%;">
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Calendar, Plus, Document, Close, Loading, Check, Edit, Picture, RefreshLeft, InfoFilled } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { getDailyList, getDailyListMock, getDailyDetail, getDailyDetailMock, saveDaily, saveDailyMock } from '@/api/daily';
import { getProjectPlanTree, getProjectPlanTreeMock } from '@/api/projectPlan';
import { getPlanStatus, getPlanStatusMock } from '@/api/enums';
import { uploadFile } from '@/api/fileUpload';
import eventBus, { EVENTS } from '@/utils/eventBus';

// 日期格式化函数
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  // 如果是完整的日期时间格式，只取日期部分
  if (dateTimeStr.includes(' ')) {
    return dateTimeStr.split(' ')[0];
  }

  // 如果是ISO格式，转换为日期
  if (dateTimeStr.includes('T')) {
    return dateTimeStr.split('T')[0];
  }

  return dateTimeStr;
};

// 格式化计划时间范围
const formatPlanTimeRange = (startTime, endTime) => {
  const formattedStart = formatDateTime(startTime);
  const formattedEnd = formatDateTime(endTime);

  if (!formattedStart && !formattedEnd) return '';
  if (!formattedEnd) return formattedStart;
  if (!formattedStart) return formattedEnd;

  // 如果开始和结束日期相同，只显示一个日期
  if (formattedStart === formattedEnd) {
    return formattedStart;
  }

  return `${formattedStart} - ${formattedEnd}`;
};

// 组件接收props
const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  projectName: {
    type: String,
    default: '项目日报'
  },
  readOnly: {
    type: Boolean,
    default: false
  }
});

// 用户store
const userStore = useUserStore();

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 权限控制：判断是否可以编辑（项目经理且不是只读模式）
const canEdit = computed(() => {
  return !props.readOnly && isProjectManager.value;
});

// 内部状态
const dailyReports = ref([]);

// 分页相关数据
const reportsPageSize = ref(8); // 动态分页大小，初始值为8
const currentReportPage = ref(1);
const reportDateRange = ref([]); // 修改为数组类型，适用于日期范围选择器

const totalReportsCount = ref(0); // 新增：存储从API返回的总数

// 智能分页计算 - 针对日报页面的动态行高优化
const calculateOptimalReportPageSize = () => {
  try {
    // 获取视口高度
    const viewportHeight = window.innerHeight

    // 项目详情页面的预留空间
    // 减去：顶部导航(60px) + 项目详情头部(120px) + 标签页(50px) + 日报搜索区域(60px) + 表格头部(40px) + 分页器(60px) + 安全边距(100px)
    const reservedHeight = 490
    const availableHeight = Math.max(viewportHeight - reservedHeight, 200)

    // 日报表格的特殊处理：
    // 由于"当日工作内容"等字段包含换行，平均行高比普通表格更高
    // 估算每行平均高度为40px（考虑换行内容）
    const avgRowHeight = 40

    // 计算可显示的行数，减少20%作为缓冲（更保守，因为内容可能很长）
    const maxRows = Math.floor(availableHeight / avgRowHeight * 0.8)

    // 根据屏幕高度设置合适的分页大小
    let pageSize
    if (viewportHeight <= 700) {
      // 小屏幕 - 5条（日报内容较多，显示更少）
      pageSize = Math.min(maxRows, 5)
    } else if (viewportHeight <= 800) {
      // 中小屏幕 - 6条
      pageSize = Math.min(maxRows, 6)
    } else if (viewportHeight <= 1000) {
      // 中等屏幕 - 8条
      pageSize = Math.min(maxRows, 8)
    } else {
      // 大屏幕 - 10条
      pageSize = Math.min(maxRows, 10)
    }

    // 确保最小值为5条（日报至少要能看到5条）
    return Math.max(5, pageSize)
  } catch (error) {
    console.warn('计算日报分页大小失败，使用默认值:', error)
    return 5 // 默认值
  }
}

// 窗口大小变化处理 - 日报页面
const handleReportResize = () => {
  const newPageSize = calculateOptimalReportPageSize()
  if (newPageSize !== reportsPageSize.value) {
    const oldPageSize = reportsPageSize.value
    reportsPageSize.value = newPageSize

    // 重新计算当前页，确保不超出范围
    if (totalReports.value > 0) {
      const maxPage = Math.ceil(totalReports.value / reportsPageSize.value)
      if (currentReportPage.value > maxPage && maxPage > 0) {
        currentReportPage.value = maxPage
      }
    }

    // 只有当分页大小真正改变时才重新获取数据
    if (oldPageSize !== newPageSize) {
      searchDailyReports()
    }
  }
}

// 防抖处理的窗口大小变化 - 日报页面
let reportResizeTimer = null
const debouncedHandleReportResize = () => {
  if (reportResizeTimer) {
    clearTimeout(reportResizeTimer)
  }
  reportResizeTimer = setTimeout(handleReportResize, 300)
}

// 日报弹窗相关状态
const reportDialogVisible = ref(false);
const activeReportTab = ref('workInfo');
const isViewMode = ref(false);
const currentProject = ref(null);
const planStatusEnum = ref({}); // 计划状态枚举

// 预览相关变量
const previewVisible = ref(false);
const previewUrl = ref('');



// 当前编辑的日报信息
const currentEditingReport = ref({
  id: null, // 日报ID，新增时为null，编辑时有值
  isEdit: false // 是否为编辑模式
});

// 日报表单数据
const reportForm = ref({
  workDescription: '',
  workImages: [],
  taskProgress: [
    { taskName: '任务A', planTime: '2023-04-20 09:00', responsible: '张三', executorName: '赵六', progressDesc: '', status: '进行中', images: [] },
    { taskName: '任务B', planTime: '2023-04-20 10:00', responsible: '李四', executorName: '钱七', progressDesc: '', status: '延期', images: [] },
    { taskName: '任务C', planTime: '2023-04-20 11:00', responsible: '王五', executorName: '孙八', progressDesc: '', status: '进行中', images: [] },
  ],
  otherWorkContent: '',
  completionStatus: '',
  problemsEncountered: '',
  solutions: '',
});



// 在组件挂载时初始化
onMounted(() => {
  // 初始化分页大小
  reportsPageSize.value = calculateOptimalReportPageSize();

  // 添加窗口大小变化监听
  window.addEventListener('resize', debouncedHandleReportResize);

  // 加载计划状态枚举
  loadPlanStatusEnum();
  // 只有当projectId有效时才加载数据
  if (props.projectId && props.projectId !== '1') {
    searchDailyReports();
  }
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', debouncedHandleReportResize);
  if (reportResizeTimer) {
    clearTimeout(reportResizeTimer);
  }
});

// 监听projectId变化，当projectId变化时重新加载数据
watch(() => props.projectId, (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId && newProjectId !== '1') {
    // 重置分页状态
    currentReportPage.value = 1;
    reportDateRange.value = [];
    // 重新加载数据
    searchDailyReports();
  }
}, { immediate: false });

// 根据搜索条件过滤的日报数据
const filteredReportsData = computed(() => {
  let results = [...dailyReports.value];
  
  // 按照日期降序排序，最新日期显示在前面
  results = results.sort((a, b) => {
    const dateA = new Date(a.reportDate.replace(/-/g, '/'));
    const dateB = new Date(b.reportDate.replace(/-/g, '/'));
    return dateB - dateA; // 降序排列
  });
  
  // 根据日期范围筛选
  if (reportDateRange.value && reportDateRange.value.length === 2) {
    const [startDate, endDate] = reportDateRange.value;
    results = results.filter(item => {
      const itemDate = new Date(item.reportDate.replace(/-/g, '/'));
      const start = new Date(startDate.replace(/-/g, '/'));
      const end = new Date(endDate.replace(/-/g, '/'));
      // 设置end为当天的最后一毫秒，以包含结束日期当天
      end.setHours(23, 59, 59, 999);
      return itemDate >= start && itemDate <= end;
    });
  }
  
  return results;
});

// 数据总数 - 直接使用API返回的总数
const totalReports = computed(() => {
  return totalReportsCount.value;
});



// 分页后的数据 - 直接使用API返回的数据（后端分页）
const paginatedReports = computed(() => {
  // 直接使用API返回的当前页数据，API已经处理了分页
  return dailyReports.value;
});

// 序号计算方法 - 后端分页
const indexMethod = (index) => {
  return (currentReportPage.value - 1) * reportsPageSize.value + index + 1;
};

// 页码变化处理函数
const handleReportPageChange = (page) => {
  currentReportPage.value = page;
  // 重新获取数据
  searchDailyReports();
};



// 日期选择器变化处理
const handleDateChange = () => {
  currentReportPage.value = 1; // 重置到第一页
  // 调用API进行搜索
  searchDailyReports();
};

// 查看日报按钮事件
const viewReport = async (row) => {
  try {
    isViewMode.value = true;
    currentProject.value = { projectName: props.projectName };
    activeReportTab.value = 'workInfo'; // 重置默认显示第一个tab

    // 调用日报详情接口
    let response;
    try {
      response = await getDailyDetail(row.id);
    } catch (apiError) {
      response = await getDailyDetailMock(row.id);
    }

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      const detail = response.data;


      // 处理日报图片
      const dailyImages = detail.dailyImages?.map((url, index) => ({
        name: `日报图片${index + 1}.jpg`,
        url: url,
        uid: Date.now() + index
      })) || [];

      // 处理任务进展数据
      const taskProgress = detail.planProgressList?.map(task => ({
        taskId: task.taskId,
        taskName: task.taskName || '任务',
        planTime: formatPlanTimeRange(task.planStartTime, task.planEndTime),
        responsible: task.ownerName || '',
        executorName: task.executorName || '',
        progressDesc: task.progressContent || '',
        status: getPlanStatusLabel(task.taskStatus),
        images: task.imageUrl ? [{
          name: '任务图片.jpg',
          url: task.imageUrl,
          uid: Date.now() + Math.random()
        }] : []
      })) || [];

      // 设置表单数据
      reportForm.value = {
        workDescription: detail.startContent || '',
        workImages: dailyImages,
        taskProgress: taskProgress.length > 0 ? taskProgress : [
          {
            taskName: '日常工作',
            planTime: formatDateTime(detail.reportDate),
            responsible: detail.reporterName || '张三',
            executorName: detail.reporterName || '张三',
            progressDesc: detail.startContent || '',
            status: '进行中',
            images: []
          }
        ],
        otherWorkContent: detail.otherContent || '',
        completionStatus: detail.completionStatus || '',
        problemsEncountered: detail.problems || '',
        solutions: detail.solutions || '',
      };
    } else {

      // 如果接口失败，使用列表数据填充
      const attachmentImages = [];
      if (row.imageCount > 0) {
        // 为每个图片创建占位符
        for (let i = 0; i < row.imageCount; i++) {
          attachmentImages.push({
            name: `图片${i + 1}.jpg`,
            url: 'https://via.placeholder.com/300x200?text=图片加载失败',
            preview: 'https://via.placeholder.com/300x200?text=图片加载失败',
            uid: Date.now() + i
          });
        }
      }

      reportForm.value = {
        workDescription: row.startContent || row.workContent || '',
        workImages: attachmentImages,
        taskProgress: [
          {
            taskName: '日常工作',
            planTime: formatDateTime(row.reportDate),
            responsible: '张三',
            executorName: '张三',
            progressDesc: row.workContent || '',
            status: '进行中',
            images: []
          }
        ],
        otherWorkContent: row.otherWorkContent || '',
        completionStatus: row.completionStatus || '',
        problemsEncountered: row.problemsEncountered || '',
        solutions: row.solutions || '',
      };
    }

    reportDialogVisible.value = true;

    // 抽屉不需要手动居中
    // setTimeout(() => {
    //   const drawer = document.querySelector('.report-drawer .el-drawer');
    //   // 抽屉自动处理位置
    // }, 50);

  } catch (error) {
    ElMessage.error('获取日报详情失败：' + (error.message || '系统错误'));
  }
};





// 检查当日是否已有日报
const checkTodayReport = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]; // 获取今日日期 YYYY-MM-DD

    // 查询当日日报
    const params = {
      projectId: props.projectId,
      reportDate: today, // 直接传入当日日期
      pageNum: 1,
      pageSize: 10
    };

    const response = await getDailyList(params);

    if (response && (response.code === 200 || response.code === 0) && response.data && response.data.length > 0) {
      const todayReport = response.data[0]; // 直接取第一条，因为已经按reportDate过滤了

      // 找到当日日报，返回编辑模式信息
      return {
        hasReport: true,
        reportId: todayReport.id,
        reportData: todayReport
      };
    }

    // 没有找到当日日报
    return {
      hasReport: false,
      reportId: null,
      reportData: null
    };

  } catch (error) {
    // 出错时默认为新增模式
    return {
      hasReport: false,
      reportId: null,
      reportData: null
    };
  }
};

// 填写日报 - 只有项目经理才能执行
const addDailyReport = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限填写日报');
    return;
  }

  try {
    isViewMode.value = false;
    currentProject.value = { projectName: props.projectName };

    // 1. 检查当日是否已有日报
    const todayReportCheck = await checkTodayReport();

    // 设置当前编辑状态
    currentEditingReport.value = {
      id: todayReportCheck.reportId,
      isEdit: todayReportCheck.hasReport
    };

    // 2. 加载计划状态枚举
    await loadPlanStatusEnum();

    // 3. 加载当日计划进展数据
    let taskList = [];
    let detailResponse; // 将声明移到外面，确保后续可以访问
    try {

      if (currentEditingReport.value.isEdit && currentEditingReport.value.id) {
        // 编辑模式：传入日报ID获取完整日报信息
        try {
          detailResponse = await getDailyDetail(currentEditingReport.value.id, props.projectId);

          if (detailResponse && (detailResponse.code === 200 || detailResponse.code === 0) && detailResponse.data) {
            const detail = detailResponse.data;

            // 处理任务进展数据
            if (detail.planProgressList && detail.planProgressList.length > 0) {
              taskList = detail.planProgressList.map(task => ({
                taskId: task.taskId,
                taskName: task.taskName || '任务',
                planTime: formatPlanTimeRange(task.planStartTime, task.planEndTime),
                responsible: task.ownerName || '',
                executorName: task.executorName || '',
                progressDesc: task.progressContent || '',
                status: getPlanStatusLabel(task.taskStatus),
                images: task.imageUrl ? [{
                  name: '任务图片.jpg',
                  url: task.imageUrl,
                  uid: Date.now() + Math.random()
                }] : []
              }));
            }
          }
        } catch (apiError) {
          console.warn('获取日报详情失败:', apiError);
        }
      } else {
        // 新增模式：不传ID，获取今日计划进展列表
        try {
          detailResponse = await getDailyDetail(null, props.projectId);

          if (detailResponse && (detailResponse.code === 200 || detailResponse.code === 0) && detailResponse.data) {
            const detail = detailResponse.data;

            // 处理任务进展数据
            if (detail.planProgressList && detail.planProgressList.length > 0) {
              taskList = detail.planProgressList.map(task => ({
                taskId: task.taskId,
                taskName: task.taskName || '任务',
                planTime: formatPlanTimeRange(task.planStartTime, task.planEndTime),
                responsible: task.ownerName || '',
                executorName: task.executorName || '',
                progressDesc: task.progressContent || '',
                status: getPlanStatusLabel(task.taskStatus),
                images: task.imageUrl ? [{
                  name: '任务图片.jpg',
                  url: task.imageUrl,
                  uid: Date.now() + Math.random()
                }] : []
              }));
            }
          }
        } catch (apiError) {
          console.warn('获取今日计划进展失败:', apiError);
        }
      }
    } catch (error) {
      console.warn('获取计划进展数据失败:', error);
    }

    // 如果没有加载到任务，taskList保持为空数组

    // 4. 初始化表单数据
    if (currentEditingReport.value.isEdit && detailResponse && detailResponse.data) {
      // 编辑模式：使用从详细接口获取的完整数据
      const detail = detailResponse.data;

      // 处理日报图片（如果有的话）
      const dailyImages = [];
      if (detail.dailyImages && Array.isArray(detail.dailyImages)) {
        detail.dailyImages.forEach((imageUrl, index) => {
          if (imageUrl) {
            dailyImages.push({
              url: imageUrl,
              uploadedUrl: imageUrl,
              uploadSuccess: true,
              name: `existing_image_${index + 1}.jpg`,
              isExisting: true // 标记为已存在的图片
            });
          }
        });
      }

      reportForm.value = {
        workDescription: detail.startContent || '',
        workImages: dailyImages,
        taskProgress: taskList, // 使用已获取的任务进展数据
        otherWorkContent: detail.otherContent || '',
        completionStatus: detail.completionStatus || '',
        problemsEncountered: detail.problems || '',
        solutions: detail.solutions || '',
      };
    } else {
      // 新增模式：初始化空表单
      reportForm.value = {
        workDescription: '',
        workImages: [],
        taskProgress: taskList, // 使用今日计划进展数据
        otherWorkContent: '',
        completionStatus: '',
        problemsEncountered: '',
        solutions: '',
      };
    }

    reportDialogVisible.value = true;

    // 抽屉不需要手动居中
    // setTimeout(() => {
    //   const drawer = document.querySelector('.report-drawer .el-drawer');
    //   // 抽屉自动处理位置
    // }, 50);

  } catch (error) {
    ElMessage.error('初始化日报填写失败');
  }
};

// 重置日报搜索
const resetDailySearch = () => {
  reportDateRange.value = [];
  currentReportPage.value = 1; // 重置到第一页
  // 重新获取数据
  searchDailyReports();
};

// 加载计划状态枚举
const loadPlanStatusEnum = async () => {
  try {
    let response;
    try {
      response = await getPlanStatus();
    } catch (apiError) {
      response = await getPlanStatusMock();
    }

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      planStatusEnum.value = response.data;
    } else {
      // 使用默认映射（根据接口文档）
      planStatusEnum.value = {
        '0': '未开始',
        '1': '进行中',
        '2': '已完成',
        '3': '逾期',
        '4': '逾期完成'
      };
    }
  } catch (error) {
    // 使用默认映射（根据接口文档）
    planStatusEnum.value = {
      '0': '未开始',
      '1': '进行中',
      '2': '已完成',
      '3': '逾期',
      '4': '逾期完成'
    };
  }
};

// 获取计划状态码（用于后端接口）
const getPlanStatusCode = (status) => {
  // 如果已经是状态码，直接返回
  if (planStatusEnum.value[status]) {
    return status;
  }

  // 根据中文描述查找对应的状态码
  for (const [code, desc] of Object.entries(planStatusEnum.value)) {
    if (desc === status) {
      return code;
    }
  }

  // 兜底映射（根据接口文档）
  const statusMap = {
    '未开始': '0',
    '进行中': '1',
    '已完成': '2',
    '逾期': '3',
    '逾期完成': '4'
  };
  return statusMap[status] || '1'; // 默认返回"进行中"
};

// 获取计划状态描述（用于前端显示）
const getPlanStatusLabel = (statusCode) => {
  // 如果传入的是中文描述，直接返回
  if (typeof statusCode === 'string' && planStatusEnum.value) {
    for (const [code, desc] of Object.entries(planStatusEnum.value)) {
      if (desc === statusCode) {
        return statusCode;
      }
    }
  }

  // 根据状态码获取中文描述
  return planStatusEnum.value[statusCode] || planStatusEnum.value['1'] || '进行中';
};

// 搜索日报数据
const searchDailyReports = async () => {
  // 检查projectId是否有效
  if (!props.projectId || props.projectId === '1') {
    dailyReports.value = [];
    totalReportsCount.value = 0;
    return;
  }

  try {
    const params = {
      projectId: props.projectId,
      pageNum: currentReportPage.value,
      pageSize: reportsPageSize.value
    };
    
    // 添加日期范围参数
    if (reportDateRange.value && reportDateRange.value.length === 2) {
      params.startDate = reportDateRange.value[0];
      params.endDate = reportDateRange.value[1];
    }
    
    // 调用 /api/project/daily/list 接口
    const response = await getDailyList(params);

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      // 处理分页数据
      if (response.data.records) {
        // 标准分页格式数据 {data: {records: [], total: 6}}
        dailyReports.value = response.data.records;
        totalReportsCount.value = response.data.total || 0;
      } else if (Array.isArray(response.data)) {
        // 数组格式数据，但有total字段 {data: [], total: 6}
        dailyReports.value = response.data;
        // 优先使用response.total，如果没有则使用数组长度
        totalReportsCount.value = response.total || response.data.length;
      } else {
        dailyReports.value = [];
        totalReportsCount.value = 0;
      }
    } else {
      // API返回错误
      dailyReports.value = [];
      totalReportsCount.value = 0;
    }
  } catch (error) {
    ElMessage.error('搜索日报失败');
  }
};

// 处理文件上传
const handleFileChange = async (file, type) => {
  if (type === 'work') {
    // 检查文件大小和类型
    const isJPG = file.raw.type === 'image/jpeg';
    const isPNG = file.raw.type === 'image/png';
    const isLt50M = file.raw.size / 1024 / 1024 < 50;

    if (!isJPG && !isPNG) {
      ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
      return;
    }
    if (!isLt50M) {
      ElMessage.error('上传图片大小不能超过 50MB!');
      return;
    }

    // 限制上传数量
    if (reportForm.value.workImages.length >= 3) {
      ElMessage.warning('最多只能上传3张图片');
      return;
    }

    // 创建预览URL
    if (file.raw) {
      file.preview = URL.createObjectURL(file.raw);
      file.url = file.preview; // 设置url用于显示
      // 移除假的上传状态，直接设置为成功
      file.uploadSuccess = true;
    }

    // 先添加到列表中显示预览
    reportForm.value.workImages.push(file);

    try {
      // 调用文件上传接口
      const uploadResponse = await uploadFile(file.raw, 50023, {
        // 可以根据需要添加可选参数
        // mimeType: 'image/',
        // maxSize: 50 * 1024 * 1024 // 50MB
      });

      // 检查API响应是否成功
      if (!uploadResponse || (uploadResponse.code !== 0 && uploadResponse.code !== 200)) {
        throw new Error(`API调用失败: ${uploadResponse?.msg || '未知错误'}`);
      }

      // 检查文件上传是否成功
      if (!uploadResponse.data || !uploadResponse.data.success) {
        const errorMsg = uploadResponse.data?.errorMessage || '文件上传失败';
        throw new Error(errorMsg);
      }

      // 检查是否获得了文件URL
      if (!uploadResponse.data.fileUrl) {
        throw new Error('未获得文件URL');
      }

      // 上传成功，更新文件信息
      file.uploadedUrl = uploadResponse.data.fileUrl;
      file.uploadSuccess = true;
      ElMessage.success('图片上传成功');

    } catch (error) {
      // 上传失败时移除文件
      file.uploadError = true;
      ElMessage.error('图片上传失败：' + (error.message || '网络错误'));

      // 上传失败，从列表中移除
      const index = reportForm.value.workImages.findIndex(img => img.uid === file.uid);
      if (index > -1) {
        reportForm.value.workImages.splice(index, 1);
      }
    }
  }
};

// 处理任务图片上传
const handleTaskFileChange = async (file, index) => {
  // 检查文件类型和大小
  const isJPG = file.raw.type === 'image/jpeg';
  const isPNG = file.raw.type === 'image/png';
  const isLt50M = file.raw.size / 1024 / 1024 < 50;

  if (!isJPG && !isPNG) {
    ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
    return;
  }
  if (!isLt50M) {
    ElMessage.error('上传图片大小不能超过 50MB!');
    return;
  }

  // 创建预览URL
  if (file.raw) {
    file.preview = URL.createObjectURL(file.raw);
    file.url = file.preview; // 设置url用于显示
    // 移除假的上传状态，直接设置为成功
    file.uploadSuccess = true;
  }

  // 先添加到列表中显示预览
  reportForm.value.taskProgress[index].images = [file];

  try {
    // 调用文件上传接口
    const uploadResponse = await uploadFile(file.raw, 50023, {
      // 可以根据需要添加可选参数
      // mimeType: 'image/',
      // maxSize: 50 * 1024 * 1024 // 50MB
    });

    // 检查API响应是否成功
    if (!uploadResponse || (uploadResponse.code !== 0 && uploadResponse.code !== 200)) {
      throw new Error(`API调用失败: ${uploadResponse?.msg || '未知错误'}`);
    }

    // 检查文件上传是否成功
    if (!uploadResponse.data || !uploadResponse.data.success) {
      const errorMsg = uploadResponse.data?.errorMessage || '文件上传失败';
      throw new Error(errorMsg);
    }

    // 检查是否获得了文件URL
    if (!uploadResponse.data.fileUrl) {
      throw new Error('未获得文件URL');
    }

    // 上传成功，更新文件信息
    file.uploadedUrl = uploadResponse.data.fileUrl;
    file.uploadSuccess = true;
    ElMessage.success('任务图片上传成功');

  } catch (error) {
    // 上传失败时移除文件
    file.uploadError = true;
    ElMessage.error('任务图片上传失败：' + (error.message || '网络错误'));

    // 上传失败，清空图片
    reportForm.value.taskProgress[index].images = [];
  }
};

// 移除图片
const removeImage = (type, index) => {
  if (type === 'work') {
    reportForm.value.workImages.splice(index, 1);
  } else {
    reportForm.value.taskProgress[index].images = [];
  }
};

// 添加图片预览方法
const handlePreviewImage = (file) => {
  previewUrl.value = file.url || file.preview || URL.createObjectURL(file.raw);
  previewVisible.value = true;
};

// 提交日报 - 只有项目经理才能执行
const submitReport = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限提交日报');
    return;
  }

  try {
    // 构建提交数据，按照接口文档格式
    const submitData = {
      projectId: props.projectId,
      reportDate: new Date().toISOString().split('T')[0], // 当前日期
      reporterId: userStore.userInfo?.userId || 1, // 从用户信息获取用户ID
      startDescription: reportForm.value.workDescription || '', // 开工说明（用户填写的内容）
      workContent: reportForm.value.workDescription || '', // 当日工作内容（与开工说明保持一致，确保列表显示）
      otherWorkContent: reportForm.value.otherWorkContent || '', // 其他工作内容
      completionStatus: reportForm.value.completionStatus || '', // 完成情况
      problemsEncountered: reportForm.value.problemsEncountered || '', // 遇到的问题
      solutions: reportForm.value.solutions || '', // 解决方案
      dailyImageUrls: reportForm.value.workImages?.map(img => {
        // 优先使用上传后的URL，否则使用原有URL
        const url = img.uploadedUrl || img.url || '';
        return url ? {
          url: url,
          btId: 50023, // 固定传50023
          objectName: img.name || 'daily_image.jpg' // 上传的文档的名字
        } : null;
      }).filter(item => item) || [], // 过滤空项
      planProgressList: reportForm.value.taskProgress?.map(task => ({
        taskId: task.taskId || 1,
        progressContent: task.progressDesc || '',
        imageUrl: task.images?.[0] ? (
          task.images[0].uploadedUrl ||
          task.images[0].url || ''
        ) : '',
        taskStatus: getPlanStatusCode(task.status), // 使用枚举状态码
        btId: '50023', // 固定传50023，注意这里是字符串类型
        objectName: task.images?.[0]?.name || 'task_image.jpg' // 上传的文档的名字
      })) || []
    };

    // 关键修改：根据编辑模式决定是否传递id
    if (currentEditingReport.value.isEdit && currentEditingReport.value.id) {
      // 编辑模式：传递日报ID
      submitData.id = currentEditingReport.value.id;
    }

    // 调用保存接口
    let response;
    try {
      response = await saveDaily(submitData);
    } catch (apiError) {
      response = await saveDailyMock(submitData);
    }

    if (response && (response.code === 200 || response.code === 0)) {
      const successMessage = currentEditingReport.value.isEdit ? '日报更新成功！' : '日报提交成功！';
      ElMessage.success(successMessage);
      reportDialogVisible.value = false;

      // 重置编辑状态
      currentEditingReport.value = {
        id: null,
        isEdit: false
      };

      // 刷新日报列表
      searchDailyReports();

      // 触发全局事件，通知工作台页面更新日报状态
      const wasEdit = currentEditingReport.value.isEdit; // 保存编辑状态，因为下面会重置
      eventBus.emit(EVENTS.DAILY_REPORT_SUBMITTED, {
        projectId: props.projectId,
        reportDate: new Date().toISOString().split('T')[0],
        isEdit: wasEdit
      });
    } else {
      ElMessage.error(response?.msg || '日报提交失败');
    }
  } catch (error) {
    ElMessage.error('日报提交失败：' + (error.message || '系统错误'));
  }
};
</script>

<style lang="scss" scoped>
/* tab-content 样式 */
.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 日报样式 */
.daily-report {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  overflow: hidden;
}

/* 顶部区域 */
.daily-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: none;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  margin-left: 5px;
  margin-right: 5px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.daily-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.daily-header h3 .el-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #409EFF;
}

.daily-search {
  display: flex;
  align-items: center;
  gap: 8px;
}

.daily-search .label {
  font-size: 14px;
  color: #606266;
}

/* 表格容器样式 - 修正分页器位置 */
.table-container {
  flex: none; /* 改为none，让容器根据内容自适应高度 */
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding: 0;
  height: auto;
  min-height: auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin: 0 5px 5px 5px;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  min-height: 0; /* 修复 flex 子项的最小高度问题 */
}

/* 表格样式统一 */
:deep(.el-table) {
  border-radius: 0;
  width: 100% !important;
  table-layout: fixed;
  --el-table-border-color: #e6e6e6;
  --el-table-header-bg-color: #f2f6fc;
  --el-table-row-hover-bg-color: #f5f7fa;
}

/* 表格外层容器样式 */
:deep(.el-table__inner-wrapper) {
  height: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 13px;
  color: #333;
}

/* 表头样式 */
:deep(.el-table__header-wrapper th) {
  background-color: #f2f6fc;
  color: #333;
  font-weight: 500;
  height: 40px;
  padding: 8px 0;
  border-bottom: 1px solid #e0e3e9;
}

:deep(.el-table__header-wrapper th.el-table__cell) {
  background-color: #f2f6fc;
}

:deep(.el-table__header-wrapper th.el-table__cell .cell) {
  font-weight: 500;
  color: #333;
  padding: 0 4px;
}

/* 行样式 */
:deep(.el-table__row) {
  height: 32px !important;
}

/* 确保所有行高度一致 */
:deep(.el-table__cell) {
  padding: 0 !important;
  height: 32px !important;
  line-height: 32px !important;
}

/* 表格单元格内容样式 */
:deep(.el-table .cell) {
  padding: 0 4px !important;
  line-height: 32px !important;
}

/* 序号列样式调整 */
.serial-column {
  background-color: #f5f7fa !important;
}

/* 操作列样式优化 */
.operation-column {
  display: flex;
  justify-content: center;
  gap: 4px;
}

/* 操作按钮特定样式 */
.operation-column .op-btn {
  padding: 0 4px !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;
}

/* 确保图标和文本之间的间距合适 */
.operation-column .op-btn .el-icon {
  margin-right: 2px !important;
  font-size: 14px !important;
}

/* 确保按钮之间没有额外的间距 */
.operation-column .op-btn + .op-btn {
  margin-left: 0 !important;
}

/* 加载动画 */
.is-loading {
  animation: loading-rotate 2s linear infinite;
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 分页器容器优化 - 与项目干系人页面保持一致 */
.pagination-container {
  margin-top: 0;
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #f8faff 100%);
  border-top: 1px solid #f0f2f5;
  border-radius: 0 0 8px 8px;
  min-height: 32px;
  box-sizing: border-box;
}

.pagination-info {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

/* 分页器本身紧凑展示 - 与项目干系人页面保持一致 */
:deep(.el-pagination) {
  padding: 0;
  height: 24px;
  display: flex;
  align-items: center;
}

:deep(.el-pagination button) {
  min-width: 24px;
  height: 24px;
  border-radius: 3px;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  font-size: 11px;
}

:deep(.el-pagination button:hover:not(:disabled)) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
}

:deep(.el-pagination button:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 3px;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  margin: 0 1px;
}

:deep(.el-pagination .el-pager li:hover) {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-color: #5B7BFA;
  color: #5B7BFA;
  transform: translateY(-1px);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
  transform: translateY(-1px);
}

:deep(.el-input__wrapper) {
  padding: 0 8px;
}

/* 按钮样式已在全局统一，移除本地自定义样式 */



/* 日报抽屉样式 */
.report-drawer {
  /* 覆盖 Element Plus 抽屉默认样式 */
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0 !important;
  }

  :deep(.el-drawer__body) {
    padding: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  :deep(.el-drawer__footer) {
    padding: 0 !important;
    margin: 0 !important;
  }

  .drawer-footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
    display: flex;
    justify-content: flex-start;
    gap: 8px;
    margin: 0; /* 确保没有额外的外边距 */
  }
}

/* 自定义抽屉头部 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px 16px 8px !important;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  gap: 6px;
}

.drawer-close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  font-size: 16px;
  transition: color 0.3s;
}

.drawer-close-btn:hover {
  color: #606266;
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  flex: 1;
  margin-left: 16px;
}

.drawer-icon {
  color: #409EFF;
  font-size: 18px;
}

/* 抽屉底部按钮 */
.report-drawer .drawer-footer {
  padding: 16px 24px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}

.drawer-footer .el-button {
  padding: 8px 18px;
  height: 32px;
  font-size: 13px;
}

.drawer-footer .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #FFFFFF;
}

/* 抽屉优化布局 */
.drawer-optimized {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px 0;
}

/* 统一的布局样式 */
.section-layout {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: start;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  grid-column: 1 / -1;
  margin-bottom: 12px;
}

.section-icon {
  color: #409EFF;
  font-size: 16px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1;
  display: flex;
  align-items: center;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
  line-height: 16px;
}

.section-content {
  grid-column: 1 / -1;
  margin-left: 0;
}

/* 表单区域间距 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 抽屉标签页样式 */
.drawer-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 图片上传区域样式 */
.upload-area-drawer {
  width: 100%;
}

.upload-container-horizontal {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
}

.upload-box-drawer {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-box-drawer:hover {
  border-color: #409EFF;
}

.upload-trigger-drawer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
}

.image-list-horizontal {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.image-item-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.image-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.image-thumb-drawer {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-thumb-drawer:hover {
  transform: scale(1.05);
}

.delete-btn-drawer {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  min-height: 20px;
  padding: 0;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
}

.delete-btn-drawer:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 状态标识样式 */
.upload-status-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.upload-status-mask.error {
  background: rgba(245, 108, 108, 0.8);
}

.upload-success-badge,
.existing-image-badge {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.upload-success-badge {
  background: #67c23a;
}

.existing-image-badge {
  background: #409eff;
}

/* 输入框样式优化 */
.description-input {
  width: 100%;
}

.description-input :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.description-input :deep(.el-textarea__inner):focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 上传提示样式 */
.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-tip .el-icon {
  font-size: 14px;
}

/* 全局上传提示 */
.upload-tip-global {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.report-header {
  padding: 0 0 12px 0;
}

.project-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.report-tabs {
  margin-top: 0;
  padding: 0 24px;
}

.tab-content {
  padding: 12px 0;
}

.work-info-content {
  padding: 16px 8px;
}

.other-work-content {
  padding: 16px 8px;
}

/* 上传区域样式 */
.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.upload-box {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #f5f7fa;
  position: relative;
}

.upload-box:hover {
  border-color: #409EFF;
}

.upload-trigger {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-left: 0;
}

/* 图片缩略图样式优化 */
.image-thumb-box {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.image-thumb-box:hover {
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: all 0.3s;
}

.image-thumb:hover {
  transform: scale(1.05);
}

.image-thumb-empty {
  color: #bbb;
  font-size: 12px;
}

.delete-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  background: rgba(255,255,255,0.7);
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
  padding: 2px;
  transition: all 0.3s;
}

.delete-icon:hover {
  background: rgba(255,255,255,0.9);
  color: #F56C6C;
  transform: scale(1.1);
}

.thumb-delete {
  font-size: 16px;
}

.file-name-thumb {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0,0,0,0.4);
  color: #fff;
  font-size: 12px;
  text-align: center;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 0 0 4px 4px;
}

.upload-notice {
  margin-top: 16px;
  padding: 10px 16px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.upload-list-title {
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
}

.numbering {
  font-weight: bold;
  color: #409EFF;
  margin-right: 4px;
}

.upload-tip-global {
  position: absolute;
  right: 32px;
  bottom: 24px;
  color: #909399;
  font-size: 13px;
  line-height: 1.5;
  z-index: 2;
  background: transparent;
  pointer-events: none;
}

/* 输入框样式 */
.description-input :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  min-height: 80px;
  resize: vertical;
}

.description-input :deep(.el-textarea__inner:focus) {
  border-color: #409EFF;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

/* 其他工作说明表单样式 */
.compact-form {
  margin-top: 8px;
}

.compact-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.compact-form :deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
  padding-right: 12px;
  line-height: 32px;
}

.compact-form :deep(.el-form-item__content) {
  line-height: 32px;
  width: 100%;
}

.compact-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  background-color: #fff;
  padding: 1px 11px;
  height: 36px;
  border-radius: 4px;
}



/* 对话框按钮样式已在全局统一，移除本地自定义样式 */

/* 标签页样式优化 */
.report-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
}

.report-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #e4e7ed;
}

.report-tabs :deep(.el-tabs__item) {
  font-size: 14px;
}

/* 上传状态样式 */
.upload-status-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 4px;
}

.upload-status-mask.error {
  background-color: rgba(245, 108, 108, 0.8);
}

.upload-status-mask .upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.upload-success-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #67c23a;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.existing-image-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #409EFF;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 任务图片上传状态 */
.task-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.upload-status-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.upload-status-inline.error {
  color: #f56c6c;
}

.upload-status-inline.success {
  color: #67c23a;
}

.upload-text-small {
  font-size: 11px;
}

.report-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 500;
}

.report-tabs :deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #409EFF;
}

/* 工作内容单元格样式 */
.work-content-cell {
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  max-height: 120px;
  overflow-y: auto;
  padding: 4px 0;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
}

/* 问题单元格样式 */
.problem-cell {
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
}

/* 解决方案单元格样式 */
.solution-cell {
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
}

/* 图片数量样式 */
.image-count {
  color: #409EFF;
  font-weight: 500;
  cursor: pointer;
}

.no-image {
  color: #C0C4CC;
  font-size: 12px;
}

/* 表格行悬浮效果 */
.el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 表格单元格内边距优化 */
.el-table .cell {
  padding: 8px 12px;
  line-height: 1.4;
}

/* 序号列样式 */
.serial-column .cell {
  font-weight: 500;
  color: #909399;
}

/* 操作列按钮样式 */
.operation-column .op-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.operation-column .op-btn:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}
</style>