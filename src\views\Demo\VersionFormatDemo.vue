<template>
  <div class="version-demo">
    <el-card class="demo-card">
      <template #header>
        <span>版本号格式化演示</span>
      </template>
      
      <div class="demo-section">
        <h4>版本状态枚举</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="预审核阶段">{{ versionStatusEnum['0'] || '0' }}</el-descriptions-item>
          <el-descriptions-item label="正式审核阶段">{{ versionStatusEnum['1'] || '1' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="demo-section">
        <h4>版本号格式化规则</h4>
        <el-alert type="info" :closable="false">
          <ul>
            <li><strong>预审核阶段 (versionStatus = 0):</strong> 版本号格式为 0.1、0.2、0.3...</li>
            <li><strong>正式审核阶段 (versionStatus = 1):</strong> 版本号格式为 1.0、2.0、3.0...</li>
          </ul>
        </el-alert>
      </div>

      <div class="demo-section">
        <h4>格式化示例</h4>
        <el-table :data="demoData" border stripe>
          <el-table-column label="原始版本号" prop="version" width="120" />
          <el-table-column label="版本状态" prop="versionStatus" width="120">
            <template #default="{ row }">
              {{ versionStatusEnum[row.versionStatus] || row.versionStatus }}
            </template>
          </el-table-column>
          <el-table-column label="格式化后" prop="formatted" width="120" />
          <el-table-column label="说明" prop="description" />
        </el-table>
      </div>

      <div class="demo-section">
        <h4>实时测试</h4>
        <el-form :model="testForm" inline>
          <el-form-item label="版本号:">
            <el-input-number v-model="testForm.version" :min="1" :max="99" />
          </el-form-item>
          <el-form-item label="版本状态:">
            <el-select v-model="testForm.versionStatus">
              <el-option label="预审核阶段" :value="0" />
              <el-option label="正式审核阶段" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-tag type="success" size="large">
              {{ formatVersionNumber(testForm.version, testForm.versionStatus) }}
            </el-tag>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { formatVersionNumber } from '@/utils/versionFormatter';
import { getVersionStatusApi } from '@/api/enums';

// 版本状态枚举
const versionStatusEnum = ref({});

// 演示数据
const demoData = ref([
  { version: 1, versionStatus: 0, formatted: 'v0.1', description: '第一个预审核版本' },
  { version: 2, versionStatus: 0, formatted: 'v0.2', description: '第二个预审核版本' },
  { version: 3, versionStatus: 0, formatted: 'v0.3', description: '第三个预审核版本' },
  { version: 1, versionStatus: 1, formatted: 'v1.0', description: '第一个正式版本' },
  { version: 2, versionStatus: 1, formatted: 'v2.0', description: '第二个正式版本' },
  { version: 3, versionStatus: 1, formatted: 'v3.0', description: '第三个正式版本' }
]);

// 测试表单
const testForm = ref({
  version: 1,
  versionStatus: 0
});

// 获取版本状态枚举
const fetchVersionStatusEnum = async () => {
  try {
    const response = await getVersionStatusApi();
    if (response && (response.code === 0 || response.code === 200) && response.data) {
      versionStatusEnum.value = response.data;
    } else {
      // 使用默认枚举
      versionStatusEnum.value = {
        "0": "预审核阶段",
        "1": "正式审核阶段"
      };
    }
  } catch (error) {
    console.error('获取版本状态枚举失败:', error);
    // 使用默认枚举
    versionStatusEnum.value = {
      "0": "预审核阶段", 
      "1": "正式审核阶段"
    };
  }
};

onMounted(() => {
  fetchVersionStatusEnum();
  
  // 更新演示数据的格式化结果
  demoData.value = demoData.value.map(item => ({
    ...item,
    formatted: formatVersionNumber(item.version, item.versionStatus)
  }));
});
</script>

<style scoped>
.version-demo {
  padding: 20px;
}

.demo-card {
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h4 {
  margin-bottom: 15px;
  color: #409eff;
}

.demo-section ul {
  margin: 0;
  padding-left: 20px;
}

.demo-section li {
  margin-bottom: 8px;
}
</style>
