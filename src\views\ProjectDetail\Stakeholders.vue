<template>
  <div class="tab-content">
    <div class="stakeholders">
      <div class="stakeholders-header">
        <div class="header-flex">
          <h3><el-icon><UserFilled /></el-icon> 项目干系人</h3>
          <!-- 操作按钮 - 只有项目经理且非只读模式才能看到 -->
          <div class="stakeholders-actions" v-if="canEdit">
            <el-button type="primary" size="small" @click="addExternalStakeholder">
              <el-icon><Plus /></el-icon> 添加外部人员
            </el-button>
            <el-button type="primary" size="small" @click="addInternalStakeholder">
              <el-icon><Plus /></el-icon> 添加内部人员
            </el-button>
            <el-button size="small" @click="removeSelectedStakeholders">
              <el-icon><Delete /></el-icon> 移除
            </el-button>
          </div>
          
          <div class="search-area">
            <span class="label">关键词：</span>
            <el-input 
              v-model="searchKeyword" 
              placeholder="请输入姓名/角色" 
              clearable 
              style="width: 180px" 
              @input="handleSearchInput"
            />
            <span class="label">类型：</span>
            <el-select 
              v-model="stakeholderTypeFilter" 
              placeholder="请选择" 
              style="width: 180px" 
              clearable
              @change="handleTypeFilterChange"
            >
              <el-option label="外部人员" value="外部人员" />
              <el-option label="内部人员" value="内部人员" />
            </el-select>
            <el-button size="small" @click="resetStakeholderSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="table-container">
        <div class="table-wrapper">
          <el-table
            :data="stakeholdersData"
            border 
            style="width: 100%"
            :stripe="true"
            :highlight-current-row="true"
            v-loading="loading"
            @selection-change="handleStakeholderSelectionChange">
            <!-- 选择列 - 只有项目经理且非只读模式才能看到 -->
            <el-table-column v-if="canEdit" type="selection" width="50" align="center" />
            <el-table-column label="序号" type="index" width="70" align="center" class-name="serial-column" :index="indexMethod" />
            <el-table-column prop="type" label="类型" width="100" align="center" />
            <el-table-column prop="department" label="单位/部门" min-width="180" align="center" show-overflow-tooltip />
            <el-table-column prop="name" label="姓名" width="100" align="center" />
            <el-table-column prop="role" label="项目角色" width="120" align="center" />
            <el-table-column prop="contact" label="联系方式" min-width="150" align="center" />
            <!-- 操作列 - 只有项目经理且非只读模式才能看到 -->
            <el-table-column v-if="canEdit" label="操作" width="150" align="center" fixed="right">
              <template #default="{ row }">
                <div class="operation-column">
                  <el-button type="primary" link size="small" class="op-btn" @click="editStakeholder(row)">
                    <el-icon><Edit /></el-icon>编辑
                  </el-button>
                  <el-button type="danger" link size="small" class="op-btn" @click="deleteStakeholder(row)">
                    <el-icon><Delete /></el-icon>移除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页组件 - 参考全部项目页面的样式 -->
        <div class="pagination-container">
          <div class="pagination-info">
            共 {{ totalStakeholders }} 条
          </div>
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="totalStakeholders"
            :pager-count="5"
            layout="prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
  
  <!-- 添加外部人员弹窗 -->
  <el-dialog
    v-model="externalDialogVisible"
    title="添加外部人员"
    width="600px"
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    destroy-on-close
  >
    <div class="dialog-content">
      <el-form :model="externalForm" label-position="right" label-width="100px">
        <el-form-item label="姓名：">
          <el-input v-model="externalForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="单位/部门：">
          <el-input v-model="externalForm.department" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="项目角色：">
          <el-input v-model="externalForm.role" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="联系方式：">
          <el-input v-model="externalForm.contact" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="externalDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddExternalStakeholder">确定</el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 添加内部人员弹窗 -->
  <el-dialog
    v-model="internalDialogVisible"
    title="添加内部人员"
    width="600px"
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    destroy-on-close
  >
    <div class="dialog-content">
      <el-form :model="internalForm" label-position="right" label-width="100px">
        <el-form-item label="姓名：">
          <el-select 
            v-model="internalForm.name" 
            placeholder="请选择" 
            filterable 
            :loading="internalUsersLoading"
            :disabled="internalUsersLoading"
          >
            <el-option
              v-for="item in internalUserOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目角色：">
          <el-input v-model="internalForm.role" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="internalDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddInternalStakeholder">确定</el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 编辑干系人弹窗 -->
  <el-dialog
    v-model="editDialogVisible"
    title="编辑"
    width="600px"
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    destroy-on-close
  >
    <div class="dialog-content">
      <el-form :model="editForm" label-position="right" label-width="100px">
        <el-form-item label="姓名：">
          <el-input v-model="editForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="类型：">
          <el-input v-model="editForm.type" placeholder="请输入" disabled />
        </el-form-item>
        <el-form-item label="单位/部门：">
          <el-input v-model="editForm.department" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="项目角色：">
          <el-input v-model="editForm.role" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="联系方式：">
          <el-input v-model="editForm.contact" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmEditStakeholder">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh, Plus, Delete, UserFilled, Edit } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import {
  getStakeholderList,
  getStakeholderListAll,
  addExternalStakeholder as addExternalStakeholderAPI,
  addInternalStakeholder as addInternalStakeholderAPI,
  editExternalStakeholder as editExternalStakeholderAPI,
  editInternalStakeholder as editInternalStakeholderAPI,
  deleteStakeholder as deleteStakeholderAPI,
  getStakeholderListMock
} from '@/api/stakeholder';
import { getUsersByRole } from '@/api/user';

// 组件接收props
const props = defineProps({
  stakeholders: {
    type: Array,
    required: true
  },
  projectId: {
    type: String,
    required: true
  },
  contractNo: {
    type: String,
    default: ''
  },
  readOnly: {
    type: Boolean,
    default: false
  }
});

// 事件发射
const emit = defineEmits(['update:stakeholders']);

// 用户store
const userStore = useUserStore();

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 权限控制：判断是否可以编辑（项目经理且不是只读模式）
const canEdit = computed(() => {
  return !props.readOnly && isProjectManager.value;
});

// 数据加载状态
const loading = ref(false);
// 选中的干系人
const selectedStakeholders = ref([]);
// 干系人类型过滤
const stakeholderTypeFilter = ref('');
// 搜索关键词
const searchKeyword = ref('');
// 干系人数据
const stakeholdersData = ref([]);

// 动态计算分页大小 - 针对项目干系人页面优化
const calculateOptimalPageSize = () => {
  try {
    // 获取视口高度
    const viewportHeight = window.innerHeight

    // 根据屏幕高度设置合适的分页大小
    let pageSize
    if (viewportHeight <= 700) {
      // 小屏幕 - 10条
      pageSize = 10
    } else if (viewportHeight <= 800) {
      // 中小屏幕 - 12条
      pageSize = 12
    } else if (viewportHeight <= 1000) {
      // 中等屏幕 - 15条
      pageSize = 15
    } else {
      // 大屏幕 - 18条
      pageSize = 18
    }

    return pageSize
  } catch (error) {
    console.warn('计算分页大小失败，使用默认值:', error)
    return 10 // 默认值
  }
}

// 窗口大小变化处理
const handleResize = () => {
  const newPageSize = calculateOptimalPageSize()
  if (newPageSize !== pageSize.value) {
    pageSize.value = newPageSize
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(totalStakeholders.value / pageSize.value)
    if (currentPage.value > maxPage && maxPage > 0) {
      currentPage.value = maxPage
    }
    // 重新获取数据
    getStakeholdersData()
  }
}

// 防抖处理的窗口大小变化
let resizeTimer = null
const debouncedHandleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(handleResize, 300)
}

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10); // 保持响应式，将动态计算
const totalStakeholders = ref(0);
const totalPages = ref(0);
const gotoPage = ref('1'); // 初始化为字符串'1'

// 弹窗显示状态
const externalDialogVisible = ref(false);
const internalDialogVisible = ref(false); // 新增内部人员弹窗状态
const editDialogVisible = ref(false); // 新增编辑干系人弹窗状态

// 外部人员表单数据
const externalForm = ref({
  name: '',
  department: '',
  role: '',
  contact: ''
});

// 内部人员表单数据
const internalForm = ref({
  name: '', // 存储用户ID
  role: ''
});

// 获取选中用户的显示名称
const getSelectedUserName = computed(() => {
  if (!internalForm.value.name) return '';
  const selectedUser = internalUserOptions.value.find(user => user.value === internalForm.value.name);
  return selectedUser ? selectedUser.label : '';
});

// 编辑干系人表单数据
const editForm = ref({
  id: null,
  name: '',
  type: '',
  department: '',
  role: '',
  contact: ''
});

// 内部人员选项
const internalUserOptions = ref([]);
const internalUsersLoading = ref(false);

// 监听当前页码变化，同步更新输入框中的页码
watch(currentPage, (newPage) => {
  gotoPage.value = newPage.toString();
});

// 获取干系人数据
const getStakeholdersData = async () => {
  // 优先使用props中的contractNo，如果没有则从sessionStorage获取
  const contractNo = props.contractNo || sessionStorage.getItem('currentContractNo');

  if (!contractNo) {
    console.warn('合同号不存在，无法获取干系人数据');
    return;
  }

  loading.value = true;
  try {
    // 构建分页查询参数
    const params = {
      contractNo: contractNo,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    // 添加搜索条件
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value;
    }
    if (stakeholderTypeFilter.value) {
      params.stakeholderType = stakeholderTypeFilter.value === '外部人员' ? '1' : '2';
    }

    // 尝试调用真实API
    let response;
    try {
      response = await getStakeholderList(params);
    } catch (apiError) {
      response = await getStakeholderListMock(params);
    }

    if (response && response.data) {
      // 转换API数据格式为组件需要的格式
      const transformedData = response.data.map(item => ({
        id: item.id,
        type: item.source === '1' ? '外部人员' : '内部人员',
        name: item.stakeholderUserName,
        department: item.department || '-',
        role: item.projectRole,
        contact: item.mobile || '-',
        // 保留原始数据用于编辑
        originalData: item
      }));

      stakeholdersData.value = transformedData;

      // 更新分页信息
      totalStakeholders.value = response.total || 0;
      calculateTotalPages();

      // 获取全量数据用于传递给父组件（项目计划责任人下拉菜单使用）
      await getStakeholdersForParent();
    } else {
      stakeholdersData.value = [];
      totalStakeholders.value = 0;
      calculateTotalPages();
      emit('update:stakeholders', []);
    }
  } catch (error) {
    console.error('获取干系人数据失败:', error);
    ElMessage.error('获取干系人数据失败：' + (error.message || '网络错误'));
    stakeholdersData.value = [];
    totalStakeholders.value = 0;
    calculateTotalPages();
    emit('update:stakeholders', []);
  } finally {
    loading.value = false;
  }
};

// 获取全量干系人数据用于传递给父组件（项目计划责任人下拉菜单使用）
const getStakeholdersForParent = async () => {
  try {
    // 优先使用props中的contractNo，如果没有则从sessionStorage获取
    const contractNo = props.contractNo || sessionStorage.getItem('currentContractNo');

    if (!contractNo) {
      console.warn('合同号不存在，无法获取全量干系人数据');
      emit('update:stakeholders', []);
      return;
    }

    const response = await getStakeholderListAll({ contractNo: contractNo });

    if (response && response.data) {
      // 转换API数据格式为组件需要的格式
      const transformedData = response.data.map(item => ({
        id: item.id,
        type: item.source === '1' ? '外部人员' : '内部人员',
        name: item.stakeholderUserName,
        department: item.department || '-',
        role: item.projectRole,
        contact: item.mobile || '-',
        // 保留原始数据用于编辑
        originalData: item
      }));

      // 通知父组件更新数据
      emit('update:stakeholders', transformedData);
    } else {
      emit('update:stakeholders', []);
    }
  } catch (error) {
    console.error('获取全量干系人数据失败:', error);
    emit('update:stakeholders', []);
  }
};

// 监听合同号变化，重新获取数据
watch(() => props.contractNo, (newContractNo, oldContractNo) => {
  if (newContractNo && newContractNo !== oldContractNo) {
    // 重新初始化分页大小并获取数据
    pageSize.value = calculateOptimalPageSize();
    getStakeholdersData();
  }
}, { immediate: false }); // 改为 false，避免与onMounted重复执行

// 获取内部成员列表
const getInternalUsers = async () => {
  internalUsersLoading.value = true;
  try {
    // 调用API获取内部成员，不传roleId参数
    const response = await getUsersByRole();
    
    if (response && response.data) {
      // 转换数据格式为下拉选项
      internalUserOptions.value = response.data.map(user => ({
        label: user.realName || user.username || user.name,
        value: user.id || user.userId,
        // 保留原始数据
        originalData: user
      }));
    } else {

      internalUserOptions.value = [];
    }
  } catch (error) {
    console.error('获取内部成员列表失败:', error);
    ElMessage.error('获取内部成员列表失败：' + (error.message || '网络错误'));
    internalUserOptions.value = [];
  } finally {
    internalUsersLoading.value = false;
  }
};

// 在组件挂载时获取干系人数据
onMounted(async () => {
  // 初始化分页大小
  pageSize.value = calculateOptimalPageSize();

  // 等待分页大小设置完成后再获取数据
  await getStakeholdersData();
  getInternalUsers(); // 同时获取内部成员列表

  // 监听窗口大小变化
  window.addEventListener('resize', debouncedHandleResize);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 清理窗口大小变化监听
  window.removeEventListener('resize', debouncedHandleResize);

  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
});

// 处理选择变更
const handleStakeholderSelectionChange = (selection) => {
  selectedStakeholders.value = selection;
};

// 处理搜索输入变化
const handleSearchInput = () => {
  currentPage.value = 1; // 重置到第一页
  gotoPage.value = '1'; // 同步更新输入框中的页码
  getStakeholdersData(); // 重新获取数据
};

// 处理类型筛选变化
const handleTypeFilterChange = () => {
  currentPage.value = 1; // 重置到第一页
  gotoPage.value = '1'; // 同步更新输入框中的页码
  getStakeholdersData(); // 重新获取数据
};



// 计算总页数
const calculateTotalPages = () => {
  totalPages.value = Math.ceil(totalStakeholders.value / pageSize.value);
  
  // 如果当前页超出总页数，重置到第一页
  if (currentPage.value > totalPages.value && totalPages.value > 0) {
    currentPage.value = 1;
    gotoPage.value = '1';
  }
  
  // 如果没有数据，确保当前页为1
  if (totalPages.value === 0) {
    currentPage.value = 1;
    gotoPage.value = '1';
  }
};

// 处理页码变化
const handlePageChange = (newPage) => {
  currentPage.value = newPage;
  gotoPage.value = newPage.toString(); // 同步更新输入框中的页码
  getStakeholdersData(); // 重新获取数据
};

// 跳转到指定页码
const goToSpecificPage = () => {
  const page = parseInt(gotoPage.value);

  if (!isNaN(page) && page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    getStakeholdersData(); // 重新获取数据
  } else {
    ElMessage.warning(`请输入1-${totalPages.value}之间的页码`);
    // 恢复输入框中的页码为当前页
    gotoPage.value = currentPage.value.toString();
  }
};



// 重置搜索
const resetStakeholderSearch = () => {
  stakeholderTypeFilter.value = '';
  searchKeyword.value = '';
  currentPage.value = 1; // 重置到第一页
  gotoPage.value = '1'; // 同步更新输入框中的页码
  getStakeholdersData(); // 重新获取数据
};

// 重置表单
const resetExternalForm = () => {
  externalForm.value = {
    name: '',
    department: '',
    role: '',
    contact: ''
  };
};

// 添加外部人员 - 只有项目经理才能执行
const addExternalStakeholder = () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限添加外部人员');
    return;
  }

  // 打开弹窗
  externalDialogVisible.value = true;
  resetExternalForm();
};

// 确认添加外部人员 - 只有项目经理才能执行
const confirmAddExternalStakeholder = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限添加外部人员');
    return;
  }

  // 表单验证
  if (!externalForm.value.name) {
    ElMessage.warning('请输入姓名');
    return;
  }
  
  if (!externalForm.value.department) {
    ElMessage.warning('请输入单位/部门');
    return;
  }
  
  if (!externalForm.value.role) {
    ElMessage.warning('请输入项目角色');
    return;
  }
  
  if (!externalForm.value.contact) {
    ElMessage.warning('请输入联系方式');
    return;
  }
  
  // 优先使用props中的contractNo，如果没有则从sessionStorage获取
  const contractNo = props.contractNo || sessionStorage.getItem('currentContractNo');

  if (!contractNo) {
    ElMessage.error('合同号不能为空');
    return;
  }

  try {
    // 构建API请求数据
    const requestData = {
      stakeholderUserName: externalForm.value.name,
      department: externalForm.value.department,
      projectRole: externalForm.value.role,
      mobile: externalForm.value.contact,
      source: '1', // 外部人员
      contractNo: contractNo
    };

    // 调用API
    const response = await addExternalStakeholderAPI(requestData);

    if (response && (response.code === 0 || response.code === 200)) {
      ElMessage.success('添加外部人员成功');
      
      // 关闭弹窗
      externalDialogVisible.value = false;
      
      // 重新获取干系人数据
      await getStakeholdersData();
      
      // 重置表单
      resetExternalForm();
    } else {
      throw new Error(response?.msg || response?.message || '添加失败');
    }
  } catch (error) {
    console.error('添加外部人员失败:', error);
    ElMessage.error('添加外部人员失败：' + (error.message || '网络错误'));
  }
};

// 重置内部人员表单
const resetInternalForm = () => {
  internalForm.value = {
    name: '',
    role: ''
  };
};

// 添加内部人员 - 只有项目经理才能执行
const addInternalStakeholder = () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限添加内部人员');
    return;
  }

  internalDialogVisible.value = true;
  resetInternalForm();
};

// 确认添加内部人员 - 只有项目经理才能执行
const confirmAddInternalStakeholder = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限添加内部人员');
    return;
  }

  if (!internalForm.value.name) {
    ElMessage.warning('请选择内部成员');
    return;
  }
  if (!internalForm.value.role) {
    ElMessage.warning('请输入项目角色');
    return;
  }
  // 优先使用props中的contractNo，如果没有则从sessionStorage获取
  const contractNo = props.contractNo || sessionStorage.getItem('currentContractNo');

  if (!contractNo) {
    ElMessage.error('合同号不能为空');
    return;
  }

  // 验证选择的用户是否有效
  const selectedUser = internalUserOptions.value.find(user => user.value === internalForm.value.name);
  if (!selectedUser) {
    ElMessage.error('选择的用户无效，请重新选择');
    return;
  }

  try {
    // 构建API请求数据
    const requestData = {
      stakeholderUserId: selectedUser.originalData.username || selectedUser.originalData.userName, // 传递userName而不是ID
      projectRole: internalForm.value.role,
      source: '2', // 内部人员
      contractNo: contractNo
    };

    // 调用API
    const response = await addInternalStakeholderAPI(requestData);

    if (response && (response.code === 0 || response.code === 200)) {
      ElMessage.success('添加内部人员成功');
      
      // 关闭弹窗
      internalDialogVisible.value = false;
      
      // 重新获取干系人数据
      await getStakeholdersData();
      
      // 重置表单
      resetInternalForm();
    } else {
      throw new Error(response?.msg || response?.message || '添加失败');
    }
  } catch (error) {
    console.error('添加内部人员失败:', error);
    ElMessage.error('添加内部人员失败：' + (error.message || '网络错误'));
  }
};

// 移除选中的干系人 - 只有项目经理才能执行
const removeSelectedStakeholders = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限移除干系人');
    return;
  }

  if (selectedStakeholders.value.length === 0) {
    ElMessage.warning('请先选择要移除的干系人');
    return;
  }
  
  try {
    const ids = selectedStakeholders.value.map(item => item.id);
    
    // 逐个删除选中的干系人
    const deletePromises = ids.map(id => deleteStakeholderAPI(id));
    await Promise.all(deletePromises);
    
    ElMessage.success('批量删除成功');
    selectedStakeholders.value = [];
    
    // 重新获取干系人数据
    await getStakeholdersData();
  } catch (error) {
    console.error('批量删除干系人失败:', error);
    ElMessage.error('批量删除干系人失败：' + (error.message || '网络错误'));
  }
};

// 编辑干系人 - 只有项目经理才能执行
const editStakeholder = (row) => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限编辑干系人');
    return;
  }

  editForm.value = { ...row }; // 复制当前行数据到编辑表单
  editDialogVisible.value = true;
};

// 确认编辑干系人 - 只有项目经理才能执行
const confirmEditStakeholder = async () => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限编辑干系人');
    return;
  }

  if (!editForm.value.name) {
    ElMessage.warning('请输入姓名');
    return;
  }
  if (!editForm.value.role) {
    ElMessage.warning('请输入项目角色');
    return;
  }

  try {
    let response;
    
    if (editForm.value.type === '外部人员') {
      // 编辑外部人员
      const requestData = {
        id: editForm.value.id,
        stakeholderUserName: editForm.value.name,
        department: editForm.value.department,
        projectRole: editForm.value.role,
        mobile: editForm.value.contact
      };
      
      response = await editExternalStakeholderAPI(requestData);
    } else {
      // 编辑内部人员
      // 对于内部人员，stakeholderUserId应该传递userName而不是ID
      const requestData = {
        id: editForm.value.id,
        stakeholderUserId: editForm.value.originalData?.stakeholderUserId || editForm.value.originalData?.userName || editForm.value.name,
        projectRole: editForm.value.role
      };

      response = await editInternalStakeholderAPI(requestData);
    }

    if (response && (response.code === 0 || response.code === 200)) {
      ElMessage.success('编辑成功');
      editDialogVisible.value = false;
      
      // 重新获取干系人数据
      await getStakeholdersData();
    } else {
      throw new Error(response?.msg || response?.message || '编辑失败');
    }
  } catch (error) {
    console.error('编辑干系人失败:', error);
    ElMessage.error('编辑干系人失败：' + (error.message || '网络错误'));
  }
};

// 删除单个干系人 - 只有项目经理才能执行
const deleteStakeholder = async (row) => {
  if (!isProjectManager.value) {
    ElMessage.error('您没有权限删除干系人');
    return;
  }

  try {
    // 调用删除API
    const response = await deleteStakeholderAPI(row.id);
    
    if (response && (response.code === 0 || response.code === 200)) {
      ElMessage.success('删除成功');
      
      // 重新获取干系人数据
      await getStakeholdersData();
      
      // 清空选中状态
      selectedStakeholders.value = [];
    } else {
      throw new Error(response?.msg || response?.message || '删除失败');
    }
  } catch (error) {
    console.error('删除干系人失败:', error);
    ElMessage.error('删除干系人失败：' + (error.message || '网络错误'));
  }
};

// 序号方法
const indexMethod = (index) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
</script>

<style scoped>
/* tab-content 样式 */
.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 干系人样式 */
.stakeholders {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  overflow: hidden;
}

/* 顶部区域 */
.stakeholders-header {
  padding: 12px 16px;
  border-bottom: none;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  margin-left: 5px;
  margin-right: 5px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

/* 水平布局容器 */
.header-flex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
}

.stakeholders-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
  white-space: nowrap;
  min-width: 100px;
}

.stakeholders-header h3 .el-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #409EFF;
}

/* 按钮区域 */
.stakeholders-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
  margin-right: 16px;
}

/* 搜索区域 */
.search-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.search-area .label {
  font-size: 14px;
  color: #606266;
}

/* 表格容器样式 - 修正分页器位置 */
.table-container {
  flex: none; /* 改为none，让容器根据内容自适应高度 */
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding: 0;
  height: auto;
  min-height: auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin: 0 5px 5px 5px;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

/* 表格样式统一 - 参考全部项目页面 */
:deep(.el-table) {
  border-radius: 8px 8px 0 0; /* 只保留顶部圆角 */
  overflow: hidden;
  border-bottom: none; /* 移除表格底部边框 */
  margin: 0; /* 确保表格没有外边距 */
  width: 100% !important; /* 确保表格占满容器宽度 */
  table-layout: fixed;
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f8faff;
}

/* 精确控制表格行高度，消除空隙 - 统一使用26px */
:deep(.el-table .el-table__row) {
  height: 26px !important;
}

:deep(.el-table .el-table__row td) {
  height: 26px !important;
  line-height: 26px !important;
  padding: 0 8px !important;
  box-sizing: border-box;
}

:deep(.el-table .el-table__header-wrapper .el-table__header tr) {
  height: 26px !important;
}

:deep(.el-table .el-table__header-wrapper .el-table__header th) {
  height: 26px !important;
  line-height: 26px !important;
  padding: 0 8px !important;
  box-sizing: border-box;
}

/* 表格外层容器样式 */
:deep(.el-table__inner-wrapper) {
  height: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
}

/* 移除表格最后一行的底部边框 */
:deep(.el-table__body tr:last-child td) {
  border-bottom: none !important;
}

/* 确保表格与分页器无缝连接 */
:deep(.el-table--border) {
  border-bottom: none;
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border::after) {
  display: none;
}

/* 表格外边框优化 */
:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell:last-child) {
  border-right: none;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 13px;
  color: #333;
}

/* 表头样式 - 与上面的26px设置保持一致 */
:deep(.el-table__header-wrapper th) {
  background-color: #f2f6fc;
  color: #333;
  font-weight: 500;
  /* 高度由上面的26px设置控制，这里不重复设置 */
  border-bottom: 1px solid #e0e3e9;
}

:deep(.el-table__header-wrapper th.el-table__cell) {
  background-color: #f2f6fc;
}

:deep(.el-table__header-wrapper th.el-table__cell .cell) {
  font-weight: 500;
  color: #333;
  padding: 0 4px;
}

/* 移除重复的行高设置，使用上面更具体的26px设置 */

/* 序号列样式调整 */
.serial-column {
  background-color: #f5f7fa !important;
}

/* 操作列样式优化 */
.operation-column {
  display: flex;
  justify-content: center;
  gap: 4px;
}

/* 操作按钮特定样式 */
.operation-column .op-btn {
  padding: 0 4px !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;
}

/* 确保图标和文本之间的间距合适 */
.operation-column .op-btn .el-icon {
  margin-right: 2px !important;
  font-size: 14px !important;
}

/* 确保按钮之间没有额外的间距 */
.operation-column .op-btn + .op-btn {
  margin-left: 0 !important;
}

/* 勾选框样式 */
:deep(.el-checkbox__inner) {
  width: 16px;
  height: 16px;
}

/* 按钮样式已在全局统一，移除本地自定义样式 */

:deep(.el-input__wrapper) {
  padding: 0 8px;
}

/* 分页器容器优化 - 参考全部项目页面 */
.pagination-container {
  margin-top: 0;
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #f8faff 100%);
  border-top: 1px solid #f0f2f5;
  border-radius: 0 0 8px 8px;
  min-height: 32px;
  box-sizing: border-box;
}

.pagination-info {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

/* 分页器本身紧凑展示 - 参考全部项目页面 */
:deep(.el-pagination) {
  padding: 0;
  height: 24px;
  display: flex;
  align-items: center;
}

:deep(.el-pagination button) {
  min-width: 24px;
  height: 24px;
  border-radius: 3px;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  font-size: 11px;
}

:deep(.el-pagination button:hover:not(:disabled)) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
}

:deep(.el-pagination button:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 3px;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  margin: 0 1px;
}

:deep(.el-pagination .el-pager li:hover) {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-color: #5B7BFA;
  color: #5B7BFA;
  transform: translateY(-1px);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
  transform: translateY(-1px);
}

/* 弹窗样式 - 修改为匹配截图 */
:deep(.el-dialog) {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 0;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: normal;
  color: #303133;
  line-height: 24px;
}

:deep(.el-dialog__headerbtn) {
  top: 15px;
  right: 20px;
}

:deep(.el-dialog__body) {
  padding: 30px 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

.dialog-content {
  margin: 0 auto;
}

/* 表单样式调整 */
.dialog-content :deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding-right: 12px;
  text-align: right;
}

.dialog-content :deep(.el-input__wrapper) {
  border-radius: 4px;
  width: 100%;
}

.dialog-content :deep(.el-select) {
  width: 100%;
}

.dialog-content :deep(.el-select .el-input) {
  width: 100%;
}

.dialog-content :deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 禁用输入框样式 */
.dialog-content :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
  box-shadow: none;
}

.dialog-content :deep(.el-input.is-disabled .el-input__inner) {
  color: #606266;
  -webkit-text-fill-color: #606266;
  background-color: #f5f7fa;
}

/* 底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 对话框按钮样式已在全局统一，移除本地自定义样式 */
</style> 