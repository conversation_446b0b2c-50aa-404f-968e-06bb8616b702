import request from '@/utils/request'
import { createApiFunction } from '@/config/api'

// 获取附件类型枚举
export function getAttachmentTypes() {
  return request({
    url: '/enums/attachment-types',
    method: 'get'
  })
}

// 获取表类型枚举
export function getTableTypes() {
  return request({
    url: '/enums/table-types',
    method: 'get'
  })
}

// 获取工作项变更类型枚举
export function getWorkItemChangeTypes() {
  return request({
    url: '/enums/work-item-change-types',
    method: 'get'
  })
}

// 获取工作项状态枚举
export function getWorkItemStatus() {
  return request({
    url: '/enums/work-item-status',
    method: 'get'
  })
}

// 获取工作项动态类型枚举
export function getWorkItemDynamicTypes() {
  return request({
    url: '/enums/work-item-dynamic-types',
    method: 'get'
  })
}

// 获取审核状态枚举
export function getReviewStatus() {
  return request({
    url: '/enums/review-status',
    method: 'get'
  })
}

// 获取计划状态枚举
export function getPlanStatus() {
  return request({
    url: '/enums/plan-status',
    method: 'get'
  })
}

// 获取计划动态类型枚举
export function getPlanDynamicTypes() {
  return request({
    url: '/enums/plan-dynamic-types',
    method: 'get'
  })
}

// 获取计划操作子类型枚举
export function getPlanActionSubTypes() {
  return request({
    url: '/enums/plan-action-sub-types',
    method: 'get'
  })
}

// 获取AI生成状态枚举
export function getAiGenerationStatus() {
  return request({
    url: '/enums/ai-generation-status',
    method: 'get'
  })
}

// 获取版本状态枚举
export function getVersionStatus() {
  return request({
    url: '/enums/version-status',
    method: 'get'
  })
}

// Mock数据 - 审核状态枚举
export function getReviewStatusMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '获取成功',
        data: {
          '2': '通过',
          '3': '驳回',
          '0': '待审核',
          '1': '审核中'
        }
      });
    }, 300);
  });
}

// Mock数据 - 计划状态枚举（与真实API保持一致）
export function getPlanStatusMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '获取成功',
        data: {
          0: '未开始',
          1: '进行中',
          2: '已完成',
          3: '逾期',
          4: '逾期完成'
        }
      });
    }, 300);
  });
}

// Mock数据 - 计划动态类型枚举
export function getPlanDynamicTypesMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          "1": "操作",
          "2": "日报"
        },
        success: true
      });
    }, 200);
  });
}

// Mock数据 - 计划操作子类型枚举
export function getPlanActionSubTypesMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          "1": "创建计划",
          "2": "修改计划",
          "3": "删除计划",
          "4": "变更计划",
          "6": "提交日报",
          "7": "修改日报"
        },
        success: true
      });
    }, 200);
  });
}

// Mock数据 - AI生成状态枚举
export function getAiGenerationStatusMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          "0": "未开始",
          "1": "生成中",
          "2": "已完成",
          "3": "失败"
        },
        success: true
      });
    }, 300);
  });
}

// Mock数据 - 版本状态枚举
export function getVersionStatusMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          "0": "预审核阶段",
          "1": "正式审核阶段"
        },
        success: true
      });
    }, 200);
  });
}

// 使用配置自动选择真实API或Mock API的导出
export const getPlanDynamicTypesApi = createApiFunction(getPlanDynamicTypes, getPlanDynamicTypesMock);
export const getPlanActionSubTypesApi = createApiFunction(getPlanActionSubTypes, getPlanActionSubTypesMock);
export const getPlanStatusApi = createApiFunction(getPlanStatus, getPlanStatusMock);
export const getReviewStatusApi = createApiFunction(getReviewStatus, getReviewStatusMock);
export const getAiGenerationStatusApi = createApiFunction(getAiGenerationStatus, getAiGenerationStatusMock);
export const getVersionStatusApi = createApiFunction(getVersionStatus, getVersionStatusMock);