# 交付部项目管理系统 - 组件设计文档

## 1. 布局组件

### 1.1 AppLayout

顶层布局组件，包含整个应用的基本骨架。

**属性:**
- `showTabs`: Boolean - 是否显示顶部标签导航

**插槽:**
- `default`: 主内容区域

**示例:**
```vue
<AppLayout>
  <RouterView />
</AppLayout>
```

### 1.2 HeaderComponent

应用顶部全局导航栏。

**属性:**
- `username`: String - 当前登录用户名
- `avatar`: String - 用户头像URL

**事件:**
- `@logout`: 用户点击退出按钮
- `@goToSettings`: 用户点击设置按钮
- `@goToMessages`: 用户点击消息按钮

**示例:**
```vue
<HeaderComponent 
  username="张三"
  avatar="/path/to/avatar.png"
  @logout="handleLogout"
  @goToSettings="goToSettings"
  @goToMessages="goToMessages"
/>
```

### 1.3 SidebarMenu

左侧导航菜单组件。

**属性:**
- `menuItems`: Array - 菜单项配置
- `collapsed`: Boolean - 是否折叠
- `activeIndex`: String - 当前选中的菜单项index

**事件:**
- `@select`: 菜单项被选中
- `@collapse-change`: 折叠状态变化

**示例:**
```vue
<SidebarMenu 
  :menuItems="menuConfig"
  :collapsed="isCollapsed"
  :activeIndex="currentMenuIndex"
  @select="handleMenuSelect"
  @collapse-change="handleCollapseChange"
/>
```

### 1.4 TabsNav

多标签导航组件，用于顶部标签式导航。

**属性:**
- `tabs`: Array - 标签页配置
- `activeTab`: String - 当前激活的标签ID

**事件:**
- `@tab-click`: 标签被点击
- `@tab-remove`: 标签被关闭
- `@tab-add`: 添加新标签

**示例:**
```vue
<TabsNav 
  :tabs="openedTabs"
  :activeTab="currentTabId"
  @tab-click="switchTab"
  @tab-remove="closeTab"
/>
```

## 2. 表单组件

### 2.1 SearchForm

通用搜索筛选表单组件。

**属性:**
- `fields`: Array - 表单字段配置
- `initialValues`: Object - 初始值
- `loading`: Boolean - 加载状态

**事件:**
- `@search`: 点击查询按钮
- `@reset`: 点击重置按钮
- `@change`: 表单值变更

**示例:**
```vue
<SearchForm 
  :fields="searchFields"
  :initialValues="defaultValues"
  :loading="isSearching"
  @search="handleSearch"
  @reset="handleReset"
/>
```

### 2.2 ProjectForm

项目信息表单组件。

**属性:**
- `project`: Object - 项目对象数据
- `mode`: String - 表单模式（'create'|'edit'|'readonly'）
- `loading`: Boolean - 提交状态

**事件:**
- `@submit`: 表单提交
- `@cancel`: 取消编辑

**示例:**
```vue
<ProjectForm 
  :project="currentProject"
  mode="edit"
  :loading="isSaving"
  @submit="saveProject"
  @cancel="cancelEdit"
/>
```

### 2.3 UserForm

用户信息表单组件。

**属性:**
- `user`: Object - 用户对象数据
- `roles`: Array - 可选角色列表
- `mode`: String - 表单模式（'create'|'edit'|'readonly'）

**事件:**
- `@submit`: 表单提交
- `@cancel`: 取消编辑

**示例:**
```vue
<UserForm 
  :user="selectedUser"
  :roles="availableRoles"
  mode="create"
  @submit="createUser"
  @cancel="cancelUserCreate"
/>
```

## 3. 数据展示组件

### 3.1 DataTable

通用数据表格组件。

**属性:**
- `columns`: Array - 表格列配置
- `data`: Array - 表格数据
- `loading`: Boolean - 加载状态
- `pagination`: Object - 分页配置
- `selectable`: Boolean - 是否可选择行

**事件:**
- `@row-click`: 行点击
- `@selection-change`: 选择变化
- `@sort-change`: 排序变化
- `@page-change`: 页码变化

**插槽:**
- `header`: 表格顶部工具栏
- `column-[name]`: 自定义列内容
- `empty`: 空数据状态

**示例:**
```vue
<DataTable 
  :columns="tableColumns"
  :data="projectsList"
  :loading="isLoading"
  :pagination="paginationConfig"
  selectable
  @row-click="handleRowClick"
  @selection-change="handleSelectionChange"
>
  <template #column-status="{ row }">
    <StatusTag :status="row.status" />
  </template>
</DataTable>
```

### 3.2 StatisticCard

数据统计卡片组件。

**属性:**
- `title`: String - 卡片标题
- `value`: [String, Number] - 统计值
- `unit`: String - 单位
- `trend`: String - 趋势（'up'|'down'|'flat'）
- `trendPercentage`: Number - 趋势百分比
- `icon`: String - 图标名称

**示例:**
```vue
<StatisticCard 
  title="在建项目总数"
  :value="20"
  unit="个"
  trend="up"
  :trendPercentage="5"
  icon="project"
/>
```

### 3.3 ProjectProgress

项目进度展示组件。

**属性:**
- `percentage`: Number - 进度百分比
- `status`: String - 状态
- `startDate`: String - 开始日期
- `endDate`: String - 结束日期
- `showDates`: Boolean - 是否显示日期

**示例:**
```vue
<ProjectProgress 
  :percentage="65"
  status="in-progress"
  startDate="2023-01-01"
  endDate="2023-12-31"
  showDates
/>
```

### 3.4 StatusTag

状态标签组件。

**属性:**
- `status`: String - 状态标识
- `type`: String - 标签类型（'default'|'success'|'warning'|'danger'|'info'）
- `size`: String - 标签大小

**示例:**
```vue
<StatusTag status="completed" type="success" size="medium" />
```

## 4. 业务组件

### 4.1 ProjectList

项目列表页面主组件。

**属性:**
- `filter`: Object - 过滤条件

**示例:**
```vue
<ProjectList :filter="currentFilter" />
```

### 4.2 ProjectDetail

项目详情视图组件。

**属性:**
- `projectId`: [String, Number] - 项目ID
- `activeTab`: String - 激活的二级标签（'basic'|'workitems'|'stakeholders'|'schedule'）

**示例:**
```vue
<ProjectDetail :projectId="123" activeTab="basic" />
```

### 4.3 ProjectBasicInfo

项目基本信息组件。

**属性:**
- `projectId`: [String, Number] - 项目ID
- `editable`: Boolean - 是否可编辑

**事件:**
- `@edit`: 点击编辑按钮

**示例:**
```vue
<ProjectBasicInfo 
  :projectId="123"
  :editable="hasEditPermission"
  @edit="startEdit"
/>
```

### 4.4 WorkItemsTable

项目工作项表格组件。

**属性:**
- `projectId`: [String, Number] - 项目ID
- `filter`: Object - 过滤条件
- `editable`: Boolean - 是否可编辑

**事件:**
- `@item-create`: 创建工作项
- `@item-update`: 更新工作项
- `@item-delete`: 删除工作项

**示例:**
```vue
<WorkItemsTable 
  :projectId="123"
  :filter="workItemsFilter"
  :editable="hasEditPermission"
  @item-create="createWorkItem"
  @item-update="updateWorkItem"
  @item-delete="deleteWorkItem"
/>
```

### 4.5 StakeholdersTable

项目干系人表格组件。

**属性:**
- `projectId`: [String, Number] - 项目ID
- `editable`: Boolean - 是否可编辑

**事件:**
- `@stakeholder-add`: 添加干系人
- `@stakeholder-remove`: 移除干系人
- `@stakeholder-update`: 更新干系人信息

**示例:**
```vue
<StakeholdersTable 
  :projectId="123"
  :editable="hasEditPermission"
  @stakeholder-add="addStakeholder"
  @stakeholder-remove="removeStakeholder"
  @stakeholder-update="updateStakeholder"
/>
```

### 4.6 Dashboard

工作台主组件。

**属性:**
- `userId`: [String, Number] - 用户ID

**示例:**
```vue
<Dashboard :userId="currentUser.id" />
```

## 5. 交互组件

### 5.1 ConfirmDialog

确认对话框组件。

**属性:**
- `visible`: Boolean - 是否可见
- `title`: String - 对话框标题
- `content`: String - 对话框内容
- `confirmButtonText`: String - 确认按钮文字
- `cancelButtonText`: String - 取消按钮文字
- `type`: String - 对话框类型（'info'|'warning'|'danger'|'success'）

**事件:**
- `@confirm`: 点击确认按钮
- `@cancel`: 点击取消按钮
- `@update:visible`: 可见性变化

**示例:**
```vue
<ConfirmDialog 
  :visible="showDeleteConfirm"
  title="确认删除"
  content="是否确认删除该项目？此操作不可撤销。"
  confirmButtonText="删除"
  cancelButtonText="取消"
  type="danger"
  @confirm="confirmDelete"
  @cancel="cancelDelete"
  @update:visible="(val) => showDeleteConfirm = val"
/>
```

### 5.2 Notification

通知提示组件。

**属性:**
- `title`: String - 通知标题
- `message`: String - 通知内容
- `type`: String - 通知类型（'info'|'warning'|'error'|'success'）
- `duration`: Number - 显示时长（毫秒）
- `position`: String - 显示位置

**示例:**
```vue
<Notification 
  title="操作成功"
  message="项目已成功创建"
  type="success"
  :duration="3000"
  position="top-right"
/>
```

### 5.3 LoadingOverlay

加载遮罩组件。

**属性:**
- `loading`: Boolean - 是否显示加载状态
- `text`: String - 加载文字提示

**示例:**
```vue
<LoadingOverlay :loading="isLoading" text="数据加载中..." />
```

## 6. 图表组件

### 6.1 ProjectStatusChart

项目状态分布图表。

**属性:**
- `data`: Array - 图表数据
- `height`: String - 图表高度
- `showLegend`: Boolean - 是否显示图例

**示例:**
```vue
<ProjectStatusChart 
  :data="statusDistribution"
  height="300px"
  showLegend
/>
```

### 6.2 ProgressChart

项目进度趋势图。

**属性:**
- `data`: Array - 进度数据
- `height`: String - 图表高度
- `yAxisName`: String - Y轴名称

**示例:**
```vue
<ProgressChart 
  :data="progressTrend"
  height="350px"
  yAxisName="完成率"
/>
```

## 7. 实用组件

### 7.1 FileUploader

文件上传组件。

**属性:**
- `accept`: String - 接受的文件类型
- `multiple`: Boolean - 是否支持多文件
- `limit`: Number - 文件数量限制
- `size`: Number - 文件大小限制(MB)
- `uploadUrl`: String - 上传接口地址

**事件:**
- `@upload-success`: 上传成功
- `@upload-error`: 上传失败
- `@file-exceed`: 文件数量超出限制

**示例:**
```vue
<FileUploader 
  accept=".jpg,.png,.pdf,.doc,.docx"
  :multiple="true"
  :limit="5"
  :size="10"
  uploadUrl="/api/upload"
  @upload-success="handleUploadSuccess"
  @upload-error="handleUploadError"
/>
```

### 7.2 DateRangePicker

日期范围选择器。

**属性:**
- `startDate`: String - 开始日期
- `endDate`: String - 结束日期
- `format`: String - 日期格式
- `placeholder`: Object - 占位文本
- `disabledDate`: Function - 禁用日期函数

**事件:**
- `@update:startDate`: 更新开始日期
- `@update:endDate`: 更新结束日期
- `@change`: 日期范围变化

**示例:**
```vue
<DateRangePicker 
  :startDate="form.startDate"
  :endDate="form.endDate"
  format="YYYY-MM-DD"
  :placeholder="{ start: '开始日期', end: '结束日期' }"
  :disabledDate="disableFutureDates"
  @update:startDate="updateStartDate"
  @update:endDate="updateEndDate"
  @change="handleDateRangeChange"
/>
```

### 7.3 UserSelector

用户选择器组件。

**属性:**
- `selected`: Array - 已选用户ID列表
- `multiple`: Boolean - 是否多选
- `disabled`: Boolean - 是否禁用

**事件:**
- `@update:selected`: 更新选中的用户
- `@change`: 选择变化

**示例:**
```vue
<UserSelector 
  :selected="selectedUserIds"
  :multiple="true"
  :disabled="!hasPermission"
  @update:selected="updateSelectedUsers"
  @change="handleUserSelectionChange"
/>
```

### 7.4 EmptyState

空状态提示组件。

**属性:**
- `image`: String - 图片URL
- `description`: String - 描述文本
- `showAction`: Boolean - 是否显示操作按钮
- `actionText`: String - 按钮文本

**事件:**
- `@action`: 点击操作按钮

**插槽:**
- `default`: 自定义内容
- `image`: 自定义图片
- `action`: 自定义操作区域

**示例:**
```vue
<EmptyState 
  image="/images/empty-projects.svg"
  description="暂无项目数据"
  showAction
  actionText="创建项目"
  @action="createNewProject"
/>
``` 