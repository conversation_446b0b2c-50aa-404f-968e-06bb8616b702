import { createRouter, createWebHashHistory } from 'vue-router';
import MainLayout from '../layout/MainLayout.vue'
import { useUserStore } from '../store/modules/user'
import { usePermissionStore } from '../store/modules/permission'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login/index.vue'),
    meta: { title: '登录', noAuth: true }
  },
  {
    path: '/token-login',
    name: 'TokenLogin',
    component: () => import('../views/Login/TokenLogin.vue'),
    meta: { title: '外部登录', noAuth: true }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/workbench',
    children: [
      {
        path: 'workbench',
        name: 'Workbench',
        component: () => import('../views/Workbench/index.vue'),
        meta: { title: '工作台' }
      },
      {
        path: 'projects',
        name: 'Projects',
        component: () => import('../views/Projects/index.vue'),
        meta: { title: '项目管理' }
      },
      {
        path: 'projects/all',
        name: 'AllProjects',
        component: () => import('../views/Projects/index.vue'),
        meta: { title: '全部项目' }
      },
      {
        path: 'projects/shenzhen',
        name: 'ShenzhenProjects',
        component: () => import('../views/ProjectDetail/index.vue'),
        meta: { title: '深圳项目', filter: 'shenzhen' }
      },
      {
        path: 'projects/wuhan',
        name: 'WuhanProjects',
        component: () => import('../views/ProjectDetail/index.vue'),
        meta: { title: '武汉项目', filter: 'wuhan' }
      },
      {
        path: 'project/:id',
        name: 'ProjectDetail',
        component: () => import('../views/ProjectDetail/index.vue'),
        meta: { title: '项目详情' },
        props: true
      },
      {
        path: 'project-detail/:id',
        name: 'UserProjectDetail',
        component: () => import('../views/ProjectDetail/index.vue'),
        meta: { title: '项目详情' },
        props: true
      },
      {
        // 项目计划详情作为独立路由
        path: 'project-plan/:projectId/:planId',
        name: 'ProjectPlanDetail',
        component: () => import('@/views/ProjectDetail/ProjectPlanDetail.vue'),
        meta: {
          title: '项目计划详情',
          requiresAuth: true
        },
        props: true
      },
      {
        // 项目计划变更路由
        path: 'project-plan-edit/:projectId',
        name: 'ProjectPlanEdit',
        component: () => import('@/views/ProjectDetail/ProjectPlanEdit.vue'),
        meta: {
          title: '项目计划变更',
          requiresAuth: true
        },
        props: true
      },
      {
        // 项目计划审核路由
        path: 'project-plan-review/:projectId',
        name: 'ProjectPlanReview',
        component: () => import('@/views/ProjectDetail/ProjectPlanReview.vue'),
        meta: {
          title: '项目计划审核',
          requiresAuth: true
        },
        props: true
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('../views/Users/<USER>'),
        meta: { title: '用户管理' }
      },
      {
        path: 'users/role',
        name: 'Roles',
        component: () => import('../views/Users/<USER>'),
        meta: { title: '角色管理' }
      },

      {
        path: 'plan-demo',
        name: 'PlanDemo',
        component: () => import('../views/ProjectDetail/ProjectPlanDemo.vue'),
        meta: { title: '计划详情演示', noAuth: true }
      },
      {
        path: 'test-permission',
        name: 'TestPermission',
        component: () => import('../views/TestPermission.vue'),
        meta: { title: '权限测试', noAuth: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫 - 验证用户是否已登录和权限
router.beforeEach(async (to, from, next) => {
  // 获取用户store和权限store
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 项目管理系统` : '项目管理系统'

  // 处理外部token登录跳转
  if (to.query.token && to.path !== '/token-login') {
    // 如果URL中有token参数且不是token-login页面，重定向到token-login页面
    next({
      path: '/token-login',
      query: {
        token: to.query.token,
        redirect: to.path !== '/' ? to.fullPath.split('?')[0] : '/workbench' // 移除token参数，保留目标路径
      }
    })
    return
  }

  // 如果页面不需要登录验证，直接放行
  if (to.meta.noAuth) {
    next()
    return
  }

  // 检查用户是否已登录
  if (!userStore.isLoggedIn()) {
    // 未登录，跳转到登录页
    // 如果当前访问的是根路径，不需要保存重定向信息
    if (to.path === '/') {
      next({ name: 'Login' })
    } else {
      next({ name: 'Login', query: { redirect: to.fullPath } })
    }
    return
  }

  // 已登录，检查权限
  try {
    // 如果角色信息还未加载，先加载角色信息
    if (userStore.allRoles.length === 0) {
      await userStore.fetchAllRoles()
    }

    // 如果权限数据还未加载，先加载权限数据
    if (userStore.userRole && permissionStore.userMenuIds.length === 0) {
      await permissionStore.initPermissions(userStore.userRole)
    }

    // 检查用户是否有访问该路径的权限
    // 特殊路径不需要权限检查
    const publicPaths = ['/', '/workbench', '/login']
    const isPublicPath = publicPaths.includes(to.path)

    // 动态路径模式匹配
    const isDynamicProjectPath = /^\/project-detail\//.test(to.path) || /^\/project\//.test(to.path) || /^\/project-plan/.test(to.path)

    if (!isPublicPath && !isDynamicProjectPath && !permissionStore.hasPathPermission(to.path)) {
      // 无权限访问，跳转到工作台或首页
      console.warn(`用户无权限访问路径: ${to.path}`)
      next({ path: '/workbench' })
      return
    }

    // 对于动态项目路径，检查用户是否有项目管理权限
    if (isDynamicProjectPath) {
      const hasProjectPermission = permissionStore.hasMenuPermission(2) // 项目管理菜单ID是2
      if (!hasProjectPermission) {
        console.warn(`用户无项目管理权限，无法访问: ${to.path}`)
        next({ path: '/workbench' })
        return
      }
    }

    // 有权限，放行
    next()
  } catch (error) {
    console.error('权限检查失败:', error)
    // 权限检查失败，仍然放行，避免阻塞用户操作
    next()
  }
})

export default router 