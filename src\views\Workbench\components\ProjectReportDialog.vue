<template>
  <!-- 日报填写抽屉 -->
  <el-drawer
    v-model="dialogVisible"
    size="60%"
    direction="rtl"
    destroy-on-close
    class="report-drawer"
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="closeDialog">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Document /></el-icon>
          填写日报 - {{ currentProject?.projectName || '深圳XXXXX项目' }}
        </div>
      </div>
    </template>
    
    <div class="report-tabs">
      <el-tabs v-model="activeReportTab" class="drawer-tabs">
        <!-- 今日开工信息标签页 -->
        <el-tab-pane label="今日开工信息" name="workInfo">
          <div class="tab-content work-info-content drawer-optimized">
            <!-- 开工说明区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Edit /></el-icon>
                  <span class="section-title">开工说明</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.workDescription"
                    type="textarea"
                    :rows="4"
                    placeholder="请详细描述今日开工情况、工作内容等..."
                    class="description-input"
                  />
                </div>
              </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Picture /></el-icon>
                  <span class="section-title">现场图片</span>
                  <span class="section-subtitle">最多上传3张照片</span>
                </div>
                <div class="section-content">
                  <div class="upload-area-drawer">
                <div class="upload-container-horizontal">
                  <el-upload
                    class="upload-box-drawer"
                    action="#"
                    :auto-upload="false"
                    :on-change="(file) => handleFileChange(file, 'work')"
                    :show-file-list="false"
                    :disabled="reportForm.workImages.length >= 3"
                  >
                    <div class="upload-trigger-drawer">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传图片</div>
                    </div>
                  </el-upload>

                  <div v-if="reportForm.workImages.length > 0" class="image-list-horizontal">
                    <div v-for="(img, index) in reportForm.workImages" :key="index" class="image-item-horizontal">
                      <div class="image-container">
                        <img :src="img.url || img.preview || ''" alt="图片预览" class="image-thumb-drawer" v-if="img.url || img.preview" @click="handlePreviewImage(img)" />
                        <div class="image-thumb-empty" v-else>无预览</div>

                        <!-- 状态标识 -->
                        <div v-if="img.uploadError" class="upload-status-mask error">
                          <el-icon><Close /></el-icon>
                          <div class="upload-text">上传失败</div>
                        </div>
                        <div v-else-if="img.uploadSuccess && !img.isExisting" class="upload-success-badge">
                          <el-icon><Check /></el-icon>
                        </div>
                        <div v-else-if="img.isExisting" class="existing-image-badge">
                          <el-icon><Document /></el-icon>
                        </div>

                        <!-- 删除按钮 -->
                        <el-button
                          class="delete-btn-drawer"
                          size="small"
                          type="danger"
                          :icon="Close"
                          circle
                          @click="removeImage('work', index)"
                        />
                      </div>

                    </div>
                  </div>
                </div>
              </div>

                  <div class="upload-tip">
                    <el-icon><InfoFilled /></el-icon>
                    支持文件格式: .jpg, .png，单个文件不能超过50MB
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 当日计划进展标签页 -->
        <el-tab-pane label="当日计划进展" name="planProgress">
          <div class="tab-content drawer-optimized">
            <div class="form-section">
              <el-table
              :data="reportForm.taskProgress" 
              border 
              style="width: 100%"
              size="small"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '8px 0' }"
            >
              <el-table-column type="index" label="序号" width="50" align="center" />
              <el-table-column prop="taskName" label="工作项名称" width="100" show-overflow-tooltip />
              <el-table-column prop="planTime" label="计划时间" width="160" show-overflow-tooltip />
              <el-table-column prop="responsible" label="责任人" width="70" align="center" />
              <el-table-column prop="executorName" label="执行人" width="70" align="center" />
              <el-table-column prop="progress" label="当日进展" min-width="120" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="progress-input">
                    <el-input v-model="row.progressDesc" placeholder="点击添加进展" size="small" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="任务状态" width="120" align="center">
                <template #default="{ row }">
                  <el-select v-model="row.status" placeholder="选择状态" size="small" style="width: 90px; margin: 0 auto; display: block;">
                    <el-option
                      v-for="(label, code) in planStatusEnum"
                      :key="code"
                      :label="label"
                      :value="label"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="images" label="图片" width="120" align="center">
                <template #default="{ row, $index }">
                  <div v-if="row.images && row.images.length">
                    <div class="task-image-container">
                      <el-button type="primary" link size="small" @click="handlePreviewImage(row.images[0])">
                        {{ row.images[0].name }}
                      </el-button>
                      <!-- 上传错误状态显示 -->
                      <div v-if="row.images[0].uploadError" class="upload-status-inline error">
                        <el-icon><Close /></el-icon>
                        <span class="upload-text-small">失败</span>
                      </div>
                      <div v-else-if="row.images[0].uploadSuccess" class="upload-status-inline success">
                        <el-icon><Check /></el-icon>
                        <span class="upload-text-small">成功</span>
                      </div>
                    </div>
                  </div>
                  <el-upload
                    v-else
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="(file) => handleTaskFileChange(file, $index)"
                  >
                    <el-button class="upload-image-btn" size="small">上传图片</el-button>
                  </el-upload>
                </template>
              </el-table-column>
            </el-table>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 其他工作说明标签页 -->
        <el-tab-pane label="其他工作说明" name="otherWorkInfo">
          <div class="tab-content other-work-content drawer-optimized">
            <!-- 其他工作内容 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Document /></el-icon>
                  <span class="section-title">其他工作内容</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.otherWorkContent"
                    type="textarea"
                    :rows="3"
                    placeholder="请详细描述其他工作内容..."
                    class="description-input"
                  />
                </div>
              </div>
            </div>

            <!-- 完成情况 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Check /></el-icon>
                  <span class="section-title">完成情况</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.completionStatus"
                    placeholder="请输入完成情况..."
                    class="description-input"
                  />
                </div>
              </div>
            </div>

            <!-- 遇到的问题 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><InfoFilled /></el-icon>
                  <span class="section-title">遇到的问题</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.problemsEncountered"
                    placeholder="请输入遇到的问题..."
                    class="description-input"
                  />
                </div>
              </div>
            </div>

            <!-- 解决方案 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Plus /></el-icon>
                  <span class="section-title">解决方案</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.solutions"
                    placeholder="请输入解决方案..."
                    class="description-input"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="drawer-footer">
        <el-button type="primary" @click="submitReport">提交</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 添加图片预览对话框 -->
  <el-dialog v-model="previewVisible" title="图片预览" width="800px" center>
    <img :src="previewUrl" alt="预览图片" style="width: 100%;">
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getDailyList, getDailyListMock, getDailyDetail, saveDaily, saveDailyMock } from '@/api/daily';
import { getPlanStatus, getPlanStatusMock } from '@/api/enums';
import { uploadFile } from '@/api/fileUpload';
import { Close, Check, Document, Plus, Edit, Picture, InfoFilled } from '@element-plus/icons-vue';
import eventBus, { EVENTS } from '@/utils/eventBus';

// 日期格式化函数
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  // 如果是完整的日期时间格式，只取日期部分
  if (dateTimeStr.includes(' ')) {
    return dateTimeStr.split(' ')[0];
  }

  // 如果是ISO格式，转换为日期
  if (dateTimeStr.includes('T')) {
    return dateTimeStr.split('T')[0];
  }

  return dateTimeStr;
};



// 格式化计划时间范围
const formatPlanTimeRange = (startTime, endTime) => {
  const formattedStart = formatDateTime(startTime);
  const formattedEnd = formatDateTime(endTime);

  if (!formattedStart && !formattedEnd) return '';
  if (!formattedEnd) return formattedStart;
  if (!formattedStart) return formattedEnd;

  // 如果开始和结束日期相同，只显示一个日期
  if (formattedStart === formattedEnd) {
    return formattedStart;
  }

  return `${formattedStart} - ${formattedEnd}`;
};

// Props 声明
const props = defineProps({
  visible: Boolean,
  projectData: Object
});

// Emits 声明
const emit = defineEmits(['update:visible', 'report-submitted']);

// 组件内部状态
const dialogVisible = ref(false);
const currentProject = ref(null);
const activeReportTab = ref('workInfo');
const planStatusEnum = ref({}); // 计划状态枚举

// 当前编辑的日报信息
const currentEditingReport = ref({
  id: null, // 日报ID，新增时为null，编辑时有值
  isEdit: false // 是否为编辑模式
});

// 日报表单数据
const reportForm = reactive({
  workDescription: '',
  workImages: [],
  taskProgress: [
    { taskName: '任务A', planTime: '2023-04-20 09:00', responsible: '张三', executorName: '赵六', progressDesc: '', status: '进行中', images: [] },
    { taskName: '任务B', planTime: '2023-04-20 10:00', responsible: '李四', executorName: '钱七', progressDesc: '', status: '延期', images: [] },
    { taskName: '任务C', planTime: '2023-04-20 11:00', responsible: '王五', executorName: '孙八', progressDesc: '', status: '进行中', images: [] },
  ],
  otherWorkContent: '',
  completionStatus: '',
  problemsEncountered: '',
  solutions: '',
});

// 图片预览相关状态
const previewVisible = ref(false);
const previewUrl = ref('');



// 加载计划状态枚举
const loadPlanStatusEnum = async () => {
  try {
    let response;
    try {
      response = await getPlanStatus();
    } catch (apiError) {
      response = await getPlanStatusMock();
    }

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      planStatusEnum.value = response.data;
    } else {
      // 使用默认映射（根据接口文档）
      planStatusEnum.value = {
        '0': '未开始',
        '1': '进行中',
        '2': '已完成',
        '3': '逾期',
        '4': '逾期完成'
      };
    }
  } catch (error) {
    // 使用默认映射（根据接口文档）
    planStatusEnum.value = {
      '0': '未开始',
      '1': '进行中',
      '2': '已完成',
      '3': '逾期',
      '4': '逾期完成'
    };
  }
};

// 获取计划状态码（用于后端接口）
const getPlanStatusCode = (status) => {
  // 如果已经是状态码，直接返回
  if (planStatusEnum.value[status]) {
    return status;
  }

  // 根据中文描述查找对应的状态码
  for (const [code, desc] of Object.entries(planStatusEnum.value)) {
    if (desc === status) {
      return code;
    }
  }

  // 兜底映射（根据接口文档）
  const statusMap = {
    '未开始': '0',
    '进行中': '1',
    '已完成': '2',
    '逾期': '3',
    '逾期完成': '4'
  };
  return statusMap[status] || '1'; // 默认返回"进行中"
};

// 获取计划状态描述（用于前端显示）
const getPlanStatusLabel = (statusCode) => {
  // 如果传入的是中文描述，直接返回
  if (typeof statusCode === 'string' && planStatusEnum.value) {
    for (const [code, desc] of Object.entries(planStatusEnum.value)) {
      if (desc === statusCode) {
        return statusCode;
      }
    }
  }

  // 根据状态码获取中文描述
  return planStatusEnum.value[statusCode] || planStatusEnum.value['1'] || '进行中';
};

// 监听父组件传递的可见性状态
watch(() => props.visible, async (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    currentProject.value = props.projectData;

    // 初始化日报数据
    await initializeReportData();
  }
});

// 监听内部可见性状态，同步到父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 检查当日是否已有日报
const checkTodayReport = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]; // 获取今日日期 YYYY-MM-DD
    const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

    if (!projectId) {
      return {
        hasReport: false,
        reportId: null,
        reportData: null
      };
    }

    // 查询当日日报
    const params = {
      projectId: projectId,
      reportDate: today, // 直接传入当日日期
      pageNum: 1,
      pageSize: 10
    };

    let response;
    try {
      response = await getDailyList(params);
    } catch (apiError) {
      response = await getDailyListMock(params);
    }

    if (response && (response.code === 200 || response.code === 0) && response.data && response.data.length > 0) {
      const todayReport = response.data[0]; // 直接取第一条，因为已经按reportDate过滤了

      // 找到当日日报，返回编辑模式信息
      return {
        hasReport: true,
        reportId: todayReport.id,
        reportData: todayReport
      };
    }

    // 没有找到当日日报
    return {
      hasReport: false,
      reportId: null,
      reportData: null
    };

  } catch (error) {
    console.error('检查当日日报失败:', error);
    // 出错时默认为新增模式
    return {
      hasReport: false,
      reportId: null,
      reportData: null
    };
  }
};

// 初始化日报数据
const initializeReportData = async () => {
  try {
    const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

    if (!projectId) {
      ElMessage.error('项目ID不能为空');
      return;
    }

    // 1. 检查当日是否已有日报
    const todayReportCheck = await checkTodayReport();

    // 设置当前编辑状态
    currentEditingReport.value = {
      id: todayReportCheck.reportId,
      isEdit: todayReportCheck.hasReport
    };

    // 2. 加载计划状态枚举
    await loadPlanStatusEnum();

    // 3. 加载当日计划进展数据
    let taskList = [];
    let detailResponse; // 将声明移到外面，确保后续可以访问
    try {

      if (currentEditingReport.value.isEdit && currentEditingReport.value.id) {
        // 编辑模式：传入日报ID获取完整日报信息
        try {
          detailResponse = await getDailyDetail(currentEditingReport.value.id, projectId);
        } catch (apiError) {
          console.warn('获取日报详情失败:', apiError);
        }
      } else {
        // 新增模式：不传ID，获取今日计划进展列表
        try {
          detailResponse = await getDailyDetail(null, projectId); // 不传ID，但传projectId
        } catch (apiError) {
          console.warn('获取今日计划进展失败:', apiError);
        }
      }

      if (detailResponse && (detailResponse.code === 200 || detailResponse.code === 0) && detailResponse.data) {
        const detail = detailResponse.data;

        // 处理任务进展数据
        if (detail.planProgressList && detail.planProgressList.length > 0) {
          taskList = detail.planProgressList.map(task => ({
            taskId: task.taskId,
            taskName: task.taskName || '任务',
            planTime: formatPlanTimeRange(task.planStartTime, task.planEndTime),
            responsible: task.ownerName || '',
            executorName: task.executorName || '',
            progressDesc: task.progressContent || '',
            status: getPlanStatusLabel(task.taskStatus),
            images: task.imageUrl ? [{
              name: '任务图片.jpg',
              url: task.imageUrl,
              uid: Date.now() + Math.random()
            }] : []
          }));
        }
      }
    } catch (error) {
      console.error('获取计划进展数据失败:', error);
    }

    // 如果没有加载到任务，taskList保持为空数组

    // 4. 初始化表单数据
    if (currentEditingReport.value.isEdit && detailResponse && detailResponse.data) {
      // 编辑模式：使用从详细接口获取的完整数据
      const detail = detailResponse.data;

      // 处理日报图片（如果有的话）
      const dailyImages = [];
      if (detail.dailyImages && Array.isArray(detail.dailyImages)) {
        detail.dailyImages.forEach((imageUrl, index) => {
          if (imageUrl) {
            dailyImages.push({
              url: imageUrl,
              uploadedUrl: imageUrl,
              uploadSuccess: true,
              name: `existing_image_${index + 1}.jpg`,
              isExisting: true // 标记为已存在的图片
            });
          }
        });
      }

      // 使用 Object.assign 来更新 reactive 对象
      Object.assign(reportForm, {
        workDescription: detail.startContent || '',
        workImages: dailyImages,
        taskProgress: taskList, // 使用已获取的任务进展数据
        otherWorkContent: detail.otherContent || '',
        completionStatus: detail.completionStatus || '',
        problemsEncountered: detail.problems || '',
        solutions: detail.solutions || ''
      });
    } else {
      // 新增模式：初始化空表单

      // 使用 Object.assign 来更新 reactive 对象
      Object.assign(reportForm, {
        workDescription: '',
        workImages: [],
        taskProgress: taskList, // 使用今日计划进展数据
        otherWorkContent: '',
        completionStatus: '',
        problemsEncountered: '',
        solutions: ''
      });
    }

  } catch (error) {
    console.error('初始化日报数据失败:', error);
    ElMessage.error('初始化日报数据失败');
  }
};

// 处理文件上传
const handleFileChange = async (file, type) => {
  if (type === 'work') {
    // 检查文件大小和类型
    const isJPG = file.raw.type === 'image/jpeg';
    const isPNG = file.raw.type === 'image/png';
    const isLt50M = file.raw.size / 1024 / 1024 < 50;

    if (!isJPG && !isPNG) {
      ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
      return;
    }
    if (!isLt50M) {
      ElMessage.error('上传图片大小不能超过 50MB!');
      return;
    }

    // 限制上传数量
    if (reportForm.workImages.length >= 3) {
      ElMessage.warning('最多只能上传3张图片');
      return;
    }

    // 创建预览URL
    if (file.raw) {
      file.preview = URL.createObjectURL(file.raw);
      file.url = file.preview;
      // 移除假的上传状态，直接设置为成功
      file.uploadSuccess = true;
    }

    // 先添加到列表中显示预览
    reportForm.workImages.push(file);

    try {
      // 调用文件上传接口
      const uploadResponse = await uploadFile(file.raw, 50023, {
        // 可以根据需要添加可选参数
        // mimeType: 'image/',
        // maxSize: 50 * 1024 * 1024 // 50MB
      });

      // 检查API响应是否成功
      if (!uploadResponse || (uploadResponse.code !== 0 && uploadResponse.code !== 200)) {
        throw new Error(`API调用失败: ${uploadResponse?.msg || '未知错误'}`);
      }

      // 检查文件上传是否成功
      if (!uploadResponse.data || !uploadResponse.data.success) {
        const errorMsg = uploadResponse.data?.errorMessage || '文件上传失败';
        throw new Error(errorMsg);
      }

      // 检查是否获得了文件URL
      if (!uploadResponse.data.fileUrl) {
        throw new Error('未获得文件URL');
      }

      // 上传成功，更新文件信息
      file.uploadedUrl = uploadResponse.data.fileUrl;
      file.uploadSuccess = true;
      ElMessage.success('图片上传成功');

    } catch (error) {
      console.error('图片上传失败:', error);
      // 上传失败时移除文件
      file.uploadError = true;
      ElMessage.error('图片上传失败：' + (error.message || '网络错误'));

      // 上传失败，从列表中移除
      const index = reportForm.workImages.findIndex(img => img.uid === file.uid);
      if (index > -1) {
        reportForm.workImages.splice(index, 1);
      }
    }
  }
};

// 处理任务图片上传
const handleTaskFileChange = async (file, index) => {
  // 检查文件类型和大小
  const isJPG = file.raw.type === 'image/jpeg';
  const isPNG = file.raw.type === 'image/png';
  const isLt50M = file.raw.size / 1024 / 1024 < 50;

  if (!isJPG && !isPNG) {
    ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
    return;
  }
  if (!isLt50M) {
    ElMessage.error('上传图片大小不能超过 50MB!');
    return;
  }

  // 创建预览URL
  if (file.raw) {
    file.preview = URL.createObjectURL(file.raw);
    file.url = file.preview;
    // 移除假的上传状态，直接设置为成功
    file.uploadSuccess = true;
  }

  // 先添加到列表中显示预览
  reportForm.taskProgress[index].images = [file];

  try {
    // 调用文件上传接口
    const uploadResponse = await uploadFile(file.raw, 50023, {
      // 可以根据需要添加可选参数
      // mimeType: 'image/',
      // maxSize: 50 * 1024 * 1024 // 50MB
    });

    // 检查API响应是否成功
    if (!uploadResponse || (uploadResponse.code !== 0 && uploadResponse.code !== 200)) {
      throw new Error(`API调用失败: ${uploadResponse?.msg || '未知错误'}`);
    }

    // 检查文件上传是否成功
    if (!uploadResponse.data || !uploadResponse.data.success) {
      const errorMsg = uploadResponse.data?.errorMessage || '文件上传失败';
      throw new Error(errorMsg);
    }

    // 检查是否获得了文件URL
    if (!uploadResponse.data.fileUrl) {
      throw new Error('未获得文件URL');
    }

    // 上传成功，更新文件信息
    file.uploadedUrl = uploadResponse.data.fileUrl;
    file.uploadSuccess = true;
    ElMessage.success('任务图片上传成功');

  } catch (error) {
    console.error('任务图片上传失败:', error);
    // 上传失败时移除文件
    file.uploadError = true;
    ElMessage.error('任务图片上传失败：' + (error.message || '网络错误'));

    // 上传失败，清空图片
    reportForm.taskProgress[index].images = [];
  }
};

// 移除图片
const removeImage = (type, index) => {
  if (type === 'work') {
    reportForm.workImages.splice(index, 1);
  } else {
    reportForm.taskProgress[index].images.splice(0, 1); // 移除第一个图片
  }
};

// 添加图片预览方法
const handlePreviewImage = (file) => {
  previewUrl.value = file.url || file.preview || URL.createObjectURL(file.raw);
  previewVisible.value = true;
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;

  // 重置表单状态
  currentEditingReport.value = {
    id: null,
    isEdit: false
  };

  // 重置活动标签页
  activeReportTab.value = 'workInfo';

  // 重置表单数据
  Object.assign(reportForm, {
    workDescription: '',
    workImages: [],
    taskProgress: [
      { taskName: '任务A', planTime: '2023-04-20 09:00', responsible: '张三', executorName: '赵六', progressDesc: '', status: '进行中', images: [] },
      { taskName: '任务B', planTime: '2023-04-20 10:00', responsible: '李四', executorName: '钱七', progressDesc: '', status: '延期', images: [] },
      { taskName: '任务C', planTime: '2023-04-20 11:00', responsible: '王五', executorName: '孙八', progressDesc: '', status: '进行中', images: [] },
    ],
    otherWorkContent: '',
    completionStatus: '',
    problemsEncountered: '',
    solutions: ''
  });
};

// 提交日报
const submitReport = async () => {
  try {
    // 获取项目ID
    const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

    if (!projectId) {
      ElMessage.error('项目ID不能为空');
      return;
    }

    // 构建提交数据，按照接口文档格式
    const submitData = {
      projectId: projectId,
      reportDate: new Date().toISOString().split('T')[0], // 当前日期
      reporterId: 1, // TODO: 从用户信息获取
      startDescription: reportForm.workDescription || '', // 开工说明（用户填写的内容）
      workContent: reportForm.workDescription || '', // 当日工作内容（与开工说明保持一致，确保列表显示）
      otherWorkContent: reportForm.otherWorkContent || '', // 其他工作内容
      completionStatus: reportForm.completionStatus || '', // 完成情况
      problemsEncountered: reportForm.problemsEncountered || '', // 遇到的问题
      solutions: reportForm.solutions || '', // 解决方案
      dailyImageUrls: reportForm.workImages?.map(img => {
        // 优先使用上传后的URL，否则使用原有URL
        const url = img.uploadedUrl || img.url || '';
        return url ? {
          url: url,
          btId: 50023, // 固定传50023
          objectName: img.name || 'daily_image.jpg' // 上传的文档的名字
        } : null;
      }).filter(item => item) || [], // 过滤空项
      planProgressList: reportForm.taskProgress?.map(task => ({
        taskId: task.taskId || 1,
        progressContent: task.progressDesc || '',
        imageUrl: task.images?.[0] ? (
          task.images[0].uploadedUrl ||
          task.images[0].url || ''
        ) : '',
        taskStatus: getPlanStatusCode(task.status), // 使用枚举状态码
        btId: '50023', // 固定传50023，注意这里是字符串类型
        objectName: task.images?.[0]?.name || 'task_image.jpg' // 上传的文档的名字
      })) || []
    };

    // 关键修改：根据编辑模式决定是否传递id
    if (currentEditingReport.value.isEdit && currentEditingReport.value.id) {
      // 编辑模式：传递日报ID
      submitData.id = currentEditingReport.value.id;
    }

    // 调用保存接口
    let response;
    try {
      response = await saveDaily(submitData);
    } catch (apiError) {
      response = await saveDailyMock(submitData);
    }

    if (response && (response.code === 200 || response.code === 0)) {
      // 提交数据给父组件
      emit('report-submitted', {
        ...reportForm,
        projectId: projectId
      });

      ElMessage.success('日报提交成功！');
      closeDialog();

      // 触发全局事件，通知其他页面更新日报状态
      const wasEdit = currentEditingReport.value.isEdit; // 保存编辑状态，因为下面会重置
      eventBus.emit(EVENTS.DAILY_REPORT_SUBMITTED, {
        projectId: projectId,
        reportDate: new Date().toISOString().split('T')[0],
        isEdit: wasEdit
      });

      // 重置表单状态
      currentEditingReport.value = {
        id: null,
        isEdit: false
      };
    } else {
      ElMessage.error(response?.msg || '日报提交失败');
    }
  } catch (error) {
    ElMessage.error('日报提交失败：' + (error.message || '系统错误'));
  }
};
</script>

<style scoped>
/* 覆盖 Element Plus 抽屉默认样式 */
:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 0 !important;
}

:deep(.el-drawer__footer) {
  padding: 0 !important;
  margin: 0 !important;
}

/* 自定义抽屉头部样式 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px 16px 8px !important;
  gap: 6px;

  .drawer-close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    color: #909399;
    transition: all 0.2s;
    flex-shrink: 0;

    &:hover {
      color: #606266;
      background-color: #f5f7fa;
    }

    .el-icon {
      font-size: 16px;
    }
  }

  .drawer-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    flex: 1;

    .drawer-icon {
      margin-right: 8px;
      color: #409eff;
    }
  }
}

/* 日报填写抽屉样式 */
.report-drawer {
  .drawer-footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
    display: flex;
    justify-content: flex-start;
    gap: 8px;
    margin: 0; /* 确保没有额外的外边距 */
  }

  /* 抽屉内容区域样式 */
  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

.report-header {
  padding: 0 0 12px 0;
}

.project-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.report-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 24px;

  .drawer-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-tabs__header) {
      margin: 0;
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;
    }

    :deep(.el-tabs__content) {
      flex: 1;
      overflow-y: auto;
    }
  }
}

.tab-content {
  padding: 12px 0;
}

.work-info-content {
  padding: 16px 8px;
}

/* 抽屉优化布局 */
.drawer-optimized {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px 0;
}

.form-section {
  margin-bottom: 20px;
}



/* 新的布局结构 */
.section-layout {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: start;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  grid-column: 1 / -1;
  margin-bottom: 12px;
}

.section-icon {
  color: #409EFF;
  font-size: 16px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1;
  display: flex;
  align-items: center;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
  line-height: 16px;
}

.section-content {
  grid-column: 1 / -1;
  margin-left: 0; /* 与左侧红线对齐，不需要缩进 */
}

/* 表单内容样式重置 */
.section-content .description-input,
.section-content .upload-area-drawer,
.section-content .upload-tip {
  margin-left: 0;
  padding-left: 0;
}



/* 抽屉样式的上传区域 */
.upload-area-drawer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-box-drawer {
  width: 120px;
  height: 120px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-box-drawer:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.upload-trigger-drawer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.upload-icon {
  font-size: 24px;
  color: #c0c4cc;
}

.upload-text {
  font-size: 12px;
  color: #909399;
}

/* 水平布局容器 */
.upload-container-horizontal {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
}

.image-list-horizontal {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.image-item-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.image-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.image-thumb-drawer {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.image-thumb-drawer:hover {
  transform: scale(1.05);
}

.delete-btn-drawer {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  min-height: 20px;
  padding: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
}



.upload-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.other-work-content {
  padding: 16px 8px;
}

/* 上传区域样式 */
.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.upload-box {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #f5f7fa;
  position: relative;
}

.upload-box:hover {
  border-color: #409EFF;
}

.upload-trigger {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-left: 0;
}

/* 图片缩略图样式优化 */
.image-thumb-box {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.image-thumb-box:hover {
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: all 0.3s;
}

.image-thumb:hover {
  transform: scale(1.05);
}

.image-thumb-empty {
  color: #bbb;
  font-size: 12px;
}

.thumb-delete {
  position: absolute;
  top: 2px;
  right: 2px;
  background: rgba(255,255,255,0.7);
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
  padding: 2px;
  transition: all 0.3s;
}

.thumb-delete:hover {
  background: rgba(255,255,255,0.9);
  color: #F56C6C;
  transform: scale(1.1);
}

.file-name-thumb {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0,0,0,0.4);
  color: #fff;
  font-size: 12px;
  text-align: center;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 0 0 4px 4px;
}

.delete-icon {
  cursor: pointer;
  margin-left: 8px;
  font-size: 16px;
  color: #f56c6c;
}

.delete-icon:hover {
  color: #ff4d4f;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 4px;

  .el-icon {
    font-size: 14px;
  }
}

.upload-notice {
  margin-top: 16px;
  padding: 10px 16px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.upload-list-title {
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
}

.numbering {
  font-weight: bold;
  color: #409EFF;
  margin-right: 4px;
}

/* 输入框样式 */
.description-input :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  min-height: 80px;
  resize: vertical;
}

.description-input :deep(.el-textarea__inner:focus) {
  border-color: #409EFF;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

/* 表格样式优化 */
.report-drawer :deep(.el-table) {
  margin-bottom: 12px;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-border-color: #dcdfe6;
}

.report-drawer :deep(.el-table__cell) {
  padding: 6px 0;
}

.report-drawer :deep(.el-table--small .el-table__cell) {
  padding: 4px 0;
}



.compact-form :deep(.el-form-item__content) {
  line-height: 32px;
  width: 100%;
}

/* 移除不再需要的水平布局相关样式 */
.other-work-content {
  padding: 16px 8px;
}

/* 输入框样式调整 */
.compact-form :deep(.el-input__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
  height: 36px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
}

.compact-form :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  background-color: #fff;
}

.compact-form :deep(.el-input__inner:focus),
.compact-form :deep(.el-textarea__inner:focus) {
  border-color: #409EFF;
}

/* 修复输入框嵌套问题 */
.compact-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  background-color: #fff;
  padding: 1px 11px;
  height: 36px;
  border-radius: 4px;
}



.other-work-content :deep(.el-input__inner) {
  border: none;
  height: 34px;
  line-height: 34px;
  padding: 0;
}

.other-work-content :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  min-height: 60px;
  line-height: 1.5;
}

.other-work-content :deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 统一表单项行高 */
.other-work-content :deep(.el-form-item__label) {
  height: 36px;
  line-height: 36px;
  padding-bottom: 0;
}

.other-work-content :deep(.el-form-item__content) {
  line-height: 36px;
}

/* 当hover和focus时样式保持一致 */
.other-work-content :deep(.el-input__wrapper:hover),
.other-work-content :deep(.el-input__wrapper.is-focus),
.other-work-content :deep(.el-textarea__inner:hover),
.other-work-content :deep(.el-textarea__inner:focus) {
  border-color: #409EFF !important;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
}

/* 上传按钮样式调整 */
.report-drawer :deep(.el-upload .el-button) {
  padding: 6px 12px;
  font-size: 12px;
}

/* 文件名样式 */
.file-name {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  display: inline-block;
  vertical-align: middle;
}

/* 选择框样式调整 */
.report-drawer :deep(.el-select) {
  width: 100%;
}

/* 标签页样式优化 */
.report-tabs :deep(.el-tabs__header) {
  margin-bottom: 8px;
}

.report-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #e4e7ed;
}

.report-tabs :deep(.el-tabs__item) {
  font-size: 14px;
  color: #606266;
  height: 36px;
  line-height: 36px;
}

.report-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 500;
}

.report-tabs :deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #409EFF;
}

/* 底部按钮样式 */
.drawer-footer .el-button {
  padding: 8px 18px;
  height: 32px;
  font-size: 13px;
}

.drawer-footer .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #FFFFFF;
}

/* 统一按钮圆角 */
.report-drawer :deep(.el-button) {
  border-radius: 4px;
}

/* 统一表格样式 */
.report-drawer :deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

/* 统一输入框、下拉框圆角 */
.report-drawer :deep(.el-input__wrapper),
.report-drawer :deep(.el-textarea__wrapper),
.report-drawer :deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
}

/* 统一标签页样式 */
.report-drawer :deep(.el-tabs__nav-wrap) {
  border-radius: 4px 4px 0 0;
}

/* 统一上传组件样式 */
.upload-box,
.report-drawer :deep(.el-upload-list__item) {
  border-radius: 4px;
}

/* 统一卡片和面板样式 */
.report-drawer :deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 上传状态样式 */
.upload-status-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 4px;
}

.upload-status-mask.error {
  background-color: rgba(245, 108, 108, 0.8);
}

.upload-status-mask .upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.upload-success-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #67c23a;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.existing-image-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #409EFF;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 任务图片上传状态 */
.task-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.upload-status-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.upload-status-inline.error {
  color: #f56c6c;
}

.upload-status-inline.success {
  color: #67c23a;
}

.upload-text-small {
  font-size: 11px;
}

/* 优化滚动条显示 */
.report-drawer :deep(.el-drawer__body) {
  overflow-y: auto;
}

/* 新增全局上传提示样式 */
.upload-tip-global {
  position: absolute;
  right: 32px;
  bottom: 24px;
  color: #909399;
  font-size: 13px;
  line-height: 1.5;
  z-index: 2;
  background: transparent;
  pointer-events: none;
}
</style> 