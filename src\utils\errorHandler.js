/**
 * 统一错误处理工具
 * 提供全局的错误处理方法
 */

import { ElMessage } from 'element-plus'
import { getErrorInfo } from './errorCodes'

/**
 * 处理API错误
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误信息
 * @param {string} context 上下文信息（如项目名称）
 */
export function handleApiError(error, defaultMessage = '操作失败', context = '') {
  console.error('API错误:', error)
  
  // 提取错误码和错误信息
  let errorCode = null
  let errorMessage = defaultMessage
  
  if (error.code) {
    errorCode = error.code
    errorMessage = error.message || defaultMessage
  } else if (error.response?.data?.code) {
    errorCode = error.response.data.code
    errorMessage = error.response.data.msg || error.response.data.message || defaultMessage
  } else if (error.message) {
    errorMessage = error.message
  }
  
  // 使用错误码映射获取友好的错误信息
  if (errorCode) {
    const errorInfo = getErrorInfo(errorCode, errorMessage, context)
    showMessage(errorInfo.message, errorInfo.type)
  } else {
    showMessage(errorMessage, 'error')
  }
}

/**
 * 处理网络错误
 * @param {Error} error 错误对象
 */
export function handleNetworkError(error) {
  console.error('网络错误:', error)
  
  let errorMessage = '网络请求失败'
  
  if (error.code === 'ECONNABORTED') {
    errorMessage = '请求超时，请检查网络连接或稍后重试'
  } else if (error.code === 'ERR_NETWORK') {
    errorMessage = '网络连接失败，请检查网络设置'
  } else if (error.response) {
    const status = error.response.status
    switch (status) {
      case 401:
        errorMessage = '认证失败，请重新登录'
        break
      case 403:
        errorMessage = '权限不足，无法访问'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      default:
        errorMessage = error.response.data?.message || error.response.data?.msg || `请求失败 (${status})`
    }
  }
  
  showMessage(errorMessage, 'error')
}

/**
 * 显示消息
 * @param {string} message 消息内容
 * @param {string} type 消息类型
 */
function showMessage(message, type = 'error') {
  ElMessage({
    message,
    type,
    duration: 5000,
    showClose: true
  })
}

/**
 * 处理业务逻辑错误
 * @param {number|string} errorCode 错误码
 * @param {string} errorMessage 错误信息
 * @param {string} context 上下文信息
 */
export function handleBusinessError(errorCode, errorMessage, context = '') {
  const errorInfo = getErrorInfo(errorCode, errorMessage, context)
  showMessage(errorInfo.message, errorInfo.type)
}

/**
 * 通用错误处理器
 * 自动判断错误类型并进行相应处理
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误信息
 * @param {string} context 上下文信息
 */
export function handleError(error, defaultMessage = '操作失败', context = '') {
  if (!error) {
    showMessage(defaultMessage, 'error')
    return
  }
  
  // 如果是取消操作，不显示错误
  if (error === 'cancel' || error.message === 'cancel') {
    return
  }
  
  // 判断错误类型
  if (error.code || error.response?.data?.code) {
    // API业务错误
    handleApiError(error, defaultMessage, context)
  } else if (error.code === 'ECONNABORTED' || error.code === 'ERR_NETWORK' || error.response) {
    // 网络错误
    handleNetworkError(error)
  } else {
    // 其他错误
    showMessage(error.message || defaultMessage, 'error')
  }
}

export default {
  handleApiError,
  handleNetworkError,
  handleBusinessError,
  handleError
}
