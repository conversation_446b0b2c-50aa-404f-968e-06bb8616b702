/**
 * 系统错误码映射表
 * 用于统一管理各种业务错误码的提示信息
 */

// AI生成相关错误码
export const AI_GENERATION_ERROR_CODES = {
  23001: {
    type: 'error',
    message: '项目不存在或已被删除，请刷新页面后重试。'
  },
  23002: {
    type: 'warning',
    message: '项目未处于预审状态，无法进行AI生成。请确保项目状态正确后重试。'
  },
  23003: {
    type: 'warning',
    message: '项目已存在AI生成的计划，如需重新生成请先清除现有计划。'
  },
  23004: {
    type: 'error',
    message: '上传的文件格式不支持，请上传.doc、.docx或.pdf格式的文件。'
  },
  23005: {
    type: 'error',
    message: '上传的文件过大，请确保文件大小不超过50MB。'
  },
  23006: {
    type: 'error',
    message: 'AI生成服务暂时不可用，请稍后重试或联系管理员。'
  },
  23007: {
    type: 'error',
    message: '文件内容无法解析，请检查文件是否损坏或格式是否正确。'
  },
  23008: {
    type: 'error',
    message: '您没有权限对该项目进行AI生成操作。'
  }
}

// 项目计划相关错误码
export const PROJECT_PLAN_ERROR_CODES = {
  20001: {
    type: 'error',
    message: '项目计划不存在。'
  },
  20002: {
    type: 'warning',
    message: '项目计划已被锁定，无法修改。'
  },
  20003: {
    type: 'error',
    message: '项目计划数据格式错误。'
  },
  20004: {
    type: 'warning',
    message: '项目计划存在循环依赖，请检查前置任务设置。'
  },
  20005: {
    type: 'error',
    message: '项目计划保存失败，请重试。'
  },
  20006: {
    type: 'warning',
    message: '项目计划审核中，无法修改。'
  },
  20007: {
    type: 'error',
    message: '项目计划版本冲突，请刷新后重试。'
  },
  20008: {
    type: 'warning',
    message: '项目计划已提交审核，无法重复提交。'
  },
  20009: {
    type: 'warning',
    message: '没有待提交的草稿，请先修改计划内容。'
  }
}

// 权限相关错误码
export const PERMISSION_ERROR_CODES = {
  40001: {
    type: 'error',
    message: '您没有访问权限。'
  },
  40002: {
    type: 'error',
    message: '您没有编辑权限。'
  },
  40003: {
    type: 'error',
    message: '您没有删除权限。'
  },
  40004: {
    type: 'error',
    message: '您没有审核权限。'
  }
}

// 通用错误码
export const COMMON_ERROR_CODES = {
  50001: {
    type: 'error',
    message: '服务器内部错误，请稍后重试。'
  },
  50002: {
    type: 'error',
    message: '数据库连接失败，请联系管理员。'
  },
  50003: {
    type: 'error',
    message: '参数验证失败，请检查输入数据。'
  },
  50004: {
    type: 'error',
    message: '操作超时，请重试。'
  }
}

// 合并所有错误码
export const ALL_ERROR_CODES = {
  ...AI_GENERATION_ERROR_CODES,
  ...PROJECT_PLAN_ERROR_CODES,
  ...PERMISSION_ERROR_CODES,
  ...COMMON_ERROR_CODES
}

/**
 * 获取错误信息
 * @param {number|string} errorCode 错误码
 * @param {string} defaultMessage 默认错误信息
 * @param {string} projectName 项目名称（可选）
 * @returns {object} 错误信息对象 {type, message}
 */
export function getErrorInfo(errorCode, defaultMessage = '操作失败', projectName = '') {
  const errorInfo = ALL_ERROR_CODES[errorCode]
  
  if (errorInfo) {
    let message = errorInfo.message
    
    // 如果提供了项目名称，在消息前添加项目名称
    if (projectName && (errorCode >= 23001 && errorCode <= 23999)) {
      message = `${projectName} ${message}`
    }
    
    return {
      type: errorInfo.type,
      message: message
    }
  }
  
  // 如果没有找到对应的错误码，返回默认信息
  return {
    type: 'error',
    message: defaultMessage
  }
}

/**
 * 显示错误消息
 * @param {number|string} errorCode 错误码
 * @param {string} defaultMessage 默认错误信息
 * @param {string} projectName 项目名称（可选）
 */
export function showErrorMessage(errorCode, defaultMessage = '操作失败', projectName = '') {
  const errorInfo = getErrorInfo(errorCode, defaultMessage, projectName)
  
  // 动态导入ElMessage以避免循环依赖
  import('element-plus').then(({ ElMessage }) => {
    ElMessage({
      message: errorInfo.message,
      type: errorInfo.type,
      duration: 5000,
      showClose: true
    })
  })
}
