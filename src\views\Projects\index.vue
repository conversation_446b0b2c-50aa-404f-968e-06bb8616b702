<template>
  <div class="projects">
    <div class="block-container">
      <div class="block-content">
        <!-- 标签页 -->
        <div class="tabs-container">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部项目" name="all"></el-tab-pane>
            <el-tab-pane v-if="isProjectLeader" label="关注项目" name="followed"></el-tab-pane>
          </el-tabs>
        </div>

        <!-- 搜索表单 -->
        <div class="search-container">
          <div class="search-form-container">
            <el-form :inline="true" :model="queryParams" size="default">
              <el-form-item label="项目名称">
                <el-input v-model="queryParams.projectName" placeholder="请输入" clearable style="width: 220px" @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="项目状态">
                <el-select
                  v-model="queryParams.projectState"
                  placeholder="请选择"
                  clearable
                  style="width: 220px"
                  :loading="!dictionaryLoaded"
                >
                  <el-option
                    v-for="(label, value) in projectStateDictionary"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery">
                  <el-icon><Search /></el-icon>查询
                </el-button>
                <el-button @click="resetQuery">
                  <el-icon><RefreshLeft /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
            <div class="sync-button">
              <el-button type="primary" @click="handleSync">
                <el-icon><Refresh /></el-icon>项目同步
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 项目列表表格 -->
        <div class="table-container">
            <div class="table-content">
              <ProjectTable
                ref="projectTableRef"
                v-if="dictionaryLoaded"
                :data="paginatedData"
                :loading="loading"
                :currentPage="currentPage"
                :pageSize="pageSize"

                :getProjectTypeDisplay="getProjectTypeDisplay"
                :getProjectStateDisplay="getProjectStateDisplay"
                @view="handleView"
                @edit="handleEdit"
                @generate-task="handleGenerateTask"
                @toggle-follow="handleToggleFollow"
              />
              <div v-else class="loading-container">
                <el-skeleton :rows="10" animated />
              </div>
            </div>
            <!-- 分页组件 -->
            <div class="pagination-container">
              <div class="pagination-info">
                共 {{ total }} 条
              </div>
              <el-pagination
                v-model:current-page="currentPage"
                :page-size="pageSize"
                :total="total"
                :pager-count="7"
                layout="prev, pager, next, jumper"
                @current-change="handlePageChange"
              />
            </div>
        </div>
      </div>
    </div>
    <ProjectViewDialog 
      v-model="viewDialogVisible" 
      :detail="viewDetail" 
      :getProjectTypeDisplay="getProjectTypeDisplay"
      :getProjectStateDisplay="getProjectStateDisplay"
    />
    <ProjectEditDialog 
      v-model="editDialogVisible" 
      :detail="editDetail" 
      :projectStateDictionary="projectStateDictionary"
      @save="handleEditSave" 
    />
    <TaskGeneratorDialog 
      v-model="taskDialogVisible" 
      :detail="taskDetail" 
      :taskStatus="getProjectTaskStatus(taskDetail.id)" 
      @generate="startGenerateTask" 
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshLeft, Refresh } from '@element-plus/icons-vue'

import { canFollowProject } from '@/utils/permissions'
import ProjectTable from './ProjectTable.vue'
import ProjectViewDialog from './ProjectViewDialog.vue'
import ProjectEditDialog from './ProjectEditDialog.vue'
import TaskGeneratorDialog from './TaskGeneratorDialog.vue'
import { getProjectList, editProject, generateProjectPlan, generateProjectPlanMock, followProject, unfollowProject } from '@/api/project'
import { getDictionaryTypes, getDictionaryMap } from '@/api/dictionary'
import { getSalesUsersByRole } from '@/api/user'
import { getAiGenerationStatus, getAiGenerationStatusMock } from '@/api/enums'
import { handleError } from '@/utils/errorHandler'

// 权限控制：使用全局权限系统
const isProjectLeader = computed(() => canFollowProject())

// 标签页状态
const activeTab = ref('all') // 'all' 表示全部项目，'followed' 表示关注项目

// 查询参数
const queryParams = reactive({
  projectName: '',
  projectState: '', // 改为 projectState 匹配API
  pageNum: 1,
  pageSize: 15
})

// 加载状态
const loading = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(15)  // 默认每页显示15条记录，会根据屏幕大小动态调整

// 项目列表数据
const projectList = ref([])
const total = ref(0)

// 字典数据
const dictionaryTypes = ref({})
const projectTypeDictionary = ref({})
const projectStateDictionary = ref({})
const dictionaryLoaded = ref(false)

// 销售用户（项目经理）列表
const salesUserList = ref([])

// 注入刷新侧边栏项目列表的方法
const refreshSidebarProjects = inject('refreshSidebarProjects', () => {})

// 表格组件引用
const projectTableRef = ref(null)




// 计算属性：分页后的数据（API已分页，直接返回）
const paginatedData = computed(() => {
  return projectList.value
})

// 移除表格高度计算，让表格自然显示所有数据

// 弹窗相关
const viewDialogVisible = ref(false)
const viewDetail = ref({})
const editDialogVisible = ref(false)
const editDetail = ref({})
const taskDialogVisible = ref(false)
const taskDetail = ref({})
const aiGenerationStatusEnum = ref({}) // AI生成状态枚举

// 获取字典类型
const getDictionaryTypesData = async () => {
  try {
    const response = await getDictionaryTypes()

    if (response && response.data) {
      dictionaryTypes.value = response.data

      // 根据字典类型获取具体的字典映射
      await loadDictionaryMappings()
    }
  } catch (error) {
    console.error('获取字典类型失败:', error)
  }
}

// 获取AI生成状态枚举
const getAiGenerationStatusData = async () => {
  try {
    let response
    try {
      response = await getAiGenerationStatus()
    } catch (apiError) {
      response = await getAiGenerationStatusMock()
    }

    if (response && (response.code === 200 || response.code === 0)) {
      aiGenerationStatusEnum.value = response.data || {}
    }
  } catch (error) {
    console.error('获取AI生成状态枚举失败:', error)
  }
}

// 加载字典映射
const loadDictionaryMappings = async () => {
  try {
    // 查找项目类型和项目状态对应的字典类型
    const typeEntries = Object.entries(dictionaryTypes.value)
    
    // 根据字典类型名称找到对应的type值
    let projectTypeKey = null
    let projectStateKey = null
    
    for (const [key, value] of typeEntries) {
      if (value.includes('项目类型') || value.includes('projectType')) {
        projectTypeKey = key
      }
      if (value.includes('项目状态') || value.includes('projectState')) {
        projectStateKey = key
      }
    }
    
    // 获取项目类型字典
    if (projectTypeKey) {
      const projectTypeResponse = await getDictionaryMap(projectTypeKey)
      if (projectTypeResponse && projectTypeResponse.data) {
        projectTypeDictionary.value = projectTypeResponse.data
      }
    }

    // 获取项目状态字典
    if (projectStateKey) {
      const projectStateResponse = await getDictionaryMap(projectStateKey)
      if (projectStateResponse && projectStateResponse.data) {
        projectStateDictionary.value = projectStateResponse.data
      }
    }
  } catch (error) {
    console.error('获取字典映射失败:', error)
  } finally {
    dictionaryLoaded.value = true
  }
}

// 转换项目类型码值为显示名称
const getProjectTypeDisplay = (code) => {
  if (!code) return '--';
  return projectTypeDictionary.value[code] || code
}

// 转换项目状态码值为显示名称
const getProjectStateDisplay = (code) => {
  if (!code) return '--';
  return projectStateDictionary.value[code] || code
}

// 获取项目列表数据
const getProjectListData = async (showMessage = false) => {
  loading.value = true

  try {
    // 构建查询参数，只传递有值的参数
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize
    }

    if (queryParams.projectName) {
      params.projectName = queryParams.projectName
    }

    if (queryParams.projectState) {
      params.projectState = queryParams.projectState
    }

    // 根据当前标签页决定是否传入 followedOnly 参数
    if (activeTab.value === 'followed') {
      params.followedOnly = true
    }

    const response = await getProjectList(params)

    if (response && response.data) {
      projectList.value = response.data
      total.value = response.total || 0

      // 只在主动查询时显示成功提示
      if (showMessage) {
        const tabText = activeTab.value === 'followed' ? '关注项目' : '全部项目'
        ElMessage.success(`查询成功，共找到 ${total.value} 个${tabText}`)
      }


    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败：' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 查询方法
const handleQuery = () => {
  queryParams.pageNum = 1
  currentPage.value = 1
  getProjectListData(true) // 主动查询时显示提示
}

// 重置方法
const resetQuery = () => {
  queryParams.projectName = ''
  queryParams.projectState = ''
  handleQuery()
}

// 标签页切换处理
const handleTabChange = (tabName) => {
  // 如果切换到关注项目标签页，但用户不是项目领导，则阻止切换
  if (tabName === 'followed' && !isProjectLeader.value) {
    ElMessage.warning('只有项目领导可以查看关注项目')
    return
  }

  activeTab.value = tabName
  // 重置分页到第一页
  queryParams.pageNum = 1
  currentPage.value = 1
  // 重新获取数据
  getProjectListData()
}

// 移除表格高度计算函数，让表格自然显示数据

// 项目同步方法
const handleSync = () => {
  ElMessage.success('项目同步已启动')
}

// 查看项目
const handleView = (row) => {
  viewDetail.value = row
  viewDialogVisible.value = true
}

// 编辑项目
const handleEdit = (row) => {
  // 设置编辑数据，确保字段映射正确
  editDetail.value = {
    ...row,
    projectManagerId: row.projectManagerId // 使用项目经理ID
  }
  editDialogVisible.value = true
}

// 获取销售用户列表
const getSalesUserList = async () => {
  try {
    const response = await getSalesUsersByRole()
    if (response && response.data) {
      salesUserList.value = response.data
    }
  } catch (error) {
    console.error('获取销售用户列表失败:', error)
  }
}

// 根据ID获取项目经理姓名
const getProjectManagerName = (managerId) => {
  if (!managerId) return ''
  // 先尝试用username匹配（因为API期望的是username）
  let manager = salesUserList.value.find(user => user.username === managerId)
  if (!manager) {
    // 如果没找到，再尝试用id匹配（兼容旧数据）
    manager = salesUserList.value.find(user => user.id.toString() === managerId.toString())
  }
  return manager ? manager.realName || manager.username : ''
}

async function handleEditSave(newData) {
  try {
    // 确保销售用户列表已加载
    if (salesUserList.value.length === 0) {
      await getSalesUserList()
    }
    
    // 根据新的API结构构建请求数据
    const editData = {
      id: newData.id.toString(), // API要求字符串类型
      projectState: newData.projectState || '',
      projectShortName: newData.projectShortName || newData.projectName || '', // 项目简称
      projectManagerId: newData.projectManagerId || '', // 项目经理ID
      projectManagerName: getProjectManagerName(newData.projectManagerId) // 项目经理姓名
    }
    

    
    // 验证必填字段
    if (!editData.projectManagerName && editData.projectManagerId) {

      ElMessage.warning('无法获取项目经理姓名，请稍后重试')
      return
    }
    
    // 调用编辑项目API
    const response = await editProject(editData)
    

    
    if (response && (response.code === 0 || response.code === 200)) {
      // API调用成功，更新本地数据
      const idx = projectList.value.findIndex(item => item.id === newData.id)
      if (idx > -1) {
        // 更新本地数据，确保字段映射正确
        projectList.value[idx] = { 
          ...projectList.value[idx], 
          ...newData,
          projectManagerId: newData.projectManagerId,
          projectManagerName: editData.projectManagerName
        }
      }
      
      ElMessage.success('项目编辑成功')
      
      // 重新获取项目列表以确保数据同步
      getProjectListData()
      
      // 刷新侧边栏项目列表
      refreshSidebarProjects()
    } else {
      // API返回了错误状态码
      console.error('项目编辑失败 - API返回错误:', response)
      throw new Error(response?.msg || response?.message || `编辑失败 (状态码: ${response?.code})`)
    }
  } catch (error) {
    console.error('编辑项目失败:', error)
    ElMessage.error('编辑项目失败：' + (error.message || '网络错误'))
  }
}

// 获取项目的任务生成状态
const getProjectTaskStatus = (projectId) => {
  // 从项目列表中获取真实的AI生成状态
  const project = projectList.value.find(p => p.id === projectId)
  if (!project || !project.aiGenerationStatus) {
    return 'initial'
  }

  // 根据aiGenerationStatus字段映射到对话框状态
  const statusMap = {
    '0': 'initial',    // 未开始
    '1': 'generating', // 生成中
    '2': 'completed',  // 已完成
    '3': 'failed'      // 失败
  }

  return statusMap[project.aiGenerationStatus] || 'initial'
}

// 任务生成
const handleGenerateTask = (row) => {
  taskDetail.value = row
  taskDialogVisible.value = true
}
// 开始生成任务
const startGenerateTask = async (generateData) => {
  const { projectId, file } = generateData

  try {
    // 调用AI生成接口，优先使用真实API，失败时使用Mock
    let response
    try {
      response = await generateProjectPlan(projectId, file)
    } catch (apiError) {
      // 检查是否是特定的业务错误
      if (apiError.code || apiError.response?.data?.code) {
        const errorCode = apiError.code || apiError.response?.data?.code
        const errorMsg = apiError.message || apiError.response?.data?.msg || apiError.response?.data?.message

        // 处理特定的业务错误码
        handleAiGenerationError(errorCode, errorMsg, projectId)
        return
      }

      // 如果不是业务错误，尝试使用Mock
      response = await generateProjectPlanMock(projectId, file)
    }

    if (response && (response.code === 0 || response.code === 200)) {
      // 生成请求提交成功
      const projectName = projectList.value.find(p => p.id === projectId)?.projectName
      ElMessage.success(`项目 ${projectName} 的任务生成请求已提交，请稍后查看状态`)

      // 刷新项目列表以获取最新的AI生成状态
      await getProjectListData()
    } else {
      // 生成失败
      const errorCode = response?.code
      const errorMsg = response?.msg || response?.message || 'AI生成失败，请重试'
      handleAiGenerationError(errorCode, errorMsg, projectId)
    }
  } catch (error) {
    // 请求失败
    console.error('AI生成请求失败:', error)

    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      ElMessage.error('AI生成超时，请稍后重试')
    } else {
      ElMessage.error('AI生成失败，请检查网络连接或联系管理员')
    }
  }
}

// 处理AI生成错误
const handleAiGenerationError = (errorCode, errorMsg, projectId) => {
  const projectName = projectList.value.find(p => p.id === projectId)?.projectName || ''

  // 创建错误对象
  const error = {
    code: errorCode,
    message: errorMsg
  }

  // 使用统一的错误处理工具
  handleError(error, 'AI生成失败，请重试', projectName)
}



// 处理关注/取消关注
const handleToggleFollow = async (row) => {
  try {
    let response
    const isCurrentlyFollowed = row.followed

    // 根据当前状态调用不同的API
    if (isCurrentlyFollowed) {
      // 当前已关注，执行取消关注操作
      response = await unfollowProject(row.id)
    } else {
      // 当前未关注，执行关注操作
      response = await followProject(row.id)
    }

    if (response && response.code === 200) {
      // 更新本地数据
      const project = projectList.value.find(p => p.id === row.id)
      if (project) {
        project.followed = !project.followed
        const action = project.followed ? '关注' : '取消关注'
        ElMessage.success(`${action}成功`)

        // 如果当前在关注项目标签页，且项目被取消关注，需要重新获取数据
        if (activeTab.value === 'followed' && !project.followed) {
          getProjectListData()
        }
      }
    } else {
      ElMessage.error(response?.message || '操作失败，请重试')
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    ElMessage.error('操作失败：' + (error.message || '网络错误'))
  }
}

// 页码变更事件
const handlePageChange = (page) => {
  currentPage.value = page
  queryParams.pageNum = page
  getProjectListData() // 分页时不显示提示
}

// 动态计算分页大小
const calculateOptimalPageSize = () => {
  try {
    // 获取视口高度
    const viewportHeight = window.innerHeight

    // 更保守的预留空间计算
    // 减去：顶部导航(60px) + 页面标题(50px) + 搜索区域(100px) + 统计卡片(80px) + 表格头部(50px) + 分页器(80px) + 安全边距(80px)
    const reservedHeight = 500 // 增加预留空间，更保守
    const availableHeight = Math.max(viewportHeight - reservedHeight, 150)

    // 每行高度约26px（根据我们的表格样式）
    const rowHeight = 26

    // 计算可显示的行数，再减少20%作为缓冲
    const maxRows = Math.floor(availableHeight / rowHeight * 0.8)

    // 设置更保守的分页大小范围
    let optimalPageSize = Math.max(5, Math.min(maxRows, 30))

    // 根据屏幕尺寸调整 - 更保守的策略
    if (viewportHeight <= 700) {
      // 小屏幕，包括您当前的695px
      optimalPageSize = Math.min(optimalPageSize, 10)
    } else if (viewportHeight <= 900) {
      // 中小屏幕
      optimalPageSize = Math.min(optimalPageSize, 15)
    } else if (viewportHeight <= 1200) {
      // 中等屏幕
      optimalPageSize = Math.min(optimalPageSize, 20)
    } else {
      // 大屏幕
      optimalPageSize = Math.min(optimalPageSize, 25)
    }

    // 确保是5的倍数，便于用户理解
    optimalPageSize = Math.ceil(optimalPageSize / 5) * 5

    return optimalPageSize
  } catch (error) {
    console.warn('计算分页大小失败，使用默认值:', error)
    return 10 // 更保守的默认值
  }
}

// 更新分页大小
const updatePageSize = () => {
  const newPageSize = calculateOptimalPageSize()
  if (newPageSize !== pageSize.value) {
    pageSize.value = newPageSize
    queryParams.pageSize = newPageSize

    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(total.value / newPageSize)
    if (currentPage.value > maxPage && maxPage > 0) {
      currentPage.value = maxPage
      queryParams.pageNum = maxPage
    }

    // 重新获取数据
    getProjectListData()
  }
}

onMounted(async () => {
  // 确保非项目领导用户不会停留在关注项目标签页
  if (activeTab.value === 'followed' && !isProjectLeader.value) {
    activeTab.value = 'all'
  }

  // 初始化分页大小
  updatePageSize()

  // 监听窗口大小变化，动态调整分页大小
  const handleResize = () => {
    // 防抖处理，避免频繁调用
    clearTimeout(window.resizeTimer)
    window.resizeTimer = setTimeout(() => {
      updatePageSize()
    }, 300)
  }

  window.addEventListener('resize', handleResize)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (window.resizeTimer) {
      clearTimeout(window.resizeTimer)
    }
  })

  // 初始加载数据 - 确保字典数据优先加载
  await getDictionaryTypesData() // 先获取字典数据
  await getSalesUserList() // 获取销售用户列表
  await getAiGenerationStatusData() // 获取AI生成状态枚举

  // 字典数据加载完成后再获取项目列表
  getProjectListData() // 初始加载时不显示提示
})


</script>

<style scoped>
.projects {
  width: 100%;
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.block-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  /* 移除固定高度，让容器自动填充可用空间，与左侧导航栏对齐 */
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.header-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.block-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto; /* 改为auto，允许滚动 */
  min-height: 0; /* 重要：允许flex子项收缩 */
}

/* 标签页样式 */
.tabs-container {
  margin-bottom: 16px;
}

:deep(.el-tabs__header) {
  margin: 0 0 16px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #e4e7ed;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

.search-container {
  margin-bottom: 12px;
}

.search-form-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-button {
  display: flex;
  justify-content: flex-end;
}

/* 数据统计卡片样式 */
.stats-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 180px;
  height: 80px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-right: 16px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  transition: all 0.3s;
}

.stat-card:last-child {
  margin-right: 0;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
}

.stat-icon .el-icon {
  font-size: 24px;
  color: #fff;
}

.stat-icon.implementing {
  background-color: #67C23A;
}

.stat-icon.paused {
  background-color: #E6A23C;
}

.stat-icon.pending {
  background-color: #909399;
}

.stat-icon.total {
  background-color: #409EFF;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

/* 表格容器 - 优化自适应设计 */
.table-container {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
}

/* 表格内容区域 */
.table-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 分页器容器 - 参考用户管理页面的简洁设计 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 0 24px 0; /* 增加上下间距 */
  gap: 16px;
  margin-top: auto; /* 推到底部 */
  flex-shrink: 0; /* 防止收缩 */
  border-top: 1px solid #f0f0f0; /* 添加分隔线 */
  background: #fafafa; /* 轻微背景色区分 */
}

.pagination-info {
  color: #606266;
  font-size: 14px;
}

/* 加载容器样式 */
.loading-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  min-height: 400px;
}

/* 分页器本身样式优化 */
:deep(.el-pagination) {
  padding: 0;
  height: auto; /* 改为auto，让分页器自然高度 */
  min-height: 32px; /* 设置最小高度 */
  display: flex;
  align-items: center;
}

:deep(.el-pagination .el-pagination__total) {
  font-weight: normal;
  margin-right: 8px;
}

:deep(.el-pagination button) {
  min-width: 32px;
  height: 32px;
}

:deep(.el-pagination .el-pager li) {
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  font-weight: normal;
}

/* 移除表格高度干扰，让Element Plus自己管理 */

/* 当表格内容较少时，减少空白区域 */
:deep(.el-table__empty-block) {
  min-height: 200px !important;
}

/* 表单样式 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

/* 确保表单元素样式统一 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 4px;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #5B7BFA inset !important;
}

/* 按钮样式 */
:deep(.el-button) {
  font-weight: normal;
  border-radius: 4px;
}

:deep(.el-button--primary:not(.is-link)) {
  background-color: #409EFF;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 统一其他组件样式 */
:deep(.el-card),
:deep(.el-dialog),
:deep(.el-dropdown-menu),
:deep(.el-menu),
:deep(.el-pagination),
:deep(.el-popover) {
  border-radius: 8px;
}

@media (max-width: 768px) {
  .stats-container {
    flex-wrap: wrap;
  }

  .stat-card {
    min-width: calc(50% - 8px);
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .stat-card:nth-child(2n) {
    margin-right: 0;
  }

  /* 移动端分页器优化 */
  .pagination-container {
    flex-direction: column;
    gap: 12px;
    padding: 16px 0 20px 0;
  }

  .pagination-info {
    order: 2; /* 信息显示在分页器下方 */
  }

  /* 移动端容器高度调整 - 使用flex自动填充，与桌面端保持一致 */
}
</style> 