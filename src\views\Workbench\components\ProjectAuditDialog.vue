<template>
  <!-- 审核抽屉 -->
  <el-drawer
    v-model="dialogVisible"
    size="60%"
    direction="rtl"
    destroy-on-close
    @closed="resetAudit"
    class="audit-drawer"
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="closeDialog">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Document /></el-icon>
          计划审核 - {{ currentProject?.projectName || '项目计划审核' }}
        </div>
      </div>
    </template>

    <div class="audit-content drawer-optimized">
      <!-- 审核表格区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><List /></el-icon>
            <span class="section-title">计划审核列表</span>
            <span class="section-subtitle">{{ auditData.length }} 项待审核</span>
          </div>
          <div class="section-content">
            <el-table
              ref="auditTable"
              :data="auditData"
              border
              style="width: 100%;"
              :header-cell-style="{background: '#f5f7fa', color: '#606266', padding: '8px 0'}"
              size="small"
              v-loading="loading"
              element-loading-text="加载审核数据中..."
            >
      <el-table-column label="序号" width="80" align="center">
        <template #default="{ row }">
          <span>{{ row.seriNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="taskName" label="工作项名称" width="150" align="left" :show-overflow-tooltip="true" />
      <el-table-column prop="responsible" label="责任人" width="60" align="center" />
      <el-table-column prop="executorName" label="执行人" width="60" align="center" />
      <el-table-column prop="planStartDate" label="计划开始时间" width="110" align="center" />
      <el-table-column prop="planEndDate" label="计划完成时间" width="110" align="center" />
      <el-table-column prop="type" label="类型" width="60" align="center">
        <template #default="{ row }">
          <el-tag
            :type="row.type === '新增' ? 'success' : row.type === '删除' ? 'danger' : 'warning'"
            effect="light"
            size="small"
          >
            {{ row.type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="changeDescription" label="变更说明" min-width="180" align="left" :show-overflow-tooltip="true" />
      <el-table-column prop="auditOpinion" label="审核意见" width="180" align="center">
        <template #default="{ row }">
          <div v-if="row.tempOpinion || (row.auditStatus && row.auditOpinion)" class="audit-opinion-container">
            <el-tooltip
              :content="`点击编辑：${row.tempOpinion || row.auditOpinion}`"
              placement="top"
            >
              <div
                class="opinion-content-clickable"
                @click="openOpinionDialog(row)"
              >
                <span class="opinion-text-clickable">{{ (row.tempOpinion || row.auditOpinion) }}</span>
                <el-icon class="edit-icon"><Edit /></el-icon>
              </div>
            </el-tooltip>
          </div>
          <div v-else class="textarea-container">
            <el-button
              type="primary"
              link
              @click="openOpinionDialog(row)"
              class="input-opinion-btn"
            >
              <el-icon><Plus /></el-icon>
              请输入
            </el-button>
          </div>
        </template>
      </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button type="danger" @click="rejectAllAudit" plain>驳回</el-button>
        <el-button type="primary" @click="approveAllAudit">通过</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 审核意见输入抽屉 -->
  <el-drawer
    v-model="opinionDialogVisible"
    size="320px"
    direction="rtl"
    destroy-on-close
    class="opinion-drawer"
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="opinionDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Document /></el-icon>
          审核意见
        </div>
      </div>
    </template>

    <div class="opinion-drawer-content">
      <div class="opinion-input-section">
        <div class="section-label">审核意见</div>
        <el-input
          v-model="currentOpinion"
          type="textarea"
          :rows="4"
          placeholder="请输入审核意见..."
          class="opinion-textarea"
        />
      </div>

      <div class="quick-options-section">
        <div class="section-label">快速选择</div>
        <div class="quick-options">
          <div class="quick-option-btn" @click="selectQuickOption('通过')">
            通过
          </div>
          <div class="quick-option-btn" @click="selectQuickOption('时间太长了')">
            时间太长了
          </div>
          <div class="quick-option-btn" @click="selectQuickOption('任务描述不清楚')">
            任务描述不清楚
          </div>
        </div>
      </div>

      <div class="opinion-drawer-footer">
        <el-button type="primary" @click="saveOpinion">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Close, Document, List, Edit, Plus } from '@element-plus/icons-vue';
import { getProjectPlanReviewList, batchReviewProjectPlan } from '@/api/projectPlanDraft';
import { getReviewStatus } from '@/api/enums';

// Props 声明
const props = defineProps({
  visible: Boolean,
  projectData: Object
});

// Emits 声明
const emit = defineEmits(['update:visible', 'audit-approved', 'audit-rejected']);

// 组件内部状态
const dialogVisible = ref(false);
const currentProject = ref(null);
const auditTable = ref(null);
const reviewStatusEnum = ref({}); // 审核状态枚举

// 审核意见弹窗相关状态
const opinionDialogVisible = ref(false);
const currentOpinion = ref('');
const currentRow = ref(null);

// 审核数据
const auditData = ref([]);
const loading = ref(false);

// 加载审核数据
const loadAuditData = async () => {
  // 尝试多种可能的ID字段名
  const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

  if (!projectId) {
    ElMessage.error('项目ID不能为空');
    return;
  }



  loading.value = true;
  try {

    const response = await getProjectPlanReviewList(projectId);


    if (response && response.data && Array.isArray(response.data)) {


      if (response.data.length === 0) {
        auditData.value = [];
      } else {
        // 直接使用API返回的数据，只添加必要的字段
        auditData.value = response.data.map(item => ({
          ...item, // 保留所有原始字段，包括 seriNum
          taskName: item.planName,
          responsible: item.ownerName,
          executorName: item.executorName || '',
          planStartDate: item.planStartTime ? item.planStartTime.split(' ')[0] : '',
          planEndDate: item.planEndTime ? item.planEndTime.split(' ')[0] : '',
          type: getChangeTypeLabel(item.changeType),
          changeDescription: item.changeRemark || '-',
          auditOpinion: item.reviewOpinion || '',
          auditStatus: item.reviewOpinion ? '已审核' : '',
          tempOpinion: ''
        }));
      }
    } else {
      console.error('返回数据格式异常:', response);
      ElMessage.warning('返回数据格式异常');
      auditData.value = [];
    }
  } catch (error) {

    // 显示更详细的错误信息
    let errorMessage = '加载审核数据失败';
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error.message) {
      errorMessage = error.message;
    }

    ElMessage.error(errorMessage);
    auditData.value = [];
  } finally {
    loading.value = false;
  }
};

// 转换变更类型标签
const getChangeTypeLabel = (changeType) => {
  // 支持多种格式的类型映射
  const typeMap = {
    // 码值格式
    '1': '新增',
    '2': '编辑',
    '3': '删除',
    // 英文格式
    'ADD': '新增',
    'CREATE': '新增',
    'UPDATE': '编辑',
    'EDIT': '编辑',
    'DELETE': '删除',
    'REMOVE': '删除',
    // 中文格式（直接返回）
    '新增': '新增',
    '编辑': '编辑',
    '变更': '编辑',
    '删除': '删除'
  };
  return typeMap[changeType] || changeType || '编辑';
};

// 加载审核状态枚举
const loadReviewStatusEnum = async () => {
  try {
    // 直接调用真实API，不使用Mock
    const response = await getReviewStatus();


    if (response && (response.code === 200 || response.code === 0) && response.data) {
      reviewStatusEnum.value = response.data;

    } else {
      // 根据实际API返回格式设置默认映射
      reviewStatusEnum.value = {
        '0': '草稿',
        '1': '待审核',
        '2': '生效中',
        '3': '已驳回'
      };

    }
  } catch (error) {
    console.error('加载审核状态枚举失败:', error);
    // 根据实际API返回格式设置默认映射
    reviewStatusEnum.value = {
      '0': '草稿',
      '1': '待审核',
      '2': '生效中',
      '3': '已驳回'
    };

  }
};

// 获取审核状态码（用于后端接口）
const getReviewStatusCode = (status) => {

  // 如果已经是状态码，直接返回
  if (reviewStatusEnum.value[status]) {
    return status;
  }

  // 根据中文描述查找对应的状态码
  for (const [code, desc] of Object.entries(reviewStatusEnum.value)) {
    if (desc === status) {
      return code;
    }
  }

  // 根据实际API返回格式的兜底映射
  const statusMap = {
    '通过': '2',  // 生效中
    '驳回': '3',  // 已驳回
    '待审核': '1', // 待审核
    '草稿': '0'   // 草稿
  };

  const result = statusMap[status] || status;
  return result;
};

// 监听父组件传递的可见性状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    currentProject.value = props.projectData;
    // 加载审核状态枚举
    loadReviewStatusEnum();
    // 加载审核数据
    loadAuditData();
    // 延迟应用样式
    setTimeout(applyCompactStyle, 50);
  }
});

// 监听内部可见性状态，同步到父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 打开审核意见抽屉
const openOpinionDialog = (row) => {
  currentRow.value = row;
  currentOpinion.value = row.tempOpinion || '';
  opinionDialogVisible.value = true;
};

// 选择快速选项
const selectQuickOption = (option) => {
  currentOpinion.value = option;
};

// 保存审核意见
const saveOpinion = () => {
  if (!currentRow.value) return;
  
  currentRow.value.tempOpinion = currentOpinion.value;
  opinionDialogVisible.value = false;
};

// 确保表格尺寸紧凑
const applyCompactStyle = () => {
  nextTick(() => {
    if (!auditTable.value) return;
    
    // 强制应用紧凑样式
    const tableRows = auditTable.value.$el.querySelectorAll('.el-table__row');
    tableRows.forEach(row => {
      row.style.height = '36px';
    });
    
    // 应用头部样式
    const headerRow = auditTable.value.$el.querySelector('.el-table__header-row');
    if (headerRow) {
      headerRow.style.height = '36px';
    }
  });
};

// 处理通过所有审核项
const approveAllAudit = async () => {
  // 移除审核意见必填限制，允许直接通过

  try {
    await ElMessageBox.confirm('确认通过所有审核项？', '确认审核', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 获取项目ID
    const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

    // 准备审核数据
    const reviewResult = getReviewStatusCode('通过'); // 使用枚举状态码
    const reviewData = {
      projectId: projectId,
      reviewResult: reviewResult,
      reviewInfoList: auditData.value.map(item => ({
        draftId: item.draftId,
        reviewOpinion: item.tempOpinion || '同意变更',
        version: item.version
      }))
    };



    loading.value = true;
    const response = await batchReviewProjectPlan(reviewData);


    ElMessage.success('审核通过成功');
    emit('audit-approved', auditData.value);
    closeDialog();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核通过失败:', error);
      ElMessage.error('审核通过失败');
    }
  } finally {
    loading.value = false;
  }
};

// 处理驳回所有审核项
const rejectAllAudit = async () => {
  // 移除审核意见必填限制，允许直接驳回

  try {
    await ElMessageBox.confirm('确认驳回所有审核项？', '确认审核', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 获取项目ID
    const projectId = currentProject.value?.id || currentProject.value?.projectId || currentProject.value?.Id;

    // 准备审核数据
    const reviewResult = getReviewStatusCode('驳回'); // 使用枚举状态码
    const reviewData = {
      projectId: projectId,
      reviewResult: reviewResult,
      reviewInfoList: auditData.value.map(item => ({
        draftId: item.draftId,
        reviewOpinion: item.tempOpinion || '不同意变更',
        version: item.version
      }))
    };



    loading.value = true;
    const response = await batchReviewProjectPlan(reviewData);


    ElMessage.success('审核驳回成功');
    emit('audit-rejected', auditData.value);
    closeDialog();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核驳回失败:', error);
      ElMessage.error('审核驳回失败');
    }
  } finally {
    loading.value = false;
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 重置审核数据
const resetAudit = () => {
  currentProject.value = null;
  // 在实际应用中，可以重置审核数据或保留状态
};
</script>

<style scoped>


.audit-header {
  padding: 0 0 12px 0;
}

.project-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 审核表格样式优化 */
.audit-drawer :deep(.el-table) {
  margin-bottom: 12px;
  font-size: 12px;
}

.audit-drawer :deep(.el-table .cell) {
  padding: 0 !important;
  line-height: 1 !important;
  white-space: nowrap;
}

.audit-drawer :deep(.el-table--small) {
  font-size: 12px;
}

.audit-drawer :deep(.el-table__row) {
  height: 36px !important;
}

.audit-drawer :deep(.el-table .el-table__cell) {
  padding: 0 !important;
}

.audit-drawer :deep(.el-textarea__inner) {
  padding: 2px 4px !important;
  min-height: 28px !important;
  height: 28px !important;
  resize: none;
  font-size: 12px;
  line-height: 1.2 !important;
}

.compact-textarea :deep(.el-textarea__inner) {
  height: 28px !important;
  min-height: 28px !important;
}

.textarea-container {
  padding: 2px 0;
  height: 32px;
  max-height: 32px;
}

.audit-drawer :deep(.el-table th.el-table__cell) {
  padding: 0 !important;
  height: 36px !important;
  line-height: 18px;
}

.audit-drawer :deep(.el-table td.el-table__cell) {
  padding: 0 !important;
  height: 36px !important;
}

.audit-drawer :deep(.el-table__body) tr.el-table__row {
  height: 36px !important;
}

.audit-drawer :deep(.el-table__body) td {
  height: 36px !important;
}

.audit-drawer :deep(.el-table .el-tag) {
  height: 18px;
  line-height: 16px;
  padding: 0 4px;
  font-size: 11px;
}

.audit-opinion {
  padding: 0;
  text-align: left;
  color: #606266;
  font-size: 12px;
  padding-left: 4px;
  height: 32px;
  line-height: 32px;
  overflow: hidden;
}



/* 确保抽屉表格内容居中 */
.audit-drawer :deep(.el-table th.el-table__cell > .cell) {
  text-align: center;
}

.audit-drawer :deep(.el-table td.el-table__cell:not(.column-left)) {
  text-align: center;
}

/* 审核意见抽屉样式 */
.opinion-drawer {
  /* 覆盖 Element Plus 抽屉默认样式 */
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0 !important;
  }

  .custom-drawer-header {
    display: flex;
    align-items: center;
    padding: 16px 24px 16px 8px !important;
    gap: 6px;
    border-bottom: 1px solid #e4e7ed;

    .drawer-close-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
      color: #909399;
      transition: all 0.2s;
      flex-shrink: 0;

      &:hover {
        background-color: #f5f7fa;
        color: #606266;
      }
    }

    .drawer-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      flex: 1;

      .drawer-icon {
        color: #409eff;
      }
    }
  }

  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    text-align: left !important;
  }
}

/* 审核意见抽屉内容样式 */
.opinion-drawer-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1;
}

.opinion-input-section {
  .section-label {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
  }

  .opinion-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      font-size: 14px;
      line-height: 1.5;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.quick-options-section {
  .section-label {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
  }

  .quick-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .quick-option-btn {
    width: 100%;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    background-color: #ffffff;
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    box-sizing: border-box;

    &:hover {
      background-color: #ecf5ff;
      border-color: #b3d8ff;
      color: #409eff;
    }

    &:active {
      background-color: #d9ecff;
      border-color: #409eff;
    }
  }
}

.opinion-drawer .opinion-drawer-footer {
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center;
  text-align: left !important;

  .el-button {
    margin: 0 !important;
    margin-left: 0 !important;
    margin-right: auto !important;
  }
}

/* 强制确定按钮左对齐 */
.opinion-drawer :deep(.el-drawer__body) {
  text-align: left !important;
}

.opinion-drawer :deep(.opinion-drawer-footer) {
  display: flex !important;
  justify-content: flex-start !important;
  text-align: left !important;
}

.opinion-drawer :deep(.opinion-drawer-footer .el-button) {
  margin: 0 !important;
  align-self: flex-start !important;
}

/* 重复样式已删除，快速选择按钮样式在上面的 .quick-options-section 中定义 */

/* 统一按钮圆角 */
.audit-drawer :deep(.el-button) {
  border-radius: 4px;
}

/* 统一表格样式 */
.audit-drawer :deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

/* 统一输入框、下拉框圆角 */
.audit-drawer :deep(.el-input__wrapper),
.audit-drawer :deep(.el-textarea__wrapper) {
  border-radius: 4px;
}

/* 审核抽屉样式 */
.audit-drawer {
  /* 覆盖 Element Plus 抽屉默认样式 */
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-drawer__body) {
    padding: 24px;
    overflow-y: auto;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background: #fafafa;
  }
}

/* 自定义抽屉头部 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.drawer-close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  font-size: 16px;
  transition: color 0.3s;
}

.drawer-close-btn:hover {
  color: #606266;
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  flex: 1;
  margin-left: 16px;
}

.drawer-icon {
  color: #409EFF;
  font-size: 18px;
}

/* 抽屉底部按钮 */
.drawer-footer {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}

/* 统一的布局样式 */
.section-layout {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: start;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  grid-column: 1 / -1;
  margin-bottom: 12px;
}

.section-icon {
  color: #409EFF;
  font-size: 16px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 审核意见显示样式优化 */
.audit-opinion-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 40px;
  padding: 4px;
}

.opinion-content-clickable {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: rgba(64, 158, 255, 0.08);
  border: 1px solid rgba(64, 158, 255, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  max-width: 100%;
}

.opinion-content-clickable:hover {
  background-color: rgba(64, 158, 255, 0.12);
  border-color: rgba(64, 158, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.opinion-text-clickable {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  word-break: break-word;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.edit-icon {
  font-size: 12px;
  color: #409EFF;
  opacity: 0.7;
  transition: opacity 0.2s;
  flex-shrink: 0;
}

.opinion-content-clickable:hover .edit-icon {
  opacity: 1;
}

.input-opinion-btn {
  font-size: 12px !important;
  color: #909399 !important;
  padding: 4px 8px !important;
  height: 24px !important;
  border-radius: 4px;
  border: 1px dashed #DCDFE6;
  background-color: #FAFAFA;
  transition: all 0.2s;
}

.input-opinion-btn:hover {
  color: #409EFF !important;
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.05);
}

.input-opinion-btn .el-icon {
  font-size: 11px;
  margin-right: 3px;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
  line-height: 16px;
}

.section-content {
  grid-column: 1 / -1;
  margin-left: 0;
}

/* 表单区域间距 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}
</style> 