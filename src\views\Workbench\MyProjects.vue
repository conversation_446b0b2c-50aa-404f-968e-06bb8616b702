<template>
  <div class="projects-section">
    <div class="block-container">
      <div class="block-header">我负责的项目</div>
      <div class="block-content">
        <div class="search-form">
          <el-form :inline="true" :model="queryParams" size="default">
            <el-form-item label="项目名称">
              <el-input 
                v-model="queryParams.projectName" 
                placeholder="请输入项目名称关键词" 
                clearable 
                style="width: 200px" 
                @keyup.enter="handleQuery"
                :prefix-icon="Search"
              />
            </el-form-item>
            <el-form-item label="项目状态">
              <el-select v-model="queryParams.projectState" placeholder="请选择" clearable style="width: 200px">
                <el-option label="全部" value="" />
                <el-option
                  v-for="(label, value) in projectStateDict"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="计划审核状态">
              <el-select v-model="queryParams.planReviewStatus" placeholder="请选择" clearable style="width: 200px">
                <el-option label="全部" value="" />
                <el-option
                  v-for="(label, value) in reviewStatusEnum"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery">
                <el-icon><Search /></el-icon>查询
              </el-button>
              <el-button @click="resetQuery" style="margin-left: 8px;">
                <el-icon><RefreshLeft /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="table-container">
          <el-table 
            :data="processedData" 
            border 
            style="width: 100%" 
            v-loading="loading"
            @sort-change="handleSortChange">
            <el-table-column type="index" label="序号" width="50" align="center" :index="indexMethod" />
            <el-table-column prop="projectName" label="项目名称" min-width="80" align="left" show-overflow-tooltip class-name="column-left">
              <template #default="{ row }">
                <span
                  class="project-name-link"
                  @click="handleProjectClick(row)"
                  :title="row.projectName"
                >
                  {{ row.projectName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="area" label="区域" width="80" align="center" />
            <el-table-column prop="status" label="项目状态" width="80" align="center">
              <template #default="{ row }">
                <span class="nowrap-cell">{{ projectStateDict[row.status] || row.status }}</span>
              </template>
            </el-table-column>
            <el-table-column 
              prop="progress" 
              label="项目进度" 
              width="100" 
              align="center"
              sortable>
              <template #default="{ row }">
                <div class="progress-bar">
                  <div class="progress-inner" :style="{width: (row.progress || 0) + '%'}"></div>
                  <span class="progress-text">{{ (row.progress !== undefined && row.progress !== null && row.progress !== '') ? row.progress + '%' : '0%' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="timeRange" label="项目工期" width="160" align="center" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="nowrap-cell">{{ row.timeRange }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="version"
              label="计划版本"
              width="80"
              align="center"
              sortable
              show-overflow-tooltip />
            <el-table-column
              prop="stage"
              label="计划审核"
              width="80"
              align="center"
              sortable
              show-overflow-tooltip>
              <template #default="{ row }">
                <span class="nowrap-cell">{{ row.stage }}</span>
              </template>
            </el-table-column>
            <el-table-column 
              prop="todayTasks" 
              label="当日任务" 
              width="80" 
              align="center"
              sortable />
            <el-table-column 
              prop="comingTasks" 
              label="将到期任务" 
              width="90" 
              align="center"
              sortable />
            <el-table-column 
              prop="overdueTasks" 
              label="逾期任务" 
              width="80" 
              align="center"
              sortable />
            <el-table-column 
              prop="reportStatus" 
              label="日报状态" 
              width="90" 
              align="center"
              sortable>
              <template #default="{ row }">
                <div class="report-status-wrapper">
                  <span 
                    class="report-status-badge"
                    :class="row.reportStatus === '已提交' ? 'status-submitted' : 'status-pending'"
                  >
                    <i class="status-icon" :class="row.reportStatus === '已提交' ? 'icon-check' : 'icon-clock'"></i>
                    {{ row.reportStatus }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" :width="canShowAuditButton ? 180 : 120" fixed="right" align="center">
              <template #default="{ row }">
                <!-- 审核按钮：只有项目领导(roleId: 1)和计划员(roleId: 2)可见 -->
                <el-button
                  v-if="canShowAuditButton"
                  :type="row.planReviewStatus === '1' ? 'warning' : 'primary'"
                  link
                  @click="handleAudit(row)"
                  :class="{ 'pending-audit-btn': row.planReviewStatus === '1' }"
                >
                  <el-icon><edit-pen /></el-icon>审核
                </el-button>
                <el-button type="primary" link @click="handleReportAction(row)">
                  <el-icon><Document /></el-icon>{{ getReportButtonText }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              :pager-count="5"
              layout="prev, pager, next"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入独立弹窗组件 -->
  <ProjectAuditDialog
    v-model:visible="auditDialogVisible"
    :project-data="currentProject"
    @audit-approved="handleAuditApproved"
    @audit-rejected="handleAuditRejected"
  />
    
  <ProjectReportDialog
    v-model:visible="reportDialogVisible"
    :project-data="currentProject"
    @report-submitted="handleReportSubmitted"
  />



  <!-- 日报详情查看抽屉 -->
  <el-drawer
    v-model="reportDetailDialogVisible"
    size="60%"
    direction="rtl"
    destroy-on-close
    class="report-drawer"
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="reportDetailDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          <el-icon class="drawer-icon"><Document /></el-icon>
          {{ isViewMode ? '查看日报' : '填写日报' }} - {{ currentViewProject?.projectName || '项目日报' }}
        </div>
      </div>
    </template>

    <div class="report-tabs">
      <el-tabs v-model="activeReportTab" class="drawer-tabs">
        <!-- 今日开工信息标签页 -->
        <el-tab-pane label="今日开工信息" name="workInfo">
          <div class="tab-content work-info-content drawer-optimized">
            <!-- 开工说明区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Edit /></el-icon>
                  <span class="section-title">开工说明</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.workDescription"
                    type="textarea"
                    :rows="4"
                    placeholder="请详细描述今日开工情况、工作内容等..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Picture /></el-icon>
                  <span class="section-title">现场图片</span>
                </div>
                <div class="section-content">
                  <div class="upload-area-drawer">
                    <div class="upload-container-horizontal">
                      <!-- 图片列表显示 -->
                      <div v-if="reportForm.workImages && reportForm.workImages.length > 0" class="image-list-horizontal">
                        <div v-for="(img, index) in reportForm.workImages" :key="index" class="image-item-horizontal">
                          <div class="image-container">
                            <img :src="img.url || img.preview || ''" alt="图片预览" class="image-preview-drawer" v-if="img.url || img.preview" @click="handlePreviewImage(img)" />
                            <div class="image-preview-empty" v-else>无预览</div>
                          </div>
                        </div>
                      </div>
                      <!-- 无图片时的提示 -->
                      <div v-else class="no-images-tip">
                        <span style="color: #909399; font-size: 14px;">暂无图片</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 当日计划进展标签页 -->
        <el-tab-pane label="当日计划进展" name="planProgress">
          <div class="tab-content">
            <el-table
              :data="reportForm.taskProgress"
              border
              style="width: 100%"
              size="small"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '8px 0' }"
            >
              <el-table-column type="index" label="序号" width="50" align="center" />
              <el-table-column prop="taskName" label="工作项名称" width="100" show-overflow-tooltip />
              <el-table-column prop="planTime" label="计划时间" width="160" show-overflow-tooltip />
              <el-table-column prop="responsible" label="责任人" width="70" align="center" />
              <el-table-column prop="executorName" label="执行人" width="70" align="center" />
              <el-table-column prop="progress" label="当日进展" min-width="120" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="progress-input">
                    <el-input v-model="row.progressDesc" placeholder="点击添加进展" size="small" :disabled="isViewMode" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="任务状态" width="120" align="center">
                <template #default="{ row }">
                  <el-select v-model="row.status" placeholder="选择状态" size="small" :disabled="isViewMode" style="width: 90px; margin: 0 auto; display: block;">
                    <el-option
                      v-for="(label, code) in planStatusEnum"
                      :key="code"
                      :label="label"
                      :value="label"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="images" label="图片" width="120" align="center">
                <template #default="{ row }">
                  <div v-if="row.images && row.images.length">
                    <div class="task-image-container">
                      <el-button type="primary" link size="small" @click="handlePreviewImage(row.images[0])">
                        {{ row.images[0].name }}
                      </el-button>
                    </div>
                  </div>
                  <span v-else>无</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 其他工作说明标签页 -->
        <el-tab-pane label="其他工作说明" name="otherWorkInfo">
          <div class="tab-content other-work-content drawer-optimized">
            <!-- 其他工作内容 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Document /></el-icon>
                  <span class="section-title">其他工作内容</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.otherWorkContent"
                    type="textarea"
                    :rows="3"
                    placeholder="请详细描述其他工作内容..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>

            <!-- 完成情况 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Check /></el-icon>
                  <span class="section-title">完成情况</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.completionStatus"
                    placeholder="请输入完成情况..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>

            <!-- 遇到的问题 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><InfoFilled /></el-icon>
                  <span class="section-title">遇到的问题</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.problemsEncountered"
                    placeholder="请输入遇到的问题..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>

            <!-- 解决方案 -->
            <div class="form-section">
              <div class="section-layout">
                <div class="section-header">
                  <el-icon class="section-icon"><Plus /></el-icon>
                  <span class="section-title">解决方案</span>
                </div>
                <div class="section-content">
                  <el-input
                    v-model="reportForm.solutions"
                    placeholder="请输入解决方案..."
                    class="description-input"
                    :disabled="isViewMode"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>


  </el-drawer>

  <!-- 图片预览对话框 -->
  <el-dialog v-model="previewVisible" title="图片预览" width="800px" center>
    <img :src="previewUrl" alt="预览图片" style="width: 100%;">
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, RefreshLeft, Document, Close, Loading, Check, Edit, Picture, InfoFilled, Plus } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getMyProjects } from '@/api/project';
import { getDictionaryTypes, getDictionaryMap } from '@/api/dictionary';
import { getDailyList, getDailyDetail } from '@/api/daily';
import { getReviewStatus, getPlanStatus } from '@/api/enums';
import { useUserStore } from '@/store/modules/user';
import ProjectAuditDialog from './components/ProjectAuditDialog.vue';
import ProjectReportDialog from './components/ProjectReportDialog.vue';
import eventBus, { EVENTS } from '@/utils/eventBus';

// 用户store和路由
const userStore = useUserStore();
const router = useRouter();

// 权限控制：审核按钮权限判断
const canShowAuditButton = computed(() => {
  const userRoleId = userStore.userRole;
  // 只有项目领导(roleId: 1)和计划员(roleId: 2)可以看到审核按钮
  return userRoleId === 1 || userRoleId === 2;
});

// 权限控制：日报按钮文本判断
const getReportButtonText = computed(() => {
  const userRoleId = userStore.userRole;
  // 项目经理(roleId: 3)显示"日报"，领导(roleId: 1)和计划员(roleId: 2)显示"查看日报"
  return userRoleId === 3 ? '日报' : '查看日报';
});

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 查询参数
const queryParams = reactive({
  projectName: '',
  projectState: '',
  planReviewStatus: '', // 与数据字段名保持一致
  pageNum: 1,
  pageSize: 5
});

// 加载状态
const loading = ref(false);

// 排序相关状态
const sortField = ref('');
const sortOrder = ref(''); // 'ascending'(升序), 'descending'(降序), 或 null/''(不排序)

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5); // 改为响应式
const total = ref(0);

// 弹窗相关状态
const auditDialogVisible = ref(false);
const reportDialogVisible = ref(false);
const currentProject = ref(null);

// 查看日报相关
const currentViewProject = ref(null);

// 日报详情查看弹窗相关
const reportDetailDialogVisible = ref(false);
const isViewMode = ref(true); // 始终为查看模式
const activeReportTab = ref('workInfo');
const reportForm = ref({
  workDescription: '',
  workImages: [],
  taskProgress: [],
  otherWorkContent: '',
  completionStatus: '',
  problemsEncountered: '',
  solutions: ''
});

// 图片预览相关
const previewVisible = ref(false);
const previewUrl = ref('');

// 项目列表数据
const projectList = ref([]);

// 项目状态字典
const projectStateDict = ref({});

// 审核状态枚举
const reviewStatusEnum = ref({});

// 计划状态枚举
const planStatusEnum = ref({});

// 获取项目列表数据
const fetchProjectList = async () => {
  loading.value = true;
  try {
    // 更新查询参数
    queryParams.pageNum = currentPage.value;
    queryParams.pageSize = pageSize.value;

    // 调用API获取数据
    const res = await getMyProjects(queryParams);

    if (res && (res.code === 200 || res.code === 0)) {
      projectList.value = res.data || [];
      total.value = res.total || 0;
    } else {
      ElMessage.error(res?.message || '获取项目列表失败');
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    ElMessage.error('获取项目列表失败: ' + (error.message || '网络错误'));
  } finally {
    loading.value = false;
  }
};

// 获取审核状态显示文本
const getReviewStatusDisplay = (statusCode) => {
  if (!statusCode) return '';
  return reviewStatusEnum.value[statusCode] || statusCode;
};

// 处理区域显示，去掉括号内容
const formatAreaDisplay = (region) => {
  if (!region) return '未知';
  // 如果包含括号，只取括号前的内容
  const bracketIndex = region.indexOf('(');
  if (bracketIndex > 0) {
    return region.substring(0, bracketIndex).trim();
  }
  // 如果包含中文括号，只取括号前的内容
  const chineseBracketIndex = region.indexOf('（');
  if (chineseBracketIndex > 0) {
    return region.substring(0, chineseBracketIndex).trim();
  }
  return region;
};

// 处理数据转换，将API数据转换为表格所需格式
const processedData = computed(() => {
  return projectList.value.map(item => ({
    ...item,
    // 确保id字段正确映射 - my-overview API返回的是projectId字段
    id: item.projectId || item.id,
    status: item.projectState,
    timeRange: item.duration || '',
    area: formatAreaDisplay(item.region),
    version: item.planVersion || '',
    stage: getReviewStatusDisplay(item.planReviewStatus), // 转换审核状态
    todayTasks: item.todayTaskCount ?? 0,
    comingTasks: item.upcomingTaskCount ?? 0,
    overdueTasks: item.overdueTaskCount ?? 0,
    reportStatus: item.todayReportStatus || '',
    // 项目进度处理
    progress: (item.progress !== undefined && item.progress !== null && item.progress !== '') ? Number(item.progress) : 0
  }));
});

// 动态计算分页大小 - 针对Workbench优化
const calculateOptimalPageSize = () => {
  try {
    // 获取视口高度
    const viewportHeight = window.innerHeight



    // Workbench页面的预留空间调整，更合理的估算
    // 减去：顶部导航(60px) + 项目总览区块(160px) + 执行情况区块(160px) + 搜索区域(80px) + 表格头部(40px) + 分页器(60px) + 安全边距(40px)
    const reservedHeight = 600 // 减少预留空间
    const availableHeight = Math.max(viewportHeight - reservedHeight, 150)

    // 每行高度约26px（与Projects页面保持一致）
    const rowHeight = 26

    // 计算可显示的行数，减少10%作为缓冲（比Projects页面更宽松）
    const maxRows = Math.floor(availableHeight / rowHeight * 0.9)

    // 设置更保守的分页大小范围，适合Workbench的紧凑布局
    let optimalPageSize = Math.max(5, Math.min(maxRows, 20))

    // 根据屏幕尺寸调整 - 更合理的策略，适合Workbench
    if (viewportHeight <= 700) {
      // 小屏幕 - 显示较少项目
      optimalPageSize = Math.min(optimalPageSize, 5)
    } else if (viewportHeight <= 800) {
      // 中小屏幕
      optimalPageSize = Math.min(optimalPageSize, 8)
    } else if (viewportHeight <= 1000) {
      // 中等屏幕 - 您当前的924px应该在这个范围
      optimalPageSize = Math.min(optimalPageSize, 10)
    } else {
      // 大屏幕
      optimalPageSize = Math.min(optimalPageSize, 15)
    }

    // 确保最小值为5
    return Math.max(5, optimalPageSize)
  } catch (error) {
    console.warn('计算分页大小失败，使用默认值:', error)
    return 5 // 默认值
  }
}

// 窗口大小变化处理
const handleResize = () => {
  const newPageSize = calculateOptimalPageSize()
  if (newPageSize !== pageSize.value) {
    pageSize.value = newPageSize
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(total.value / pageSize.value)
    if (currentPage.value > maxPage && maxPage > 0) {
      currentPage.value = maxPage
    }
    // 重新获取数据
    fetchProjectList()
  }
}

// 防抖处理的窗口大小变化
let resizeTimer = null
const debouncedHandleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(handleResize, 300)
}

// 序号计算方法，保持连续
const indexMethod = (index) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

// 处理项目名称点击事件 - 跳转到项目详情页
const handleProjectClick = (row) => {
  // 跳转到项目详情页面，携带contractNo用于精确查找
  router.push({
    path: `/project-detail/${row.id}`,
    query: {
      contractNo: row.contractNo
    }
  });
};

// 处理表格排序变更
const handleSortChange = ({ column, prop, order }) => {
  // 保存排序状态
  sortField.value = prop;
  sortOrder.value = order; // 'ascending', 'descending', 或 null
  
  // 排序变化后重新获取数据
  fetchProjectList();
  
  // 显示排序提示
  if (order) {
    const orderText = order === 'ascending' ? '升序' : '降序';
    ElMessage.success(`按${column.label}${orderText}排列`);
  }
};

// 查询按钮事件
const handleQuery = () => {
  // 重置到第一页
  currentPage.value = 1;
  // 获取数据
  fetchProjectList();
};

// 重置按钮事件
const resetQuery = () => {
  // 清空查询参数
  queryParams.projectName = '';
  queryParams.projectState = '';
  queryParams.planReviewStatus = '';

  // 重置排序
  sortField.value = '';
  sortOrder.value = '';

  // 执行查询
  handleQuery();
};

// 审核按钮事件
const handleAudit = (row) => {
  currentProject.value = row;
  auditDialogVisible.value = true;
};

// 填写日报按钮事件
const handleFillReport = (row) => {
  currentProject.value = row;
  reportDialogVisible.value = true;
};

// 日报按钮统一处理方法
const handleReportAction = (row) => {
  if (isProjectManager.value) {
    // 项目经理：填写日报（现有功能）
    handleFillReport(row);
  } else {
    // 领导/计划员：查看日报（新功能）
    handleViewReports(row);
  }
};

// 查看日报功能 - 查看当日日报
const handleViewReports = async (row) => {
  try {
    currentViewProject.value = row;

    // 获取项目ID
    const projectId = row.id || row.projectId || row._id;
    if (!projectId) {
      ElMessage.error('项目ID不存在，无法查看日报');
      return;
    }

    // 查看当日日报
    await loadTodayDailyReport(projectId);

  } catch (error) {
    console.error('查看日报失败:', error);
    ElMessage.error('查看日报失败：' + (error.message || '系统错误'));
  }
};

// 加载当日日报并直接打开详情弹窗
const loadTodayDailyReport = async (projectId) => {
  try {
    const today = new Date().toISOString().split('T')[0]; // 获取今日日期 YYYY-MM-DD

    const params = {
      projectId: projectId,
      reportDate: today, // 直接传入当日日期
      pageNum: 1,
      pageSize: 10
    };

    const response = await getDailyList(params);

    if (response && (response.code === 200 || response.code === 0) && response.data && response.data.length > 0) {
      const todayReport = response.data[0]; // 直接取第一条，因为已经按reportDate过滤了

      // 直接打开当日日报详情弹窗
      await viewSingleReport(todayReport);
    } else {
      ElMessage.info(`今日（${today}）暂无日报数据`);
    }
  } catch (error) {
    console.error('加载当日日报异常:', error);
    ElMessage.error('加载日报失败：' + (error.message || '系统错误'));
  }
};

// 加载最新日报并直接打开详情弹窗（保留原方法，以备后用）
const loadLatestDailyReport = async (projectId) => {
  try {
    const params = {
      projectId: projectId,
      pageNum: 1,
      pageSize: 1 // 只获取最新的一条日报
    };

    const response = await getDailyList(params);

    if (response && (response.code === 200 || response.code === 0) && response.data && response.data.length > 0) {
      const latestReport = response.data[0];

      // 直接打开最新日报的详情弹窗
      await viewSingleReport(latestReport);
    } else {
      ElMessage.warning('该项目暂无日报数据');
    }
  } catch (error) {
    console.error('加载最新日报异常:', error);
    ElMessage.error('加载日报失败：' + (error.message || '系统错误'));
  }
};

// 加载日报列表（保留原方法，以备后用）
const loadDailyReports = async (projectId) => {
  try {
    const params = {
      projectId: projectId,
      pageNum: currentReportPage.value,
      pageSize: reportsPageSize
    };

    const response = await getDailyList(params);

    if (response && (response.code === 200 || response.code === 0)) {
      dailyReportsList.value = response.data || [];
      totalReports.value = response.total || 0;
    } else {
      console.error('日报列表加载失败:', response);
      ElMessage.error('加载日报列表失败');
    }
  } catch (error) {
    console.error('加载日报列表异常:', error);
    ElMessage.error('加载日报列表失败：' + (error.message || '系统错误'));
  }
};



// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 日期格式化函数
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  // 如果是完整的日期时间格式，只取日期部分
  if (dateTimeStr.includes(' ')) {
    return dateTimeStr.split(' ')[0];
  }

  // 如果是ISO格式，转换为日期
  if (dateTimeStr.includes('T')) {
    return dateTimeStr.split('T')[0];
  }

  return dateTimeStr;
};

// 格式化计划时间范围
const formatPlanTimeRange = (startTime, endTime) => {
  const formattedStart = formatDateTime(startTime);
  const formattedEnd = formatDateTime(endTime);

  if (!formattedStart && !formattedEnd) return '';
  if (!formattedEnd) return formattedStart;
  if (!formattedStart) return formattedEnd;

  // 如果开始和结束日期相同，只显示一个日期
  if (formattedStart === formattedEnd) {
    return formattedStart;
  }

  return `${formattedStart} - ${formattedEnd}`;
};

// 图片预览
const handlePreviewImage = (img) => {
  previewUrl.value = img.url || img.preview || '';
  previewVisible.value = true;
};

// 获取计划状态描述（用于前端显示）
const getPlanStatusLabel = (statusCode) => {
  // 兜底映射（根据接口文档）
  const statusMap = {
    '0': '未开始',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期',
    '4': '逾期完成',
    0: '未开始',
    1: '进行中',
    2: '已完成',
    3: '逾期',
    4: '逾期完成'
  };
  return statusMap[statusCode] || '进行中';
};

// 查看单个日报详情
const viewSingleReport = async (row) => {
  try {
    isViewMode.value = true;
    activeReportTab.value = 'workInfo'; // 重置默认显示第一个tab

    const response = await getDailyDetail(row.id);

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      const detail = response.data;

      // 处理日报图片
      const dailyImages = detail.dailyImages?.map((url, index) => ({
        name: `日报图片${index + 1}.jpg`,
        url: url,
        uid: Date.now() + index
      })) || [];

      // 处理任务进展数据
      const taskProgress = detail.planProgressList?.map(task => ({
        taskId: task.taskId,
        taskName: task.taskName || '任务',
        planTime: formatPlanTimeRange(task.planStartTime, task.planEndTime),
        responsible: task.ownerName || '',
        executorName: task.executorName || '',
        progressDesc: task.progressContent || '',
        status: getPlanStatusLabel(task.taskStatus) || '进行中',
        images: task.imageUrl ? [{
          name: '任务图片.jpg',
          url: task.imageUrl,
          uid: Date.now() + Math.random()
        }] : []
      })) || [];

      // 设置表单数据
      reportForm.value = {
        workDescription: detail.startContent || '',
        workImages: dailyImages,
        taskProgress: taskProgress.length > 0 ? taskProgress : [
          {
            taskName: '日常工作',
            planTime: formatDateTime(detail.reportDate),
            responsible: detail.reporterName || '张三',
            executorName: detail.reporterName || '张三',
            progressDesc: detail.startContent || '',
            status: '进行中',
            images: []
          }
        ],
        otherWorkContent: detail.otherContent || '',
        completionStatus: detail.completionStatus || '',
        problemsEncountered: detail.problems || '',
        solutions: detail.solutions || '',
      };
    } else {
      // 如果接口失败，使用列表数据填充
      const attachmentImages = [];
      if (row.imageCount > 0) {
        // 为每个图片创建占位符
        for (let i = 0; i < row.imageCount; i++) {
          attachmentImages.push({
            name: `图片${i + 1}.jpg`,
            url: 'https://via.placeholder.com/300x200?text=图片加载失败',
            preview: 'https://via.placeholder.com/300x200?text=图片加载失败',
            uid: Date.now() + i
          });
        }
      }

      reportForm.value = {
        workDescription: row.startContent || row.workContent || '',
        workImages: attachmentImages,
        taskProgress: [
          {
            taskName: '日常工作',
            planTime: formatDateTime(row.reportDate),
            responsible: '张三',
            executorName: '张三',
            progressDesc: row.workContent || '',
            status: '进行中',
            images: []
          }
        ],
        otherWorkContent: row.otherWorkContent || '',
        completionStatus: row.completionStatus || '',
        problemsEncountered: row.problemsEncountered || '',
        solutions: row.solutions || '',
      };
    }

    reportDetailDialogVisible.value = true;

  } catch (error) {
    console.error('查看日报详情失败:', error);
    ElMessage.error('查看日报详情失败：' + (error.message || '系统错误'));
  }
};

// 处理审核通过
const handleAuditApproved = (auditData) => {
  // 处理审核通过逻辑，例如更新项目状态或发送通知
  ElMessage.success('审核已通过');
  // 刷新项目列表
  fetchProjectList();
};

// 处理审核驳回
const handleAuditRejected = (auditData) => {
  // 处理审核驳回逻辑
  ElMessage.success('审核已驳回');
  // 刷新项目列表
  fetchProjectList();
};

// 处理日报提交
const handleReportSubmitted = (reportData) => {
  // 处理日报提交逻辑
  ElMessage.success('日报提交成功');

  // 更新对应项目的日报状态
  if (currentProject.value) {
    const projectIndex = projectList.value.findIndex(p => p.id === currentProject.value.id);
    if (projectIndex !== -1) {
      // 使用Vue的响应式更新方式，确保界面立即更新
      projectList.value[projectIndex] = {
        ...projectList.value[projectIndex],
        todayReportStatus: '已提交'
      };
    }
  }

  // 关闭弹窗
  reportDialogVisible.value = false;
};

// 页码变更事件
const handlePageChange = (page) => {
  currentPage.value = page;
  fetchProjectList();
};

// 加载审核状态枚举
const loadReviewStatusEnum = async () => {
  try {
    const response = await getReviewStatus();
    if (response && (response.code === 200 || response.code === 0) && response.data) {
      reviewStatusEnum.value = response.data;
    } else {
      // 使用默认映射
      reviewStatusEnum.value = {
        '0': '草稿',
        '1': '待审核',
        '2': '生效中',
        '3': '已驳回'
      };
    }
  } catch (error) {
    console.error('加载审核状态枚举失败:', error);
    // 使用默认映射
    reviewStatusEnum.value = {
      '0': '草稿',
      '1': '待审核',
      '2': '生效中',
      '3': '已驳回'
    };
  }
};

// 加载计划状态枚举
const loadPlanStatusEnum = async () => {
  try {
    let response;
    try {
      response = await getPlanStatus();
    } catch (apiError) {
      console.error('获取计划状态失败:', apiError);
      // 使用默认映射而不是Mock数据
      response = null;
    }

    if (response && (response.code === 200 || response.code === 0) && response.data) {
      planStatusEnum.value = response.data;
    } else {
      // 使用默认映射（根据接口文档）
      planStatusEnum.value = {
        '0': '未开始',
        '1': '进行中',
        '2': '已完成',
        '3': '逾期',
        '4': '逾期完成'
      };
    }
  } catch (error) {
    // 使用默认映射（根据接口文档）
    planStatusEnum.value = {
      '0': '未开始',
      '1': '进行中',
      '2': '已完成',
      '3': '逾期',
      '4': '逾期完成'
    };
  }
};

// 加载项目状态字典
const loadProjectStateDict = async () => {
  try {
    // 使用真实API
    const typesRes = await getDictionaryTypes();
    if (typesRes && (typesRes.code === 200 || typesRes.code === 0) && typesRes.data) {
      let typeKey = '';
      for (const key in typesRes.data) {
        if (typesRes.data[key] === '项目状态') {
          typeKey = key;
          break;
        }
      }
      if (typeKey) {
        const dictRes = await getDictionaryMap(typeKey);
        if (dictRes && (dictRes.code === 200 || dictRes.code === 0) && dictRes.data) {
          projectStateDict.value = dictRes.data;

        }
      }
    }
  } catch (e) {
    console.error('加载项目状态字典失败:', e);
    // 当API失败时，使用默认的项目状态字典，避免影响页面功能
    projectStateDict.value = {
      "xmzt01": "未开始",
      "xmzt02": "进行中",
      "xmzt03": "已完成",
      "xmzt04": "已暂停",
      "xmzt05": "已取消",
      "xmzt06": "待验收",
      "xmzt07": "项目暂停",
      "在建": "在建"
    };
    console.warn('使用默认项目状态字典');
  }
};

// 处理日报提交事件
const handleDailyReportSubmitted = (eventData) => {
  // 找到对应的项目并更新其日报状态
  if (eventData.projectId) {
    const project = projectList.value.find(p => p.id === eventData.projectId);
    if (project) {
      // 使用Vue的响应式更新方式
      const projectIndex = projectList.value.findIndex(p => p.id === eventData.projectId);
      if (projectIndex !== -1) {
        projectList.value[projectIndex] = {
          ...projectList.value[projectIndex],
          todayReportStatus: '已提交'
        };
      }
    } else {
      // 如果找不到对应项目，重新获取项目列表
      fetchProjectList();
    }
  }
};

onMounted(() => {
  // 初始化分页大小
  pageSize.value = calculateOptimalPageSize();

  loadProjectStateDict();
  loadReviewStatusEnum();
  loadPlanStatusEnum();
  fetchProjectList();

  // 监听窗口大小变化
  window.addEventListener('resize', debouncedHandleResize);

  // 监听日报提交事件
  eventBus.on(EVENTS.DAILY_REPORT_SUBMITTED, handleDailyReportSubmitted);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 清理窗口大小变化监听
  window.removeEventListener('resize', debouncedHandleResize);

  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  eventBus.off(EVENTS.DAILY_REPORT_SUBMITTED, handleDailyReportSubmitted);
});
</script>

<style scoped>
/* 我负责的项目区域 - 可滚动 */
.projects-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 改为hidden，让内部容器处理滚动 */
  min-height: 0; /* 修复flex子项的最小高度问题 */
}

.projects-section .block-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.projects-section .block-content {
  padding: 8px 16px 0 16px; /* 减少内边距 */
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.block-container {
  background-color: #ffffff;
  border-radius: 6px; /* 减少圆角 */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06); /* 减少阴影 */
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.02);
  transition: box-shadow 0.3s ease;
}

.block-container:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); /* 减少悬浮阴影 */
}

.block-header {
  padding: 8px 16px; /* 减少上下内边距 */
  font-size: 15px; /* 减少字体大小 */
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.block-content {
  padding: 8px 16px; /* 减少内边距 */
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 搜索表单边距统一 - 移除额外内边距避免双重边距 */
.search-form {
  margin-bottom: 6px; /* 减少底部间距 */
  padding: 8px 0; /* 减少上下内边距 */
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 6px; /* 减少圆角 */
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.search-form:hover {
  border-color: #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04); /* 减少阴影 */
}

/* 为搜索表单内的表单元素添加内边距 */
.search-form .el-form {
  padding: 0 16px;
}

/* 表格容器优化 - 与搜索表单对齐 */
.table-container {
  width: 100%;
  flex: none;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding: 0; /* 移除内边距，避免与block-content重复 */
  height: auto;
  min-height: auto;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  /* 确保与搜索表单左右边缘对齐 */
  margin-left: 0;
  margin-right: 0;
}

/* 分页器容器优化 - 右对齐显示 */
.pagination-container {
  margin-top: 0;
  padding: 6px 16px; /* 减少上下内边距 */
  display: flex;
  justify-content: flex-end; /* 改为右对齐 */
  align-items: center;
  height: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #f8faff 100%);
  border-top: 1px solid #f0f2f5;
  border-radius: 0 0 8px 8px;
  min-height: 32px; /* 减少最小高度 */
  box-sizing: border-box;
}

/* 分页器本身紧凑展示 */
:deep(.el-pagination) {
  padding: 0;
  height: 24px; /* 减少高度 */
  display: flex;
  align-items: center;
}

/* 修复表格底部多余空白 */
:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
  min-height: auto !important; /* 不设置最小高度 */
  height: auto !important;
}

/* 表格本身不需要最小高度，让它自适应内容 */
:deep(.el-table) {
  flex: 0 0 auto !important; /* 不要让表格伸缩，按实际内容高度显示 */
  min-height: unset !important; /* 移除最小高度限制 */
}

/* 调整表格高度，确保显示5行数据 */
:deep(.el-table) {
  flex: 1;
  min-height: 200px !important; /* 减少表格最小高度 */
}

/* 表格行高调整 - 更紧凑 */
:deep(.el-table__row) {
  height: 32px !important; /* 大幅减少行高 */
}

/* 表格内容区域高度优化 */
:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
  height: auto !important; /* 自动适应内容高度 */
  min-height: 160px; /* 减少最小高度 */
}

/* 移除重复的分页器样式定义 */

/* 强制移除表格纵向滚动条 */
:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
}

:deep(.el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 修改项目进度条的显示方式，确保居中和比例合适 */
.progress-bar {
  width: 85%;
  height: 14px;
  background-color: #e9ecef;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  transition: all 0.3s ease;
  cursor: pointer;
}

.progress-bar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #409EFF 0%, #66b3ff 100%);
  border-radius: 6px;
  transition: all 0.6s ease;
  position: relative;
}

.progress-inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.progress-bar:hover .progress-inner::after {
  left: 100%;
}

.progress-text {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;
  z-index: 1;
}

.nowrap-cell {
  white-space: nowrap;
}

/* 表格防止换行相关样式 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__header-wrapper th.el-table__cell) {
  white-space: nowrap;
  background-color: #f5f7fa;
  padding: 4px 0; /* 减少表头内边距 */
  height: 32px; /* 减少表头高度 */
  font-weight: 500;
  overflow: hidden;
}

:deep(.el-table__header-wrapper th.el-table__cell .cell) {
  white-space: nowrap;
  padding: 0 4px;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-table--border .el-table__cell) {
  padding: 0 !important; /* 完全移除单元格内边距 */
}

:deep(.el-table td.el-table__cell) {
  white-space: nowrap;
}

/* 统一表单和按钮样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
  transform: translateY(-1px);
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #5B7BFA inset !important;
  transform: translateY(-1px);
}

:deep(.el-button) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 项目名称链接样式 */
.project-name-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.project-name-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 移除了表格统计信息样式，因为不再显示统计信息 */

/* 分页相关样式 */
:deep(.el-pagination) {
  font-size: 12px; /* 减小字体 */
  padding: 0;
  height: auto;
  line-height: normal;
  display: flex;
  align-items: center;
  gap: 4px; /* 减小间距 */
}

:deep(.el-pagination .el-pagination__total) {
  font-weight: 500;
  margin-right: 8px; /* 减小右边距 */
  color: #606266;
  font-size: 12px; /* 减小字体 */
}

:deep(.el-pagination button) {
  min-width: 24px; /* 进一步减小按钮尺寸 */
  height: 24px; /* 进一步减小高度 */
  border-radius: 3px; /* 减小圆角 */
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  font-size: 11px; /* 减小字体 */
}

:deep(.el-pagination button:hover:not(:disabled)) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
}

:deep(.el-pagination button:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px; /* 进一步减小按钮尺寸 */
  height: 24px; /* 进一步减小高度 */
  line-height: 22px; /* 调整行高 */
  font-size: 11px; /* 进一步减小字体 */
  font-weight: 500;
  border-radius: 3px; /* 减小圆角 */
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  color: #606266;
  margin: 0 1px; /* 减小间距 */
}

:deep(.el-pagination .el-pager li:hover) {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-color: #5B7BFA;
  color: #5B7BFA;
  transform: translateY(-1px);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #5B7BFA 0%, #7b9bff 100%);
  border-color: #5B7BFA;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.3);
  transform: translateY(-1px);
}

/* 统一表格样式 */
:deep(.el-table) {
  border-radius: 8px 8px 0 0; /* 只保留顶部圆角 */
  overflow: hidden;
  border-bottom: none; /* 移除表格底部边框 */
  margin: 0; /* 确保表格没有外边距 */
  width: 100% !important; /* 确保表格占满容器宽度 */
}

/* 移除表格最后一行的底部边框 */
:deep(.el-table__body tr:last-child td) {
  border-bottom: none !important;
}

/* 确保表格与分页器无缝连接 */
:deep(.el-table--border) {
  border-bottom: none;
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border::after) {
  display: none;
}

/* 表格外边框优化 */
:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell:last-child) {
  border-right: none;
}

/* 统一其他组件样式 */
:deep(.el-card),
:deep(.el-dialog),
:deep(.el-dropdown-menu),
:deep(.el-menu),
:deep(.el-pagination),
:deep(.el-popover) {
  border-radius: 8px;
}

/* 表格单元格内容样式重置 */
:deep(.el-table .cell) {
  padding: 0 4px !important;
  box-sizing: border-box;
  font-size: 13px !important; /* 减少字体大小 */
  line-height: 28px !important; /* 调整行高以适应新的表格行高 */
}

/* 左对齐列的特殊处理 */
:deep(.el-table .column-left .cell) {
  display: block; /* 左对齐需要使用block布局 */
  text-align: left;
}

/* 表头文字居中 */
:deep(.el-table th>.cell) {
  text-align: center !important;
}

/* 表格单元格内容居中 */
:deep(.el-table td:not(.column-left)) {
  text-align: center;
}

/* 链接按钮居中布局 */
:deep(.el-table .el-button--link) {
  margin: 0 4px;
}

/* 操作列按钮容器 */
:deep(.el-table__fixed-right .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 强制所有表格内容垂直居中 */
:deep(.el-table td.el-table__cell) {
  vertical-align: middle;
}

/* Tag标签居中 */
:deep(.el-tag) {
  display: inline-block;
}

/* 操作按钮区域优化 */
:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 修复表格内容对齐 */
:deep(.nowrap-cell) {
  display: inline-block;
  text-align: center;
  width: 100%;
}

/* 表头样式增强 */
:deep(.el-table__header th) {
  background: linear-gradient(135deg, #f8faff 0%, #f5f7fa 100%);
  color: #606266;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

/* 表头悬浮效果 */
:deep(.el-table__header th:hover) {
  background: linear-gradient(135deg, #f0f4ff 0%, #eef2f8 100%);
  color: #5B7BFA;
}

/* 表格行高度统一 - 更紧凑版 */
:deep(.el-table__row) {
  height: 32px !important; /* 进一步减少行高 */
}

/* 单元格内部的操作按钮紧凑排列 */
:deep(.el-table__cell .el-button) {
  padding: 1px 3px !important; /* 减少按钮内边距 */
  height: 20px !important; /* 减少按钮高度 */
  line-height: 1 !important;
  font-size: 12px !important; /* 减少字体大小 */
}

/* 表头排序图标样式优化 */
:deep(.el-table .sort-caret.ascending) {
  border-bottom-color: #909399;
  transition: all 0.3s ease;
}

:deep(.el-table .sort-caret.descending) {
  border-top-color: #909399;
  transition: all 0.3s ease;
}

:deep(.el-table .ascending .sort-caret.ascending) {
  border-bottom-color: #5B7BFA;
  transform: scale(1.2);
}

:deep(.el-table .descending .sort-caret.descending) {
  border-top-color: #5B7BFA;
  transform: scale(1.2);
}

:deep(.el-table th.is-sortable) {
  cursor: pointer;
  transition: all 0.3s ease;
}

:deep(.el-table th.is-sortable:hover) {
  background: linear-gradient(135deg, #f0f4ff 0%, #eef2f8 100%);
}

:deep(.el-table th.is-sortable:hover .cell) {
  color: #5B7BFA;
  transform: translateY(-1px);
}

:deep(.el-table th.is-sortable.ascending .cell),
:deep(.el-table th.is-sortable.descending .cell) {
  color: #5B7BFA;
  font-weight: 600;
}

/* 表格整体样式增强 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f8faff;
  transition: all 0.3s ease;
}

/* 表格行悬浮效果 */
:deep(.el-table__body tr:hover) {
  background-color: #f8faff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(91, 123, 250, 0.08);
  transition: all 0.3s ease;
}

/* 表格行悬浮时的边框效果 */
:deep(.el-table__body tr:hover td) {
  border-color: rgba(91, 123, 250, 0.2) !important;
}

/* 全面确保垂直居中 - 更紧凑版 */
:deep(.el-table .el-table__cell) {
  padding-top: 2px !important; /* 进一步减少内边距 */
  padding-bottom: 2px !important;
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--el-table-border-color);
}

/* 表格表头样式 */
:deep(.el-table__header) {
  margin-bottom: 0 !important;
}

:deep(.el-table__header th.el-table__cell) {
  background-color: #f5f7fa;
  color: #606266;
  height: 32px !important; /* 进一步减少表头高度 */
  padding: 2px 0; /* 进一步减少内边距 */
  font-weight: 500;
}

:deep(.el-table__header th.el-table__cell .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px; /* 进一步减少高度 */
  line-height: 20px;
  padding: 0 4px; /* 减少内边距 */
  white-space: nowrap;
  overflow: hidden;
  font-size: 13px; /* 减少字体大小 */
}

/* 表格内容样式 */
:deep(.el-table__body td.el-table__cell) {
  padding: 1px 0; /* 进一步减少内边距 */
  font-size: 13px; /* 减少字体大小 */
}

/* 调整按钮垂直居中 */
:deep(.el-button--link) {
  height: auto;
  padding: 4px 8px;
  transition: all 0.3s ease;
  border-radius: 4px;
}

/* 操作按钮悬浮效果 */
:deep(.el-button--link:hover) {
  background-color: rgba(91, 123, 250, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(91, 123, 250, 0.2);
}

:deep(.el-button--link:active) {
  transform: translateY(0);
}

/* 日报状态样式 - 简洁风格，与整体页面协调 */
.report-status-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.report-status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  border: 1px solid;
  min-width: 64px;
  transition: all 0.2s ease;
  cursor: default;
  background-color: #ffffff;
}

.report-status-badge:hover {
  transform: none;
  box-shadow: none;
}

/* 已提交状态 - 简洁绿色 */
.status-submitted {
  color: #52c41a;
  border-color: #b7eb8f;
  background-color: #f6ffed;
}

/* 未提交状态 - 简洁橙色 */
.status-pending {
  color: #fa8c16;
  border-color: #ffd591;
  background-color: #fff7e6;
}

.status-icon {
  margin-right: 4px;
  font-size: 12px;
  font-style: normal;
  display: inline-block;
  width: 12px;
  height: 12px;
  line-height: 12px;
  text-align: center;
  font-weight: normal;
}

.icon-check::before {
  content: '✓';
  color: #52c41a;
  font-weight: bold;
}

.icon-clock::before {
  content: '○';
  color: #fa8c16;
  font-size: 10px;
}

/* 确保状态标签在表格单元格中垂直居中 */
:deep(.el-table__body td.el-table__cell .report-status-wrapper) {
  height: 18px; /* 减少高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 优化状态标签样式 */
:deep(.el-tag) {
  height: 18px !important; /* 减少标签高度 */
  line-height: 16px !important;
  font-size: 11px !important; /* 减少字体大小 */
  padding: 0 6px !important; /* 减少内边距 */
}



/* 日报详情抽屉样式 */
.report-drawer {
  /* 覆盖 Element Plus 抽屉默认样式 */
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0 !important;
  }

  .custom-drawer-header {
    display: flex;
    align-items: center;
    padding: 16px 24px 16px 8px !important;
    gap: 6px;

    .drawer-close-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
      color: #909399;
      transition: all 0.2s;
      flex-shrink: 0;

      &:hover {
        color: #606266;
        background-color: #f5f7fa;
      }

      .el-icon {
        font-size: 16px;
      }
    }

    .drawer-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      flex: 1;

      .drawer-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  /* 抽屉内容区域样式 */
  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .report-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0 24px;

    .drawer-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;

      :deep(.el-tabs__header) {
        margin: 0;
        border-bottom: 1px solid #e4e7ed;
        background-color: #fafafa;
      }

      :deep(.el-tabs__content) {
        flex: 1;
        overflow-y: auto;
      }
    }

    .tab-content {
      padding: 20px 0;

      &.work-info-content {
        .description-input {
          .el-textarea__inner {
            min-height: 80px;
          }
        }

        .upload-area {
          .image-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .image-thumb-box {
              position: relative;
              width: 120px;
              height: 120px;
              border: 1px solid #dcdfe6;
              border-radius: 6px;
              overflow: hidden;

              .image-thumb {
                width: 100%;
                height: 100%;
                object-fit: cover;
                cursor: pointer;

                &:hover {
                  opacity: 0.8;
                }
              }

              .image-thumb-empty {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                color: #909399;
                font-size: 12px;
              }

              .file-name-thumb {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                font-size: 12px;
                padding: 4px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }

          .no-images-tip {
            padding: 20px;
            text-align: center;
            border: 1px dashed #dcdfe6;
            border-radius: 6px;
          }
        }
      }

      &.other-work-content {
        .compact-form {
          .el-form-item {
            margin-bottom: 18px;
          }
        }
      }
    }

    /* 日报弹窗中的表格样式 - 紧凑型 */
    :deep(.el-table) {
      border-radius: 0;
      width: 100% !important;
      table-layout: fixed;
      --el-table-border-color: #e6e6e6;
      --el-table-header-bg-color: #f2f6fc;
      --el-table-row-hover-bg-color: #f5f7fa;
      font-size: 13px;
      color: #333;
    }

    /* 表头样式 */
    :deep(.el-table__header-wrapper th) {
      background-color: #f2f6fc;
      color: #333;
      font-weight: 500;
      height: 40px;
      padding: 8px 0;
      border-bottom: 1px solid #e0e3e9;
    }

    :deep(.el-table__header-wrapper th.el-table__cell) {
      background-color: #f2f6fc;
    }

    :deep(.el-table__header-wrapper th.el-table__cell .cell) {
      font-weight: 500;
      color: #333;
      padding: 0 4px;
    }

    /* 行样式 - 紧凑型 */
    :deep(.el-table__row) {
      height: 32px !important;
    }

    /* 确保所有行高度一致 */
    :deep(.el-table__cell) {
      padding: 0 !important;
      height: 32px !important;
      line-height: 32px !important;
    }

    /* 表格单元格内容样式 */
    :deep(.el-table .cell) {
      padding: 0 4px !important;
      line-height: 32px !important;
    }

    .el-table {
      .progress-input {
        .el-input {
          .el-input__inner {
            border: none;
            background: transparent;

            &:focus {
              border: 1px solid #409EFF;
              background: white;
            }
          }
        }
      }

      .task-image-container {
        .el-button {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .dialog-footer {
    text-align: center;
    padding: 20px 0 10px;
  }
}

/* 统一的日报布局样式 - 与填写日报保持一致 */
.section-layout {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: start;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  grid-column: 1 / -1;
  margin-bottom: 12px;
}

.section-icon {
  color: #409EFF;
  font-size: 16px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1;
  display: flex;
  align-items: center;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
  line-height: 16px;
}

.section-content {
  grid-column: 1 / -1;
  margin-left: 0;
}

/* 表单内容样式重置 */
.section-content .description-input,
.section-content .upload-area-drawer,
.section-content .upload-tip {
  margin-left: 0;
  padding-left: 0;
}

/* 抽屉样式的上传区域 */
.upload-area-drawer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 水平布局容器 */
.upload-container-horizontal {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
}

/* 水平布局的图片列表 */
.image-list-horizontal {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 水平布局的图片项 */
.image-item-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* 图片容器 */
.image-container {
  position: relative;
  width: 120px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片预览 */
.image-preview-drawer {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-preview-drawer:hover {
  transform: scale(1.05);
}

/* 空图片预览 */
.image-preview-empty {
  color: #909399;
  font-size: 12px;
  text-align: center;
}

/* 表单区域间距 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 待审核按钮样式 */
.pending-audit-btn {
  color: #E6A23C !important;
  font-weight: 500;
}

.pending-audit-btn:hover {
  color: #CF9236 !important;
}

/* 为待审核按钮添加轻微的背景色 */
.pending-audit-btn.is-link {
  background-color: rgba(230, 162, 60, 0.1);
  border-radius: 4px;
  padding: 4px 8px;
}

.pending-audit-btn.is-link:hover {
  background-color: rgba(230, 162, 60, 0.15);
}
</style>