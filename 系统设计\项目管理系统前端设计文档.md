# 交付部项目管理系统 - 前端设计文档

## 1. 系统概述

交付部项目管理系统是一个基于Vue3、JavaScript和Element Plus的现代化Web应用，旨在提供高效、直观的项目管理体验。系统具备项目信息管理、进度跟踪、任务分配、人员管理等功能，为项目经理和团队成员提供全方位的项目管理支持。

## 2. 技术栈

- **前端框架**: Vue 3
- **编程语言**: JavaScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **CSS预处理器**: Sass/SCSS
- **图表库**: ECharts
- **代码规范**: ESLint + Prettier

## 3. 系统架构

### 3.1 目录结构

```
src/
├── assets/                # 静态资源
│   ├── icons/            # 图标资源
│   ├── images/           # 图片资源
│   └── styles/           # 全局样式
├── components/           # 公共组件
│   ├── common/           # 通用基础组件
│   ├── layout/           # 布局组件
│   └── business/         # 业务组件
├── composables/          # 组合式API
├── config/               # 配置文件
├── directives/           # 自定义指令
├── hooks/                # 自定义Hook
├── router/               # 路由配置
├── services/             # API服务
├── store/                # 状态管理
├── utils/                # 工具函数
├── views/                # 页面视图
│   ├── dashboard/        # 工作台
│   ├── project/          # 项目管理
│   ├── user/             # 用户管理
│   └── system/           # 系统设置
├── App.vue               # 根组件
└── main.js               # 入口文件
```

### 3.2 核心模块

- **认证模块**: 负责用户登录、权限管理
- **项目管理模块**: 项目增删改查、项目基本信息、工作项管理
- **用户管理模块**: 用户和角色管理
- **工作台模块**: 项目概览、数据统计、快捷操作
- **通知模块**: 消息提醒、系统通知

### 3.3 状态管理

使用Pinia进行状态管理，按功能模块划分store:

- `userStore`: 用户信息、权限
- `projectStore`: 项目列表、当前项目信息
- `appStore`: 应用级配置、主题、布局状态

## 4. UI设计系统

### 4.1 色彩系统

#### 主色调
- **主功能色**: #2A6AF2（蓝色）
  - 深色变体: #0033CC
  - 亮色变体: #40A9FF, #1890FF, #096DD9等

#### 文本颜色
- **标题/主要文本**: #1B2A4B
- **辅助文本**: #6C7A89

#### 背景色
- **主背景色**: #F0F2F5
- **卡片/模块背景色**: #FFFFFF

#### 状态颜色
- **成功**: #52C41A
- **警告**: #FAAD14
- **错误**: #F5222D

### 4.2 图标系统

遵循统一的图标风格，包括面性图标和线性图标，强调圆润、平衡和精致。

- **面性图标**: 用于强调、表示数量或状态
- **线性图标**: 用于普通操作、导航或作为辅助说明
- **设计规范**:
  - 统一描边粗细（2px）
  - 一致的圆角处理
  - 标准化尺寸（16px、20px、24px）
  - 按上下文使用文本色或功能色

### 4.3 布局系统

- **全局布局**: 顶部全局导航 + 左侧菜单 + 右侧内容区
- **内容区域布局**:
  - 标签页导航
  - 二级页面导航（项目详情页）
  - 卡片式布局
  - 表格布局
  - 表单布局

### 4.4 组件样式

使用Element Plus组件库，并进行定制化样式覆盖，以符合设计规范。主要组件包括：

- **按钮**: 主要/次要/文本/危险按钮
- **表单元素**: 输入框、下拉框、日期选择器等
- **表格**: 数据表格、可操作表格
- **卡片**: 数据卡片、信息卡片
- **导航**: 菜单、标签页、面包屑
- **通知**: 消息提示、警告框、模态框

## 5. 页面组件结构

### 5.1 布局组件

#### AppLayout.vue
主应用布局，包含以下子组件:
- `HeaderComponent`: 全局头部
- `SidebarMenu`: 左侧导航栏
- `TabsNav`: 顶部标签导航
- `MainContent`: 内容区容器

### 5.2 页面组件

#### 工作台 (Dashboard)
- `DashboardView.vue`: 工作台主视图
  - `StatisticCards.vue`: 项目数据统计卡片
  - `ProjectFilterForm.vue`: 项目筛选表单
  - `ResponsibleProjectTable.vue`: 负责项目列表表格
  
#### 项目管理
- `ProjectListView.vue`: 全部项目列表
  - `ProjectSearchForm.vue`: 项目搜索筛选组件
  - `ProjectTable.vue`: 项目列表表格
  - `ProjectPagination.vue`: 分页组件
  
- `ProjectDetailView.vue`: 项目详情
  - `ProjectTabsNav.vue`: 项目二级导航
  - `ProjectBasicInfo.vue`: 项目基本信息
  - `ProjectWorkItems.vue`: 项目工作项
  - `ProjectStakeholders.vue`: 项目干系人
  - `ProjectSchedule.vue`: 项目日程

#### 用户管理
- `UserManagementView.vue`: 用户管理
  - `UserTable.vue`: 用户列表表格
  - `UserForm.vue`: 用户表单
  
- `RoleManagementView.vue`: 角色管理
  - `RoleTable.vue`: 角色列表表格
  - `RoleForm.vue`: 角色表单
  - `PermissionTree.vue`: 权限配置树

### 5.3 通用组件

- `SearchForm.vue`: 通用搜索表单
- `DataTable.vue`: 通用数据表格
- `StatusTag.vue`: 状态标签
- `ProgressBar.vue`: 进度条
- `ActionButtons.vue`: 操作按钮组
- `DateRangePicker.vue`: 日期范围选择器
- `FileUploader.vue`: 文件上传组件
- `EmptyState.vue`: 空状态提示

## 6. 交互设计

### 6.1 导航交互

- 左侧菜单支持折叠展开
- 顶部标签支持多标签切换、关闭、右键菜单操作
- 面包屑导航支持路径回溯

### 6.2 表单交互

- 即时表单验证
- 提交前整体验证
- 表单联动（条件字段显示/隐藏）
- 自动保存草稿

### 6.3 表格交互

- 排序、筛选
- 分页加载
- 行选择、批量操作
- 行内编辑

### 6.4 数据可视化

- 项目进度展示
- 数据统计图表
- 状态变化动效

## 7. 响应式设计

系统采用响应式设计，支持在不同屏幕尺寸下的良好显示:

- **桌面端** (>=1200px): 完整布局
- **笔记本** (>=992px): 适度简化布局
- **平板** (>=768px): 折叠侧边栏，优化表格显示
- **移动端** (<768px): 隐藏次要元素，重排布局

## 8. 性能优化

- 路由懒加载
- 组件异步加载
- 虚拟滚动（大数据列表）
- 图片懒加载
- Tree-shaking
- 合理的缓存策略

## 9. 可访问性设计

- 符合WCAG 2.1标准
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式

## 10. 实现路径

### 第一阶段: 基础架构搭建
- 项目初始化
- 路由配置
- 状态管理
- 基础组件开发

### 第二阶段: 核心页面实现
- 工作台
- 项目列表
- 项目详情
- 用户管理

### 第三阶段: 功能完善与优化
- 交互细节完善
- 性能优化
- 单元测试
- 集成测试

### 第四阶段: 部署与发布
- 生产构建优化
- 部署配置
- 灰度发布
- 性能监控 