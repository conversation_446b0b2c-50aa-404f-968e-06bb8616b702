import request from '@/utils/request'

// 分页查询日报
export function getDailyList(params) {
  return request({
    url: '/project/daily/list',
    method: 'get',
    params
  })
}

// 查询日报详情
export function getDailyDetail(id, projectId) {
  const params = {}

  if (id) {
    // 传入ID：获取指定日报的详情
    params.id = id
  }

  if (projectId) {
    // 传入projectId：项目ID（当ID为空时必填）
    params.projectId = projectId
  }

  return request({
    url: `/project/daily/detail`,
    method: 'get',
    params
  })
}

// Mock数据 - 查询日报详情
export function getDailyDetailMock(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      if (!id) {
        // 不传ID：返回今日计划进展列表
        const today = new Date().toISOString().split('T')[0];
        const mockTodayProgress = {
          projectId: '5F60313F5CF725146898A972',
          reportDate: today,
          planProgressList: [
            {
              taskId: 1,
              taskName: '用户需求分析',
              taskStatus: 'IN_PROGRESS',
              planStartTime: today + ' 09:00:00',
              planEndTime: today + ' 12:00:00',
              ownerName: '张三',
              progressContent: '',
              imageUrl: ''
            },
            {
              taskId: 2,
              taskName: '系统架构设计',
              taskStatus: 'NOT_STARTED',
              planStartTime: today + ' 13:00:00',
              planEndTime: today + ' 18:00:00',
              ownerName: '李四',
              progressContent: '',
              imageUrl: ''
            },
            {
              taskId: 3,
              taskName: '数据库设计',
              taskStatus: 'IN_PROGRESS',
              planStartTime: today + ' 14:00:00',
              planEndTime: today + ' 17:00:00',
              ownerName: '王五',
              progressContent: '',
              imageUrl: ''
            }
          ]
        };

        resolve({
          code: 200,
          msg: '获取成功',
          data: mockTodayProgress
        });
        return;
      }

      // 传入ID：返回完整日报详情
      const mockDetail = {
        id: parseInt(id),
        projectId: '5F60313F5CF725146898A972',
        reportDate: '2024-01-15',
        reporterName: '张三',
        startContent: '今日开始进行系统开发工作，主要完成用户需求分析和系统架构设计',
        otherContent: '完成了系统架构设计评审，与产品经理讨论了功能需求',
        completionStatus: '按计划进行，完成度80%',
        problems: '技术选型需要进一步确认，部分第三方库的兼容性存在问题',
        solutions: '组织技术评审会议，邀请架构师参与讨论，制定技术选型标准',
        dailyImages: [
          'https://via.placeholder.com/400x300?text=日报图片1',
          'https://via.placeholder.com/400x300?text=日报图片2'
        ],
        planProgressList: [
          {
            taskId: 1,
            taskName: '用户需求分析',
            taskStatus: 'IN_PROGRESS',
            planStartTime: '2024-01-15 09:00:00',
            planEndTime: '2024-01-15 12:00:00',
            ownerName: '张三',
            progressContent: '完成了用户访谈，整理了需求文档初稿',
            imageName: '需求分析图.jpg',
            imageUrl: 'https://via.placeholder.com/400x300?text=需求分析图'
          },
          {
            taskId: 2,
            taskName: '系统架构设计',
            taskStatus: 'IN_PROGRESS',
            planStartTime: '2024-01-15 14:00:00',
            planEndTime: '2024-01-15 18:00:00',
            ownerName: '李四',
            progressContent: '设计了系统整体架构，完成了技术选型调研',
            imageName: '架构图.jpg',
            imageUrl: 'https://via.placeholder.com/400x300?text=系统架构图'
          }
        ],
        createdTime: '2024-01-15 18:30:00'
      };

      resolve({
        code: 200,
        msg: '查询成功',
        data: mockDetail
      });
    }, 500);
  });
}

// 保存日报（新增或编辑）
export function saveDaily(data) {
  return request({
    url: '/project/daily/save',
    method: 'post',
    data
  })
}

// Mock数据 - 保存日报
export function saveDailyMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '保存成功',
        data: {
          id: Date.now(), // 模拟生成的日报ID
          ...data
        }
      });
    }, 1000);
  });
}

// Mock数据 - 用于开发测试
export function getDailyListMock(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const { pageNum = 1, pageSize = 10, projectId, reportDate, startDate, endDate } = params;
      
      const mockData = [
        {
          id: 1,
          projectId: projectId || '604205B15CF72543F4A123F3',
          reportDate: '2024-01-15',
          otherWorkContent: '完成了系统架构设计评审',
          workContent: '1. 完成用户需求分析\n2. 设计系统架构\n3. 编写技术文档',
          completionStatus: '按计划进行',
          problemsEncountered: '技术选型需要进一步确认',
          solutions: '组织技术评审会议',
          imageCount: 2,
          createdTime: '2024-01-15 18:30:00'
        },
        {
          id: 2,
          projectId: projectId || '604205B15CF72543F4A123F3',
          reportDate: '2024-01-14',
          otherWorkContent: '完成了数据库设计',
          workContent: '1. 设计数据库表结构\n2. 编写SQL脚本\n3. 进行性能测试',
          completionStatus: '超前完成',
          problemsEncountered: '无',
          solutions: '无',
          imageCount: 1,
          createdTime: '2024-01-14 17:45:00'
        },
        {
          id: 3,
          projectId: projectId || '604205B15CF72543F4A123F3',
          reportDate: '2024-01-13',
          otherWorkContent: '完成了前端页面开发',
          workContent: '1. 开发用户登录页面\n2. 开发主界面\n3. 进行界面优化',
          completionStatus: '按计划进行',
          problemsEncountered: '部分浏览器兼容性问题',
          solutions: '使用CSS前缀解决兼容性问题',
          imageCount: 3,
          createdTime: '2024-01-13 19:15:00'
        }
      ];

      // 模拟筛选
      let filteredData = mockData;
      if (reportDate) {
        filteredData = filteredData.filter(item => item.reportDate === reportDate);
      }
      if (startDate && endDate) {
        filteredData = filteredData.filter(item => 
          item.reportDate >= startDate && item.reportDate <= endDate
        );
      }

      // 模拟分页
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const rows = filteredData.slice(start, end);

      resolve({
        code: 200,
        data: rows,
        total: filteredData.length,
        message: 'success'
      });
    }, 300);
  });
}

