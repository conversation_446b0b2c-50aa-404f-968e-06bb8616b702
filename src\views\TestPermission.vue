<template>
  <div class="test-permission">
    <el-card class="permission-card">
      <template #header>
        <div class="card-header">
          <span>菜单权限测试</span>
        </div>
      </template>
      
      <div class="permission-info">
        <h3>用户信息</h3>
        <p><strong>用户ID:</strong> {{ userStore.userInfo.userId || '未登录' }}</p>
        <p><strong>用户名:</strong> {{ userStore.userInfo.realName || '未登录' }}</p>
        <p><strong>角色ID:</strong> {{ userStore.userRole || '无角色' }}</p>
        <p><strong>角色数组:</strong> {{ JSON.stringify(userStore.userRoles) }}</p>
        <p><strong>完整用户信息:</strong></p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">{{ JSON.stringify(userStore.userInfo, null, 2) }}</pre>
        
        <h3>权限信息</h3>
        <p><strong>菜单树加载状态:</strong> {{ permissionStore.loading ? '加载中' : '已加载' }}</p>
        <p><strong>菜单树数量:</strong> {{ permissionStore.menuTree.length }}</p>
        <p><strong>用户菜单权限ID:</strong> {{ permissionStore.userMenuIds.join(', ') || '无权限' }}</p>
        
        <h3>菜单权限测试</h3>
        <div class="permission-tests">
          <div class="test-item">
            <span>工作台 (/workbench):</span>
            <el-tag :type="hasWorkbenchPermission ? 'success' : 'danger'">
              {{ hasWorkbenchPermission ? '有权限' : '无权限' }}
            </el-tag>
          </div>
          
          <div class="test-item">
            <span>项目管理 (/projects):</span>
            <el-tag :type="hasProjectPermission ? 'success' : 'danger'">
              {{ hasProjectPermission ? '有权限' : '无权限' }}
            </el-tag>
          </div>
          
          <div class="test-item">
            <span>全部项目 (/projects/all):</span>
            <el-tag :type="hasProjectAllPermission ? 'success' : 'danger'">
              {{ hasProjectAllPermission ? '有权限' : '无权限' }}
            </el-tag>
          </div>
          
          <div class="test-item">
            <span>用户管理 (/users):</span>
            <el-tag :type="hasUserPermission ? 'success' : 'danger'">
              {{ hasUserPermission ? '有权限' : '无权限' }}
            </el-tag>
          </div>
        </div>
        
        <h3>菜单树结构</h3>
        <el-tree
          :data="permissionStore.menuTree"
          :props="{ children: 'children', label: 'menuName' }"
          node-key="id"
          default-expand-all
          class="menu-tree">
          <template #default="{ node, data }">
            <span class="tree-node">
              <span>{{ data.menuName }} (ID: {{ data.id }})</span>
              <span class="tree-path">{{ data.path }}</span>
              <el-tag
                size="small"
                :type="permissionStore.hasMenuPermission(data.id) ? 'success' : 'info'">
                {{ permissionStore.hasMenuPermission(data.id) ? '有权限' : '无权限' }}
              </el-tag>
            </span>
          </template>
        </el-tree>
        
        <div class="actions">
          <el-button @click="refreshPermissions" :loading="permissionStore.loading">
            刷新权限数据
          </el-button>
          <el-button @click="testNavigation">
            测试导航权限
          </el-button>
          <el-button @click="checkApiStatus" type="info">
            检查API状态
          </el-button>
          <el-button @click="fixRoleId" type="warning" v-if="!userStore.userRole && userStore.userRoles.length > 0">
            修复角色ID
          </el-button>
          <el-button @click="testUserMeApi" type="success">
            测试用户信息API
          </el-button>
          <el-button @click="useCurrentPermissions" type="primary">
            使用当前权限数据
          </el-button>
          <el-button @click="testMenuTreeApi" type="info">
            单独测试菜单树API
          </el-button>
          <el-button @click="debugPermissions" type="warning">
            调试权限数据
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 权限检查
const hasWorkbenchPermission = computed(() => permissionStore.hasPathPermission('/workbench'))
const hasProjectPermission = computed(() => permissionStore.hasPathPermission('/projects'))
const hasProjectAllPermission = computed(() => permissionStore.hasPathPermission('/projects/all'))
const hasUserPermission = computed(() => permissionStore.hasPathPermission('/users'))

// 刷新权限数据
const refreshPermissions = async () => {
  if (!userStore.userRole) {
    ElMessage.warning('用户角色信息不存在')
    return
  }
  
  try {
    await permissionStore.initPermissions(userStore.userRole)
    ElMessage.success('权限数据刷新成功')
  } catch (error) {
    ElMessage.error('权限数据刷新失败: ' + error.message)
  }
}

// 测试导航权限
const testNavigation = () => {
  const testPaths = ['/workbench', '/projects', '/projects/all', '/users']

  testPaths.forEach(path => {
    const hasPermission = permissionStore.hasPathPermission(path)
    console.log(`路径 ${path} 权限检查结果:`, hasPermission)
  })

  ElMessage.info('权限检查结果已输出到控制台')
}

// 检查API状态
const checkApiStatus = async () => {
  console.log('=== API状态检查 ===')
  console.log('用户信息:', userStore.userInfo)
  console.log('用户角色ID:', userStore.userRole)
  console.log('用户Token:', userStore.token ? '已设置' : '未设置')

  if (!userStore.userRole) {
    ElMessage.warning('用户角色ID为空，无法获取权限数据')
    return
  }

  try {
    // 测试菜单树API
    console.log('正在测试菜单树API...')
    const { getMenuTree } = await import('@/api/user')
    const menuResponse = await getMenuTree()
    console.log('菜单树API响应:', menuResponse)

    // 测试角色权限API
    console.log('正在测试角色权限API...')
    const { getMenuIdsByRole } = await import('@/api/user')
    const permissionResponse = await getMenuIdsByRole(userStore.userRole)
    console.log('角色权限API响应:', permissionResponse)

    ElMessage.success('API状态检查完成，请查看控制台输出')
  } catch (error) {
    console.error('API调用失败:', error)
    ElMessage.error('API调用失败: ' + error.message)
  }
}

// 修复角色ID
const fixRoleId = () => {
  if (userStore.userRoles.length > 0) {
    const roleId = userStore.userRoles[0]
    const updatedUserInfo = {
      ...userStore.userInfo,
      roleId: roleId
    }

    console.log('修复角色ID:', roleId)
    console.log('更新后的用户信息:', updatedUserInfo)

    userStore.setUserInfo(updatedUserInfo)
    ElMessage.success(`角色ID已修复为: ${roleId}`)
  }
}

// 测试用户信息API
const testUserMeApi = async () => {
  try {
    console.log('=== 测试 /api/users/me 接口 ===')
    const { getCurrentUser } = await import('@/api/user')
    const response = await getCurrentUser()

    console.log('用户信息API完整响应:', response)
    console.log('用户信息数据:', response.data)

    if (response.data) {
      console.log('用户ID:', response.data.userId)
      console.log('用户名:', response.data.username)
      console.log('真实姓名:', response.data.realName)
      console.log('角色数组:', response.data.roles)
      console.log('角色ID字段:', response.data.roleId)
      console.log('权限数组:', response.data.permissions)

      // 如果API返回了roleId，更新用户信息
      if (response.data.roleId) {
        const updatedUserInfo = {
          ...userStore.userInfo,
          roleId: response.data.roleId
        }
        userStore.setUserInfo(updatedUserInfo)
        ElMessage.success('从API获取到roleId并已更新')
      }
    }

    ElMessage.info('用户信息API测试完成，请查看控制台')
  } catch (error) {
    console.error('用户信息API调用失败:', error)
    ElMessage.error('API调用失败: ' + error.message)
  }
}

// 使用当前权限数据
const useCurrentPermissions = async () => {
  console.log('=== 使用当前用户权限数据 ===')
  console.log('当前用户信息:', userStore.userInfo)
  console.log('用户权限字段:', userStore.userInfo.permissions)

  // 修复roleId
  if (!userStore.userRole && userStore.userRoles.length > 0) {
    const updatedUserInfo = {
      ...userStore.userInfo,
      roleId: userStore.userRoles[0]
    }
    userStore.setUserInfo(updatedUserInfo)
    console.log('已修复roleId:', userStore.userRoles[0])
  }

  // 直接使用用户信息中的permissions
  if (userStore.userInfo.permissions && Array.isArray(userStore.userInfo.permissions)) {
    console.log('用户权限数据:', userStore.userInfo.permissions)

    // 直接设置权限数据
    console.log('准备设置权限数据:', userStore.userInfo.permissions)
    console.log('设置前Store中的权限:', permissionStore.userMenuIds)

    permissionStore.setUserMenuPermissions(userStore.userInfo.permissions)

    console.log('权限设置后，Store中的权限:', permissionStore.userMenuIds)
    console.log('权限设置后，权限数组长度:', permissionStore.userMenuIds.length)

    // 等待一下再检查
    setTimeout(() => {
      console.log('延迟检查权限数据:', permissionStore.userMenuIds)
    }, 100)

    // 获取菜单树
    await permissionStore.fetchMenuTree()

    ElMessage.success('权限数据已更新！')
  } else {
    console.log('用户权限数据不存在或格式错误')
    console.log('permissions字段类型:', typeof userStore.userInfo.permissions)
    console.log('permissions字段值:', userStore.userInfo.permissions)

    // 手动设置测试权限数据
    console.log('手动设置测试权限数据 [1, 2]')
    console.log('设置前Store中的权限:', permissionStore.userMenuIds)

    permissionStore.setUserMenuPermissions([1, 2])

    console.log('手动设置后Store中的权限:', permissionStore.userMenuIds)
    console.log('手动设置后权限数组长度:', permissionStore.userMenuIds.length)

    await permissionStore.fetchMenuTree()

    ElMessage.warning('用户权限数据不存在，已设置测试数据 [1, 2]')
  }
}

// 单独测试菜单树API
const testMenuTreeApi = async () => {
  try {
    console.log('=== 单独测试菜单树API ===')

    // 直接使用axios绕过响应拦截器
    const axios = (await import('axios')).default
    const userStore = useUserStore()

    console.log('正在调用 /api/menu/tree...')
    console.log('使用的token:', userStore.token ? '已设置' : '未设置')

    const axiosResponse = await axios.get('/api/menu/tree', {
      baseURL: '/api',
      headers: {
        'Authorization': userStore.token
      }
    })

    console.log('菜单树API原始HTTP响应:', axiosResponse)
    console.log('响应状态:', axiosResponse.status)
    console.log('响应数据:', axiosResponse.data)

    const response = axiosResponse.data
    console.log('响应code:', response.code)
    console.log('响应msg:', response.msg || response.message)
    console.log('响应data:', response.data)

    if (response.data && Array.isArray(response.data)) {
      console.log('菜单数量:', response.data.length)
      if (response.data.length > 0) {
        console.log('第一个菜单项:', response.data[0])

        // 手动设置菜单树数据
        permissionStore.menuTree = response.data
        ElMessage.success(`菜单树数据已手动设置，共 ${response.data.length} 项`)
      } else {
        ElMessage.warning('菜单树数据为空')
      }
    } else {
      ElMessage.warning('菜单树数据格式异常')
    }
  } catch (error) {
    console.error('菜单树API调用失败:', error)
    console.error('错误响应:', error.response?.data)
    ElMessage.error('菜单树API调用失败: ' + (error.response?.data?.msg || error.message))
  }
}

// 调试权限数据
const debugPermissions = () => {
  console.log('=== 权限数据调试 ===')
  console.log('用户菜单权限ID数组:', permissionStore.userMenuIds)
  console.log('权限数组类型:', typeof permissionStore.userMenuIds)
  console.log('权限数组长度:', permissionStore.userMenuIds.length)

  console.log('菜单树数据:', permissionStore.menuTree)

  // 逐个检查每个菜单的权限
  permissionStore.menuTree.forEach(menu => {
    const hasPermission = permissionStore.hasMenuPermission(menu.id)
    console.log(`菜单 "${menu.menuName}" (ID: ${menu.id}):`, hasPermission ? '有权限' : '无权限')
    console.log(`  - 检查逻辑: userMenuIds.includes(${menu.id}) = ${permissionStore.userMenuIds.includes(menu.id)}`)
  })

  ElMessage.info('权限调试完成，请查看控制台')
}
</script>

<style scoped>
.test-permission {
  padding: 20px;
}

.permission-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-info h3 {
  margin: 20px 0 10px 0;
  color: #333;
}

.permission-info p {
  margin: 5px 0;
  color: #666;
}

.permission-tests {
  margin: 15px 0;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.test-item span:first-child {
  font-weight: 500;
}

.menu-tree {
  margin: 15px 0;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.tree-path {
  color: #999;
  font-size: 12px;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.actions .el-button {
  margin: 0 10px;
}
</style>
