import request from '@/utils/request'

// 添加外部人员
export function addExternalStakeholder(data) {
  return request({
    url: '/project-stakeholder/add-external',
    method: 'post',
    data
  })
}

// 添加内部人员
export function addInternalStakeholder(data) {
  return request({
    url: '/project-stakeholder/add-internal',
    method: 'post',
    data
  })
}

// 编辑外部人员
export function editExternalStakeholder(data) {
  return request({
    url: '/project-stakeholder/edit-external',
    method: 'put',
    data
  })
}

// 编辑内部人员
export function editInternalStakeholder(data) {
  return request({
    url: '/project-stakeholder/edit-internal',
    method: 'put',
    data
  })
}

// 删除人员
export function deleteStakeholder(id) {
  return request({
    url: `/project-stakeholder/delete/${id}`,
    method: 'delete'
  })
}

// 项目干系人分页查询
export function getStakeholderList(params) {
  return request({
    url: '/project-stakeholder/list',
    method: 'get',
    params
  })
}

// 查询项目干系人列表（不分页）
export function getStakeholderListAll(params) {
  return request({
    url: '/project-stakeholder/list-all',
    method: 'get',
    params
  })
}

// Mock数据 - 用于开发测试
export function getStakeholderListMock(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          contractNo: 'HT2024001',
          stakeholderType: '外部人员',
          stakeholderUserId: '',
          stakeholderUserName: '张三',
          source: '1',
          department: '项目管理部',
          projectRole: '项目经理',
          mobile: '135****1234',
          userId: 'user001',
          createTime: '2024-01-15T10:30:00Z',
          userName: 'admin',
          isPmis: '0',
          belongGroupId: ''
        },
        {
          id: '2',
          contractNo: 'HT2024001',
          stakeholderType: '内部人员',
          stakeholderUserId: 'user002',
          stakeholderUserName: '李四',
          source: '2',
          department: '技术部',
          projectRole: '技术负责人',
          mobile: '139****5678',
          userId: 'user001',
          createTime: '2024-01-16T14:20:00Z',
          userName: 'admin',
          isPmis: '1',
          belongGroupId: 'group001'
        },
        {
          id: '3',
          contractNo: 'HT2024001',
          stakeholderType: '外部人员',
          stakeholderUserId: '',
          stakeholderUserName: '王五',
          source: '1',
          department: '供应商B',
          projectRole: '供应商代表',
          mobile: '137****9012',
          userId: 'user001',
          createTime: '2024-01-17T09:15:00Z',
          userName: 'admin',
          isPmis: '0',
          belongGroupId: ''
        }
      ]

      resolve({
        code: 0,
        message: 'success',
        data: mockData
      })
    }, 300)
  })
} 