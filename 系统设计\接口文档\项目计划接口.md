---
title: 交付项目管理系统
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 交付项目管理系统

Base URLs:

# Authentication

# JWT控制器

## POST 姓名+工号登录，返回JWT

POST /api/jwt/login

> Body 请求参数

```json
{
  "key": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[MapString](#schemamapstring)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "message": "生成token失败: ",
  "Authorization": "",
  "expirationSeconds": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[MapObject](#schemamapobject)|

# 区域控制器

## GET 获取区域列表（包含部门名称）

GET /api/area/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "areaName": "",
    "departmentCode": "",
    "departmentName": ""
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*区域列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[AreaVO](#schemaareavo)]|false|none||区域列表|
|» id|integer(int64)|false|none||none|
|» areaName|string|false|none||none|
|» departmentCode|string|false|none||none|
|» departmentName|string|false|none||none|

# 枚举值控制器

## GET 获取附件类型枚举

GET /api/enums/attachment-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取AI生成状态枚举

GET /api/enums/ai-generation-status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取计划状态枚举

GET /api/enums/plan-status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取计划动态类型枚举

GET /api/enums/plan-dynamic-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取审核状态枚举

GET /api/enums/review-status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取项目状态枚举

GET /api/enums/project-status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取表类型枚举

GET /api/enums/table-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取计划操作子类型枚举

GET /api/enums/plan-action-sub-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取责任类型枚举

GET /api/enums/responsibility-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取计划变更类型枚举

GET /api/enums/change-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取版本状态枚举

GET /api/enums/version-status

12345

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取工作台任务筛选类型枚举

GET /api/enums/workbench-plan-filter-types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

# 菜单控制器

## GET 统一菜单树接口，支持name参数

GET /api/menu/tree

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|query|string| 否 |菜单名称（可选）|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "createdTime": "",
      "createdBy": 0,
      "updatedTime": "",
      "updatedBy": 0,
      "menuName": "",
      "menuCode": "",
      "parentId": 0,
      "path": "",
      "icon": "",
      "sort": 0,
      "type": 0,
      "permission": "",
      "status": 0,
      "children": [
        {
          "id": 0,
          "createdTime": "",
          "createdBy": 0,
          "updatedTime": "",
          "updatedBy": 0,
          "menuName": "",
          "menuCode": "",
          "parentId": 0,
          "path": "",
          "icon": "",
          "sort": 0,
          "type": 0,
          "permission": "",
          "status": 0,
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListMenu](#schemaapiresponselistmenu)|

## GET 根据角色ID查询菜单和按钮ID

GET /api/menu/ids-by-role

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|roleId|query|integer| 是 |角色ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
[
  0
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*角色拥有的菜单和按钮ID列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|

# 计划

## GET 查询指定项目下的计划树结构（正式表）

GET /api/project-plan/planTree/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "",
          "projectId": "",
          "reviewStatus": "",
          "planStatus": "",
          "planStartTime": "",
          "planEndTime": "",
          "planDuration": 0,
          "actualStartTime": "",
          "actualEndTime": "",
          "actualDuration": 0,
          "ownerId": 0,
          "ownerName": "",
          "prePlanId": 0,
          "preSeriNum": "",
          "seriNum": "",
          "parentPlanId": 0,
          "changeRemark": "",
          "reviewOpinion": "",
          "attachments": [
            {
              "id": 0,
              "planId": 0,
              "fileName": "",
              "uploaderName": "",
              "uploadedTime": "",
              "url": ""
            }
          ],
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanTreeVO](#schemaapiresponselistprojectplantreevo)|

## GET 查询计划下的所有子计划树状结构（正式表）

GET /api/project-plan/childPlans

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|query|string| 是 |项目ID|
|parentPlanId|query|integer(int64)| 是 |父级计划ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "",
          "projectId": "",
          "reviewStatus": "",
          "planStatus": "",
          "planStartTime": "",
          "planEndTime": "",
          "planDuration": 0,
          "actualStartTime": "",
          "actualEndTime": "",
          "actualDuration": 0,
          "ownerId": 0,
          "ownerName": "",
          "prePlanId": 0,
          "preSeriNum": "",
          "seriNum": "",
          "parentPlanId": 0,
          "changeRemark": "",
          "reviewOpinion": "",
          "attachments": [
            {
              "id": 0,
              "planId": 0,
              "fileName": "",
              "uploaderName": "",
              "uploadedTime": "",
              "url": ""
            }
          ],
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanTreeVO](#schemaapiresponselistprojectplantreevo)|

## GET 查询项目下的所有一级计划列表（正式表）

GET /api/project-plan/topLevelPlans/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanListVO](#schemaapiresponselistprojectplanlistvo)|

## PUT 更新计划信息

PUT /api/project-plan/update

支持更新任务计划状态、责任人、前置任务，更新无需通过审核

> Body 请求参数

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "id": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[PlanUpdateDTO](#schemaplanupdatedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseBoolean](#schemaapiresponseboolean)|

## POST 清除计划变更标识

POST /api/project-plan/clearChangeMarks/{projectId}

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

# 角色控制器

## GET 角色分页列表查询

GET /api/role/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|roleName|query|string| 否 |角色名称（可选）|
|pageNum|query|integer| 是 |页码|
|pageSize|query|integer| 是 |每页大小|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "createdTime": "",
      "createdBy": 0,
      "updatedTime": "",
      "updatedBy": 0,
      "roleName": "",
      "remark": "",
      "menuIds": [
        0
      ]
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseRole](#schemaapipageresponserole)|

## POST 编辑角色及分配权限

POST /api/role/update

> Body 请求参数

```json
{
  "id": 0,
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "roleName": "string",
  "remark": "string",
  "menuIds": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[Role](#schemarole)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 获取所有角色列表

GET /api/role/all

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "createdTime": "",
      "createdBy": 0,
      "updatedTime": "",
      "updatedBy": 0,
      "roleName": "",
      "remark": "",
      "menuIds": [
        0
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListRole](#schemaapiresponselistrole)|

# 定时任务控制器

## POST 手动触发逾期任务处理

POST /api/task/manual-process-overdue

用于测试定时任务功能

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseString](#schemaapiresponsestring)|

# 用户控制器

## GET 用户列表查询（包含区域和角色信息）

GET /api/users/list

根据各种条件查询系统内的用户信息，包含首个区域和角色信息，支持分页操作

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|realName|query|string| 否 |none|
|pageNum|query|integer| 是 |none|
|pageSize|query|integer| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "username": "",
      "number": "",
      "realName": "",
      "phone": "",
      "positionId": "",
      "status": 0,
      "areaId": 0,
      "areaName": "",
      "roleId": 0,
      "roleName": "",
      "createdTime": "",
      "createdBy": 0,
      "updatedTime": "",
      "updatedBy": 0,
      "positionName": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseUserListVO](#schemaapipageresponseuserlistvo)|

## PUT 用户信息编辑

PUT /api/users/edit

支持对用户的基本信息进行编辑，部分字段为必填项

> Body 请求参数

```json
{
  "id": 0,
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "username": "string",
  "number": "string",
  "password": "string",
  "realName": "string",
  "phone": "string",
  "positionId": "string",
  "status": 0,
  "roleIds": [
    0
  ],
  "areaIds": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[User](#schemauser)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 查询当前登录用户信息

GET /api/users/me

获取当前登录用户的基本信息，并返回其所属角色及拥有的菜单和按钮权限

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "userId": 0,
    "username": "",
    "account": "",
    "phone": "",
    "realName": "",
    "position": "",
    "area": "",
    "roles": [
      0
    ],
    "permissions": [
      0
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseUserInfoDTO](#schemaapiresponseuserinfodto)|

## GET 根据角色ID查询该角色的所有用户

GET /api/users/by-role

前端传入角色ID，返回该角色下的所有用户；不传则查询所有角色的用户

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|roleId|query|integer| 否 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "createdTime": "",
      "createdBy": 0,
      "updatedTime": "",
      "updatedBy": 0,
      "username": "",
      "number": "",
      "password": "",
      "realName": "",
      "phone": "",
      "positionId": "",
      "status": 0,
      "roleIds": [
        0
      ],
      "areaIds": [
        0
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListUser](#schemaapiresponselistuser)|

# 日报控制器

## GET 分页查询日报

GET /api/project/daily/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|integer| 否 |页码（从1开始）|
|pageSize|query|integer| 否 |每页条数|
|projectId|query|string| 是 |项目ID|
|reportDate|query|string| 否 |填报日期（精确查询）|
|startDate|query|string| 否 |开始日期（范围查询）|
|endDate|query|string| 否 |结束日期（范围查询）|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "projectId": "",
      "reportDate": "",
      "otherWorkContent": "",
      "workContent": "",
      "completionStatus": "",
      "problemsEncountered": "",
      "solutions": "",
      "imageCount": 0,
      "createdTime": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseProjectDailyListVO](#schemaapipageresponseprojectdailylistvo)|

## GET 查询日报详情

GET /api/project/daily/detail

当ID为空时，查询当日的计划返回
当ID不为空时，查询日报信息返回

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |日报ID（可选）|
|projectId|query|string| 否 |项目ID（当ID为空时必填）|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "projectId": "",
    "reportDate": "",
    "reporterName": "",
    "startContent": "",
    "otherContent": "",
    "completionStatus": "",
    "problems": "",
    "solutions": "",
    "dailyImages": [
      ""
    ],
    "planProgressList": [
      {
        "taskId": 0,
        "taskName": "",
        "taskStatus": "",
        "planStartTime": "",
        "planEndTime": "",
        "ownerName": "",
        "progressContent": "",
        "imageName": "",
        "imageUrl": ""
      }
    ],
    "createdTime": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseProjectDailyDetailVO](#schemaapiresponseprojectdailydetailvo)|

## POST 保存日报（新增或编辑）

POST /api/project/daily/save

> Body 请求参数

```json
{
  "id": 0,
  "projectId": "string",
  "reportDate": "string",
  "reporterId": 0,
  "startDescription": "string",
  "otherWorkContent": "string",
  "completionStatus": "string",
  "problemsEncountered": "string",
  "solutions": "string",
  "dailyImageUrls": [
    {
      "url": "string",
      "btId": 0,
      "objectName": "string"
    }
  ],
  "planProgressList": [
    {
      "taskId": 0,
      "progressContent": "string",
      "imageUrl": "string",
      "taskStatus": "NOT_STARTED",
      "btId": "string",
      "objectName": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectDailySaveDTO](#schemaprojectdailysavedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseLong](#schemaapiresponselong)|

# 区域同步控制器

## POST 批量同步所有区域信息

POST /api/area-sync/sync-all

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "message": "批量同步区域信息失败: ",
  "timestamp": 1753953482055
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[MapObject](#schemamapobject)|

## GET 获取同步状态

GET /api/area-sync/status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "message": "区域同步服务运行正常",
  "timestamp": 1753953482162,
  "service": "AreaSyncService",
  "status": "running"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[MapObject](#schemamapobject)|

# 用户同步控制器

## POST 批量同步所有用户信息

POST /api/user-sync/sync-all

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "message": "批量同步用户信息失败: ",
  "timestamp": 1753953482278
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[MapObject](#schemamapobject)|

## GET 获取同步状态

GET /api/user-sync/status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "message": "用户同步服务运行正常",
  "timestamp": 1753953482387,
  "service": "UserSyncService",
  "status": "running"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[MapObject](#schemamapobject)|

# 计划草稿

## GET 查询项目下的计划草稿树结构

GET /api/project-plan/draft/planTree/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "",
          "projectId": "",
          "reviewStatus": "",
          "planStatus": "",
          "planStartTime": "",
          "planEndTime": "",
          "planDuration": 0,
          "actualStartTime": "",
          "actualEndTime": "",
          "actualDuration": 0,
          "ownerId": 0,
          "ownerName": "",
          "prePlanId": 0,
          "preSeriNum": "",
          "seriNum": "",
          "parentPlanId": 0,
          "changeRemark": "",
          "reviewOpinion": "",
          "attachments": [
            {
              "id": 0,
              "planId": 0,
              "fileName": "",
              "uploaderName": "",
              "uploadedTime": "",
              "url": ""
            }
          ],
          "children": [],
          "rejected": false,
          "planReviewStatus": "",
          "changeType": "",
          "isDeleted": 0,
          "version": 0
        }
      ],
      "rejected": false,
      "planReviewStatus": "",
      "changeType": "",
      "isDeleted": 0,
      "version": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanDraftTreeVO](#schemaapiresponselistprojectplandrafttreevo)|

## POST 查询计划下的子级计划草稿树状结构

POST /api/project-plan/draft/childPlans

> Body 请求参数

```json
{
  "projectId": "string",
  "planId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[BasePlanQueryDTO](#schemabaseplanquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "",
          "projectId": "",
          "reviewStatus": "",
          "planStatus": "",
          "planStartTime": "",
          "planEndTime": "",
          "planDuration": 0,
          "actualStartTime": "",
          "actualEndTime": "",
          "actualDuration": 0,
          "ownerId": 0,
          "ownerName": "",
          "prePlanId": 0,
          "preSeriNum": "",
          "seriNum": "",
          "parentPlanId": 0,
          "changeRemark": "",
          "reviewOpinion": "",
          "attachments": [
            {
              "id": 0,
              "planId": 0,
              "fileName": "",
              "uploaderName": "",
              "uploadedTime": "",
              "url": ""
            }
          ],
          "children": [],
          "rejected": false,
          "planReviewStatus": "",
          "changeType": "",
          "isDeleted": 0,
          "version": 0
        }
      ],
      "rejected": false,
      "planReviewStatus": "",
      "changeType": "",
      "isDeleted": 0,
      "version": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanDraftTreeVO](#schemaapiresponselistprojectplandrafttreevo)|

## GET 查询项目下的所有一级计划草稿列表

GET /api/project-plan/draft/topLevelPlans/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planName": "",
      "projectId": "",
      "reviewStatus": "",
      "planStatus": "",
      "planStartTime": "",
      "planEndTime": "",
      "planDuration": 0,
      "actualStartTime": "",
      "actualEndTime": "",
      "actualDuration": 0,
      "ownerId": 0,
      "ownerName": "",
      "prePlanId": 0,
      "preSeriNum": "",
      "seriNum": "",
      "parentPlanId": 0,
      "changeRemark": "",
      "reviewOpinion": "",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "",
          "uploaderName": "",
          "uploadedTime": "",
          "url": ""
        }
      ],
      "changeType": "",
      "planReviewStatus": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListPlanDraftListVO](#schemaapiresponselistplandraftlistvo)|

## POST 新增计划草稿

POST /api/project-plan/draft/create

支持层级关系，可指定父级计划
新增的逻辑：
1. 草稿表是正式表全量数据备份
2. 新增操作：在草稿表中新增对应记录
3. 提交审核时，新增变更会作为变更提交
4. 审核通过：同步新增草稿表和正式表的记录
5. 审核驳回：不新增正式表

> Body 请求参数

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "planName": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "changeRemark": "string",
  "id": 0,
  "projectId": "string",
  "actualStartTime": "string",
  "actualEndTime": "string",
  "parentPlanId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[PlanDraftCreateDTO](#schemaplandraftcreatedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## PUT 编辑计划草稿

PUT /api/project-plan/draft/update

编辑的逻辑：
1. 草稿表是正式表全量数据备份
2. 编辑操作：在草稿表中更新对应记录
3. 提交审核时，编辑变更会作为变更提交
4. 审核通过：同步更新草稿表和正式表的记录
5. 审核驳回：不更新正式表
6. 变更说明：支持填写变更说明，保存到审核记录表中

> Body 请求参数

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "id": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[PlanUpdateDTO](#schemaplanupdatedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## PUT 批量操作计划草稿（支持新增、编辑、删除混合操作）

PUT /api/project-plan/draft/batchUpdate

批量编辑的逻辑：
1. 批量校验所有传入的草稿数据
2. 根据是否包含需要审核的字段进行分类处理
3. 不包含需要审核的字段：直接同步到正式表，状态设为生效中
4. 包含需要审核的字段：保持草稿状态，创建审核记录并自动提交审核
5. 为所有变更创建对应的动态记录
6. 每个计划使用各自的变更说明

> Body 请求参数

```json
{
  "projectId": "66D1389DDD88FA5F9C28E836",
  "createList": [
    {
      "projectId": "66D1389DDD88FA5F9C28E836",
      "planName": "新增阶段A",
      "planStartTime": "2025-08-10",
      "planEndTime": "2025-08-20",
      "ownerId": 1001,
      "parentPlanId": null,
      "seriNum": "3",
      "planStatus": "1"
    },
    {
      "projectId": "66D1389DDD88FA5F9C28E836",
      "planName": "新增任务A1",
      "planStartTime": "2025-08-11",
      "planEndTime": "2025-08-15",
      "ownerId": 1002,
      "parentPlanId": null,
      "seriNum": "3.1",
      "planStatus": "1"
    }
  ],
  "updateList": [
    {
      "id": 1948563298430284500,
      "planName": "项目收款修改后的任务名称",
      "planStartTime": "2025-08-12",
      "planEndTime": "2025-08-18",
      "ownerId": 1003,
      "prePlanId": 150,
      "seriNum": "2.1",
      "preSeriNum": "1.5",
      "planStatus": "1",
      "changeRemark": "调整计划时间和负责人"
    },
    {
      "id": 1948563298430284500,
      "planName": "要被删除的计划",
      "planStartTime": "2025-08-05",
      "planEndTime": "2025-08-10",
      "ownerId": 1001,
      "changeRemark": "这个编辑会被忽略，因为该计划在删除列表中"
    }
  ],
  "deleteList": [
    {
      "id": 1948563298430284500,
      "changeRemark": "删除阶段4"
    },
    {
      "id": 1948563298430284500,
      "changeRemark": "删除重复的任务"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[BatchPlanUpdateDTO](#schemabatchplanupdatedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## DELETE 删除计划草稿（标记删除）

DELETE /api/project-plan/draft/delete

<p>
新的删除逻辑：
1. 草稿表是正式表的全量数据备份
2. 删除操作：在草稿表中标记对应记录为已删除（is_deleted=1）
3. 支持级联删除，删除父级时会标记删除所有子级
4. 提交审核时，已删除标记的记录会作为删除变更提交
5. 审核通过：同步删除草稿表和正式表的记录
6. 审核驳回：清除删除标记，记录恢复正常状态

> Body 请求参数

```json
{
  "projectId": "string",
  "planId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[BasePlanQueryDTO](#schemabaseplanquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## POST 清除计划变更标识

POST /api/project-plan/draft/clearChangeMarks/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## POST 保存计划树形结构到草稿表

POST /api/project-plan/draft/saveToDraft

将传入的计划树形数据保存到草稿表中

> Body 请求参数

```json
{
  "projectId": "string",
  "plan": [
    {
      "stage": "string",
      "tasks": [
        {
          "start_date": "string",
          "end_date": "string",
          "number": "string",
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[SavePlanTreeToDraftDTO](#schemasaveplantreetodraftdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## POST 从Excel导入计划数据

POST /api/project-plan/draft/importFromExcel

基于新的Excel导入框架，支持多种类型的Excel导入

使用说明：
1. 仅预审核阶段的项目可以导入，会覆盖项目下原有的草稿数据
2. 支持.xlsx和.xls格式的Excel文件，文件大小限制为10MB
3. 采用宽松校验策略，数据问题记录警告而不阻止导入
4. 前置工作项通过序号关联，自动建立依赖关系
5. 根据序号自动建立工作项层级关系

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|query|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |Excel文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "success": false,
    "totalRows": 0,
    "processedRows": 0,
    "warningCount": 0,
    "importTime": "",
    "projectId": "",
    "warnings": [
      {
        "rowNumber": 0,
        "fieldName": "",
        "warningMessage": ""
      }
    ],
    "errorMessage": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseExcelImportResultVO](#schemaapiresponseexcelimportresultvo)|

## GET 批量生成雪花算法ID

GET /api/project-plan/draft/generateIds

固定生成10个ID，用于前端创建多级子项时进行parentID关联

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    0
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListLong](#schemaapiresponselistlong)|

## GET 生成单个雪花算法ID

GET /api/project-plan/draft/generateId

用于前端在创建计划草稿时预先获取ID

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseLong](#schemaapiresponselong)|

# 计划详情

## POST 查询计划详情

POST /api/plans/detail

查询并展示指定计划的详细信息，包括基本信息、变更记录、审核动态等

> Body 请求参数

```json
{
  "projectId": "string",
  "planId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[BasePlanQueryDTO](#schemabaseplanquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "planId": 0,
    "planName": "",
    "prePlan": "",
    "ownerName": "",
    "reviewStatus": "",
    "planStatus": "",
    "planStartTime": "",
    "planEndTime": "",
    "planDuration": 0,
    "actualStartTime": "",
    "actualEndTime": "",
    "actualDuration": 0,
    "seriNum": "",
    "preSeriNum": "",
    "todayTaskCount": 0,
    "upcomingTaskCount": 0,
    "overdueTaskCount": 0,
    "dailyReportStatus": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponsePlanDetailVO](#schemaapiresponseplandetailvo)|

## POST 查询计划动态信息（支持类型筛选）

POST /api/plans/dynamics

分页查询指定计划的最新动态信息，支持按"全部、操作、日报"类型筛选，按时间降序排列

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 20,
  "planId": 0,
  "dynamicType": "ACTION"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[DynamicQueryDTO](#schemadynamicquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "dynamicTime": "",
      "operatorName": "",
      "dynamicContent": "",
      "dynamicType": "",
      "actionSubType": "",
      "oldValue": "",
      "newValue": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponsePlanDynamicVO](#schemaapipageresponseplandynamicvo)|

# 计划审核

## POST 提交项目计划审核

POST /api/project-plan/review/submit/{projectId}

将项目下所有草稿状态的计划提交审核
计划提交审核逻辑：
1. 获取所有草稿状态的计划
2. 为每个计划创建一个审核记录，审核状态为待审核
3. 草稿状态的计划状态更新为待审核

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## POST 批量审核项目计划

POST /api/project-plan/review/batchReview

<p>
业务逻辑说明：
1. reviewResult：本次传入的所有计划的统一审核状态（都通过或都驳回）
2. reviewInfoList：每个计划的版本和审核意见信息
3. 审核流程：
- 验证每个计划的版本号（乐观锁控制）
- 保存审核结果和审核意见
- 根据审核结果更新计划状态
4. 审核通过时：将草稿表数据同步到正式表，删除类型的计划会从正式表删除
5. 审核驳回时：不改变正式表数据，恢复草稿状态

> Body 请求参数

```json
{
  "projectId": "string",
  "reviewResult": "DRAFT",
  "reviewInfoList": [
    {
      "draftId": 0,
      "reviewOpinion": "string",
      "version": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[PlanReviewDTO](#schemaplanreviewdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 查询项目计划审核列表

GET /api/project-plan/review/reviewList/{projectId}

查询该项目下所有待审核的计划，按计划序号排序返回给前端展示

业务逻辑说明：
1. 根据项目ID查询所有待审核状态的计划审核记录
2. 批量查询对应的草稿信息和用户信息
3. 按计划序号排序并组装返回结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "seriNum": "",
      "draftId": 0,
      "reviewId": 0,
      "planName": "",
      "ownerId": 0,
      "ownerName": "",
      "planStartTime": "",
      "planEndTime": "",
      "changeType": "",
      "changeRemark": "",
      "version": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectPlanReviewListVO](#schemaapiresponselistprojectplanreviewlistvo)|

# 数据字典控制器

## GET 根据类型获取数据字典映射

GET /api/dictionary/map

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|query|string| 是 |字典类型|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

## GET 获取所有数据字典类型

GET /api/dictionary/types

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseMapString](#schemaapiresponsemapstring)|

# 项目附件

## POST 上传计划附件（图片）

POST /api/project-attachments/upload

支持为计划上传一张图片，模拟文件上传并保存到附件表

> Body 请求参数

```json
{
  "url": "string",
  "planId": 0,
  "btId": 0,
  "objectName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[AttachmentUploadQueryDTO](#schemaattachmentuploadquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseBoolean](#schemaapiresponseboolean)|

## POST 分页查询计划附件列表

POST /api/project-attachments/attachments

查询指定计划关联的附件信息，支持分页展示

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 20,
  "planId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[AttachmentQueryDTO](#schemaattachmentquerydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "",
      "uploaderName": "",
      "uploadedTime": "",
      "url": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseAttachmentVO](#schemaapipageresponseattachmentvo)|

# 项目干系人控制器

## POST 添加外部干系人

POST /api/project-stakeholder/add-external

> Body 请求参数

```json
{
  "stakeholderUserName": "string",
  "department": "string",
  "projectRole": "string",
  "mobile": "string",
  "source": "string",
  "contractNo": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectStakeholderAddExternalDTO](#schemaprojectstakeholderaddexternaldto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## POST 添加内部干系人

POST /api/project-stakeholder/add-internal

> Body 请求参数

```json
{
  "stakeholderUserId": "string",
  "projectRole": "string",
  "source": "string",
  "contractNo": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectStakeholderAddInternalDTO](#schemaprojectstakeholderaddinternaldto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## PUT 编辑外部干系人

PUT /api/project-stakeholder/edit-external

> Body 请求参数

```json
{
  "id": "string",
  "stakeholderUserName": "string",
  "department": "string",
  "projectRole": "string",
  "mobile": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectStakeholderEditExternalDTO](#schemaprojectstakeholdereditexternaldto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## PUT 编辑内部干系人

PUT /api/project-stakeholder/edit-internal

> Body 请求参数

```json
{
  "id": "string",
  "stakeholderUserId": "string",
  "projectRole": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectStakeholderEditInternalDTO](#schemaprojectstakeholdereditinternaldto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## DELETE 删除干系人

DELETE /api/project-stakeholder/delete/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |干系人ID|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 根据合同编号分页查询项目干系人列表

GET /api/project-stakeholder/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|contractNo|query|string| 是 |合同编号|
|pageNum|query|integer| 是 |页码|
|pageSize|query|integer| 是 |每页大小|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "",
      "contractNo": "",
      "stakeholderType": "",
      "stakeholderUserId": "",
      "stakeholderUserName": "",
      "source": "",
      "department": "",
      "projectRole": "",
      "mobile": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseProjectStakeholderVO](#schemaapipageresponseprojectstakeholdervo)|

## GET 根据合同编号查询项目干系人列表（不分页）

GET /api/project-stakeholder/list-all

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|contractNo|query|string| 是 |合同编号|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "",
      "contractNo": "",
      "stakeholderType": "",
      "stakeholderUserId": "",
      "stakeholderUserName": "",
      "source": "",
      "department": "",
      "projectRole": "",
      "mobile": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectStakeholderVO](#schemaapiresponselistprojectstakeholdervo)|

# 项目管理

## GET 项目列表分页查询

GET /api/project/list

支持按项目名称或项目简称进行模糊查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectName|query|string| 否 |none|
|contractNo|query|string| 否 |none|
|projectState|query|string| 否 |none|
|followedOnly|query|boolean| 否 |是否仅查询已关注项目：true=仅已关注，false/null=不限|
|pageNum|query|integer| 否 |none|
|pageSize|query|integer| 否 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "",
      "contractNo": "",
      "projectName": "",
      "projectShortName": "",
      "projectState": "",
      "projectType": "",
      "department": "",
      "biddingDate": "",
      "winningDate": "",
      "signDate": "",
      "projectAddress": "",
      "firstPartyId": "",
      "firstPartyName": "",
      "userSideId": "",
      "userSideName": "",
      "otherSidId": "",
      "otherSidName": "",
      "province": "",
      "city": "",
      "projectDuration": "",
      "contractAmount": 0,
      "submittedAmount": 0,
      "changeAmount": 0,
      "finalAmount": 0,
      "paymentMethod": "",
      "paymentFactor": 0,
      "constructionProgress": "",
      "constructionStartDate": "",
      "constructionEndDate": "",
      "acceptDate": "",
      "guaranteeStartDate": "",
      "guaranteeEndDate": "",
      "guarantee": "",
      "createTime": "",
      "userId": "",
      "expireDate": "",
      "paymentCollection": 0,
      "remark": "",
      "biddingType": "",
      "payCountMoney": 0,
      "businessType": "",
      "areaId": "",
      "currency": "",
      "areaSsId": "",
      "areaShId": "",
      "contractState": "",
      "planid": "",
      "jiduDate": "",
      "zljjzt": "",
      "shjjzt": "",
      "nbysz": "",
      "pzzt": "",
      "protaxrate": "",
      "maxvisionCode": "",
      "contractType": "",
      "contractSonNo": "",
      "sdysktype": "",
      "collectionCheckState": "",
      "checkPerson": "",
      "provisionalSum": 0,
      "userName": "",
      "checkstate": "",
      "storagestate": "",
      "plusMinus": "",
      "jscollectionCheckState": "",
      "jscheckPerson": "",
      "dataProgress": "",
      "auditProgress": "",
      "constructPortName": "",
      "settleRemark": "",
      "lockState": "",
      "yqsqbzt": "",
      "yqsqbz": "",
      "firstInstanceState": "",
      "financePerson": "",
      "financeState": "",
      "deliveryNotice": "",
      "crmNo": "",
      "pccmState": "",
      "okPay": 0,
      "payIsnot": "",
      "addTime": "",
      "numberNos": "",
      "olldContractNo": "",
      "zbContent": "",
      "erpTime": "",
      "areaIdOld": "",
      "isStarted": "",
      "planStartTime": "",
      "planEndTime": "",
      "startTime": "",
      "acceptDateNew": "",
      "projectManagerId": "",
      "projectManagerName": "",
      "aiGenerationStatus": ""
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseProjectManageVO](#schemaapipageresponseprojectmanagevo)|

## GET 查询项目经理和成员的在建项目列表（不分页）

GET /api/project/list-all

支持按项目名称或项目简称进行模糊查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectName|query|string| 否 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "",
      "contractNo": "",
      "projectName": "",
      "projectShortName": "",
      "projectState": "",
      "projectType": "",
      "department": "",
      "biddingDate": "",
      "winningDate": "",
      "signDate": "",
      "projectAddress": "",
      "firstPartyId": "",
      "firstPartyName": "",
      "userSideId": "",
      "userSideName": "",
      "otherSidId": "",
      "otherSidName": "",
      "province": "",
      "city": "",
      "projectDuration": "",
      "contractAmount": 0,
      "submittedAmount": 0,
      "changeAmount": 0,
      "finalAmount": 0,
      "paymentMethod": "",
      "paymentFactor": 0,
      "constructionProgress": "",
      "constructionStartDate": "",
      "constructionEndDate": "",
      "acceptDate": "",
      "guaranteeStartDate": "",
      "guaranteeEndDate": "",
      "guarantee": "",
      "createTime": "",
      "userId": "",
      "expireDate": "",
      "paymentCollection": 0,
      "remark": "",
      "biddingType": "",
      "payCountMoney": 0,
      "businessType": "",
      "areaId": "",
      "currency": "",
      "areaSsId": "",
      "areaShId": "",
      "contractState": "",
      "planid": "",
      "jiduDate": "",
      "zljjzt": "",
      "shjjzt": "",
      "nbysz": "",
      "pzzt": "",
      "protaxrate": "",
      "maxvisionCode": "",
      "contractType": "",
      "contractSonNo": "",
      "sdysktype": "",
      "collectionCheckState": "",
      "checkPerson": "",
      "provisionalSum": 0,
      "userName": "",
      "checkstate": "",
      "storagestate": "",
      "plusMinus": "",
      "jscollectionCheckState": "",
      "jscheckPerson": "",
      "dataProgress": "",
      "auditProgress": "",
      "constructPortName": "",
      "settleRemark": "",
      "lockState": "",
      "yqsqbzt": "",
      "yqsqbz": "",
      "firstInstanceState": "",
      "financePerson": "",
      "financeState": "",
      "deliveryNotice": "",
      "crmNo": "",
      "pccmState": "",
      "okPay": 0,
      "payIsnot": "",
      "addTime": "",
      "numberNos": "",
      "olldContractNo": "",
      "zbContent": "",
      "erpTime": "",
      "areaIdOld": "",
      "isStarted": "",
      "planStartTime": "",
      "planEndTime": "",
      "startTime": "",
      "acceptDateNew": "",
      "projectManagerId": "",
      "projectManagerName": "",
      "aiGenerationStatus": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListProjectManageVO](#schemaapiresponselistprojectmanagevo)|

## POST 项目编辑

POST /api/project/edit

> Body 请求参数

```json
{
  "id": "string",
  "projectState": "string",
  "projectShortName": "string",
  "projectManagerId": "string",
  "projectManagerName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[ProjectManageEditDTO](#schemaprojectmanageeditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 获取项目版本号

GET /api/project/version/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "projectId": "",
    "version": 0,
    "versionStatus": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseProjectVersionVO](#schemaapiresponseprojectversionvo)|

## POST 开始AI生成

POST /api/project/ai/generate

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|query|string| 是 |项目ID|
|Authorization|header|string| 否 |潘勇的|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |上传的文件（支持.doc、.docx、.pdf格式，最大50MB）|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## GET 项目总览

GET /api/project/overview

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "inProgressProjectCount": 0,
    "totalContractAmount": 0,
    "notStartedProjectCount": 0,
    "notStartedTotalAmount": 0,
    "totalReceivedAmount": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseProjectOverviewVO](#schemaapiresponseprojectoverviewvo)|

## GET 我负责的项目概览（在建项目）

GET /api/project/my-overview

支持按项目名称或项目简称进行模糊查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectName|query|string| 否 |none|
|projectState|query|string| 否 |none|
|planReviewStatus|query|string| 否 |none|
|pageNum|query|integer| 是 |none|
|pageSize|query|integer| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "projectId": "",
      "contractNo": "",
      "projectName": "",
      "region": "",
      "projectState": "",
      "progress": "",
      "duration": "",
      "planVersion": "",
      "planReviewStatus": "",
      "todayTaskCount": 0,
      "upcomingTaskCount": 0,
      "overdueTaskCount": 0,
      "todayReportStatus": "",
      "reportId": 0
    }
  ],
  "total": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiPageResponseMyProjectOverviewVO](#schemaapipageresponsemyprojectoverviewvo)|

# 文件上传控制器

## GET 获取AccessToken

GET /api/file-upload/get

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseString](#schemaapiresponsestring)|

## POST 获取文件上传配置

POST /api/file-upload/config

对应对象存储接口文档第3个接口

> Body 请求参数

```json
{
  "btId": 0,
  "objectName": "string",
  "expireTime": 0,
  "mimeType": "string",
  "maxSize": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[FileUploadConfigDTO](#schemafileuploadconfigdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "key": "",
    "url": "",
    "expireTime": 0,
    "accessDomain": "",
    "formData": {
      "": ""
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseFileUploadConfigVO](#schemaapiresponsefileuploadconfigvo)|

## POST 文件上传

POST /api/file-upload/upload

先获取上传配置，再进行文件上传

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|btId|query|integer| 否 |业务类型ID|
|objectName|query|string| 否 |文件名称（可选，不传则使用原文件名）|
|expireTime|query|integer| 否 |过期时间（可选）|
|mimeType|query|string| 否 |文件类型（可选）|
|maxSize|query|integer| 否 |最大文件大小（可选）|
|Authorization|header|string| 否 |潘勇的|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |要上传的文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "fileUrl": "",
    "fileName": "",
    "fileSize": 0,
    "success": false,
    "errorMessage": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseFileUploadResultVO](#schemaapiresponsefileuploadresultvo)|

## POST 文件删除

POST /api/file-upload/delete

根据业务类型ID和文件名称删除指定的存储对象

> Body 请求参数

```json
{
  "btId": 0,
  "objectName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|
|body|body|[FileDeleteDTO](#schemafiledeletedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "success": false,
    "message": "",
    "deletedFileName": "",
    "errorMessage": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseFileDeleteResultVO](#schemaapiresponsefiledeleteresultvo)|

# 区域信息控制器

## POST 手动刷新缓存

POST /api/area/cache/refresh

<p>
清空所有缓存并重新预热，用于数据更新后的缓存同步
通常在批量导入或修改区域数据后调用
<p>
请求示例：POST /api/area/cache/refresh

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

# 项目关注接口

## POST 关注项目（幂等）

POST /api/project/follow/{projectId}

步骤：校验项目 -> 已关注则跳过 -> 插入记录

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

## DELETE 取消关注（幂等）

DELETE /api/project/follow/{projectId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseVoid](#schemaapiresponsevoid)|

# 工作台控制器

## GET 我关注项目的汇总信息

GET /api/workbench/followed/summary

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "todayTaskCount": 0,
    "overdueTaskCount": 0,
    "dueTodayTaskCount": 0,
    "dueThisWeekTaskCount": 0,
    "dueThisMonthTaskCount": 0,
    "totalTaskCount": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseFollowedProjectSummaryBaseVO](#schemaapiresponsefollowedprojectsummarybasevo)|

## GET 我关注项目的汇总信息（列表）

GET /api/workbench/followed/summary/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "todayTaskCount": 0,
      "overdueTaskCount": 0,
      "dueTodayTaskCount": 0,
      "dueThisWeekTaskCount": 0,
      "dueThisMonthTaskCount": 0,
      "totalTaskCount": 0,
      "projectId": "",
      "projectName": "",
      "projectManagerName": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListFollowedProjectSummaryVO](#schemaapiresponselistfollowedprojectsummaryvo)|

## GET 查询指定项目的任务列表（按筛选类型）

GET /api/workbench/followed/project/{projectId}/{type}

支持分页：pageNum/pageSize

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|string| 是 |none|
|type|path|string| 是 |none|
|Authorization|header|string| 否 |潘勇的|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "seriNum": "",
      "planName": "",
      "ownerName": "",
      "executorName": "",
      "planStartTime": "",
      "planEndTime": "",
      "version": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ApiResponseListOverduePlanListItemVO](#schemaapiresponselistoverdueplanlistitemvo)|

# 数据模型

<h2 id="tocS_MapObject">MapObject</h2>

<a id="schemamapobject"></a>
<a id="schema_MapObject"></a>
<a id="tocSmapobject"></a>
<a id="tocsmapobject"></a>

```json
{
  "success": true,
  "message": "string",
  "timestamp": 0,
  "service": "string",
  "status": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|message|string|false|none||none|
|timestamp|integer|false|none||none|
|service|string|false|none||none|
|status|string|false|none||none|

<h2 id="tocS_MapString">MapString</h2>

<a id="schemamapstring"></a>
<a id="schema_MapString"></a>
<a id="tocSmapstring"></a>
<a id="tocsmapstring"></a>

```json
{
  "key": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||none|

<h2 id="tocS_AreaVO">AreaVO</h2>

<a id="schemaareavo"></a>
<a id="schema_AreaVO"></a>
<a id="tocSareavo"></a>
<a id="tocsareavo"></a>

```json
{
  "id": 0,
  "areaName": "string",
  "departmentCode": "string",
  "departmentName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|areaName|string|false|none||none|
|departmentCode|string|false|none||none|
|departmentName|string|false|none||none|

<h2 id="tocS_ApiResponseMapString">ApiResponseMapString</h2>

<a id="schemaapiresponsemapstring"></a>
<a id="schema_ApiResponseMapString"></a>
<a id="tocSapiresponsemapstring"></a>
<a id="tocsapiresponsemapstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "key": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[MapString](#schemamapstring)|false|none||请求成功时，返回的数据|

<h2 id="tocS_Menu">Menu</h2>

<a id="schemamenu"></a>
<a id="schema_Menu"></a>
<a id="tocSmenu"></a>
<a id="tocsmenu"></a>

```json
{
  "id": 0,
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "menuName": "string",
  "menuCode": "string",
  "parentId": 0,
  "path": "string",
  "icon": "string",
  "sort": 0,
  "type": 0,
  "permission": "string",
  "status": 0,
  "children": [
    {
      "id": 0,
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "menuName": "string",
      "menuCode": "string",
      "parentId": 0,
      "path": "string",
      "icon": "string",
      "sort": 0,
      "type": 0,
      "permission": "string",
      "status": 0,
      "children": [
        {
          "id": 0,
          "createdTime": "string",
          "createdBy": 0,
          "updatedTime": "string",
          "updatedBy": 0,
          "menuName": "string",
          "menuCode": "string",
          "parentId": 0,
          "path": "string",
          "icon": "string",
          "sort": 0,
          "type": 0,
          "permission": "string",
          "status": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||记录id|
|createdTime|string|false|none||创建时间|
|createdBy|integer(int64)|false|none||创建人id|
|updatedTime|string|false|none||修改时间|
|updatedBy|integer(int64)|false|none||修改者id|
|menuName|string|false|none||none|
|menuCode|string|false|none||none|
|parentId|integer(int64)|false|none||none|
|path|string|false|none||none|
|icon|string|false|none||none|
|sort|integer|false|none||none|
|type|integer|false|none||1-菜单 2-按钮|
|permission|string|false|none||none|
|status|integer|false|none||none|
|children|[[Menu](#schemamenu)]|false|none||none|

<h2 id="tocS_ApiResponseListMenu">ApiResponseListMenu</h2>

<a id="schemaapiresponselistmenu"></a>
<a id="schema_ApiResponseListMenu"></a>
<a id="tocSapiresponselistmenu"></a>
<a id="tocsapiresponselistmenu"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "menuName": "string",
      "menuCode": "string",
      "parentId": 0,
      "path": "string",
      "icon": "string",
      "sort": 0,
      "type": 0,
      "permission": "string",
      "status": 0,
      "children": [
        {
          "id": 0,
          "createdTime": "string",
          "createdBy": 0,
          "updatedTime": "string",
          "updatedBy": 0,
          "menuName": "string",
          "menuCode": "string",
          "parentId": 0,
          "path": "string",
          "icon": "string",
          "sort": 0,
          "type": 0,
          "permission": "string",
          "status": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[Menu](#schemamenu)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_AttachmentVO">AttachmentVO</h2>

<a id="schemaattachmentvo"></a>
<a id="schema_AttachmentVO"></a>
<a id="tocSattachmentvo"></a>
<a id="tocsattachmentvo"></a>

```json
{
  "id": 0,
  "planId": 0,
  "fileName": "string",
  "uploaderName": "string",
  "uploadedTime": "string",
  "url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||附件ID|
|planId|integer(int64)|false|none||计划ID，关联project_plan.id|
|fileName|string|false|none||文件名|
|uploaderName|string|false|none||创建人|
|uploadedTime|string|false|none||上传时间|
|url|string|false|none||文件URL|

<h2 id="tocS_ProjectPlanTreeVO">ProjectPlanTreeVO</h2>

<a id="schemaprojectplantreevo"></a>
<a id="schema_ProjectPlanTreeVO"></a>
<a id="tocSprojectplantreevo"></a>
<a id="tocsprojectplantreevo"></a>

```json
{
  "id": 0,
  "planName": "string",
  "projectId": "string",
  "reviewStatus": "string",
  "planStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "planDuration": 0,
  "actualStartTime": "string",
  "actualEndTime": "string",
  "actualDuration": 0,
  "ownerId": 0,
  "executorId": 0,
  "ownerName": "string",
  "executorName": "string",
  "prePlanId": 0,
  "preSeriNum": "string",
  "seriNum": "string",
  "parentPlanId": 0,
  "changeRemark": "string",
  "reviewOpinion": "string",
  "attachments": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "string",
      "uploaderName": "string",
      "uploadedTime": "string",
      "url": "string"
    }
  ],
  "children": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "string",
          "projectId": "string",
          "reviewStatus": "string",
          "planStatus": "string",
          "planStartTime": "string",
          "planEndTime": "string",
          "planDuration": 0,
          "actualStartTime": "string",
          "actualEndTime": "string",
          "actualDuration": 0,
          "ownerId": 0,
          "executorId": 0,
          "ownerName": "string",
          "executorName": "string",
          "prePlanId": 0,
          "preSeriNum": "string",
          "seriNum": "string",
          "parentPlanId": 0,
          "changeRemark": "string",
          "reviewOpinion": "string",
          "attachments": [
            {}
          ],
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||计划主键ID|
|planName|string|false|none||计划名称|
|projectId|string|false|none||所属项目ID|
|reviewStatus|string|false|none||审核状态|
|planStatus|string|false|none||计划状态|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划结束时间|
|planDuration|integer|false|none||计划工期（天）|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际结束时间|
|actualDuration|integer|false|none||实际工期（天）|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|ownerName|string|false|none||负责人姓名|
|executorName|string|false|none||执行人姓名|
|prePlanId|integer(int64)|false|none||前置计划ID，如果有依赖关系|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|parentPlanId|integer(int64)|false|none||父级计划ID，用于构建树形结构|
|changeRemark|string|false|none||变更说明|
|reviewOpinion|string|false|none||审核意见|
|attachments|[[AttachmentVO](#schemaattachmentvo)]|false|none||附件列表|
|children|[[ProjectPlanTreeVO](#schemaprojectplantreevo)]|false|none||子计划列表，树形结构的子节点|

<h2 id="tocS_ApiResponseListProjectPlanTreeVO">ApiResponseListProjectPlanTreeVO</h2>

<a id="schemaapiresponselistprojectplantreevo"></a>
<a id="schema_ApiResponseListProjectPlanTreeVO"></a>
<a id="tocSapiresponselistprojectplantreevo"></a>
<a id="tocsapiresponselistprojectplantreevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "string",
          "projectId": "string",
          "reviewStatus": "string",
          "planStatus": "string",
          "planStartTime": "string",
          "planEndTime": "string",
          "planDuration": 0,
          "actualStartTime": "string",
          "actualEndTime": "string",
          "actualDuration": 0,
          "ownerId": 0,
          "executorId": 0,
          "ownerName": "string",
          "executorName": "string",
          "prePlanId": 0,
          "preSeriNum": "string",
          "seriNum": "string",
          "parentPlanId": 0,
          "changeRemark": "string",
          "reviewOpinion": "string",
          "attachments": [
            {}
          ],
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectPlanTreeVO](#schemaprojectplantreevo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ProjectPlanListVO">ProjectPlanListVO</h2>

<a id="schemaprojectplanlistvo"></a>
<a id="schema_ProjectPlanListVO"></a>
<a id="tocSprojectplanlistvo"></a>
<a id="tocsprojectplanlistvo"></a>

```json
{
  "id": 0,
  "planName": "string",
  "projectId": "string",
  "reviewStatus": "string",
  "planStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "planDuration": 0,
  "actualStartTime": "string",
  "actualEndTime": "string",
  "actualDuration": 0,
  "ownerId": 0,
  "executorId": 0,
  "ownerName": "string",
  "executorName": "string",
  "prePlanId": 0,
  "preSeriNum": "string",
  "seriNum": "string",
  "parentPlanId": 0,
  "changeRemark": "string",
  "reviewOpinion": "string",
  "attachments": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "string",
      "uploaderName": "string",
      "uploadedTime": "string",
      "url": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||计划主键ID|
|planName|string|false|none||计划名称|
|projectId|string|false|none||所属项目ID|
|reviewStatus|string|false|none||审核状态|
|planStatus|string|false|none||计划状态|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划结束时间|
|planDuration|integer|false|none||计划工期（天）|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际结束时间|
|actualDuration|integer|false|none||实际工期（天）|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|ownerName|string|false|none||负责人姓名|
|executorName|string|false|none||执行人姓名|
|prePlanId|integer(int64)|false|none||前置计划ID，如果有依赖关系|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|parentPlanId|integer(int64)|false|none||父级计划ID，用于构建树形结构|
|changeRemark|string|false|none||变更说明|
|reviewOpinion|string|false|none||审核意见|
|attachments|[[AttachmentVO](#schemaattachmentvo)]|false|none||附件列表|

<h2 id="tocS_ApiResponseListProjectPlanListVO">ApiResponseListProjectPlanListVO</h2>

<a id="schemaapiresponselistprojectplanlistvo"></a>
<a id="schema_ApiResponseListProjectPlanListVO"></a>
<a id="tocSapiresponselistprojectplanlistvo"></a>
<a id="tocsapiresponselistprojectplanlistvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectPlanListVO](#schemaprojectplanlistvo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseBoolean">ApiResponseBoolean</h2>

<a id="schemaapiresponseboolean"></a>
<a id="schema_ApiResponseBoolean"></a>
<a id="tocSapiresponseboolean"></a>
<a id="tocsapiresponseboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|boolean|false|none||请求成功时，返回的数据|

<h2 id="tocS_UpdatePlanDTO">UpdatePlanDTO</h2>

<a id="schemaupdateplandto"></a>
<a id="schema_UpdatePlanDTO"></a>
<a id="tocSupdateplandto"></a>
<a id="tocsupdateplandto"></a>

```json
{
  "id": 0,
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||计划ID|
|planStatus|string|false|none||计划状态<br />{@link PlanStatusEnum}|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|prePlanId|integer(int64)|false|none||前置计划ID|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|

#### 枚举值

|属性|值|
|---|---|
|planStatus|NOT_STARTED|
|planStatus|IN_PROGRESS|
|planStatus|COMPLETED|
|planStatus|OVERDUE|
|planStatus|OVERDUE_COMPLETED|

<h2 id="tocS_ApiResponseVoid">ApiResponseVoid</h2>

<a id="schemaapiresponsevoid"></a>
<a id="schema_ApiResponseVoid"></a>
<a id="tocSapiresponsevoid"></a>
<a id="tocsapiresponsevoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|null|false|none||请求成功时，返回的数据|

<h2 id="tocS_Role">Role</h2>

<a id="schemarole"></a>
<a id="schema_Role"></a>
<a id="tocSrole"></a>
<a id="tocsrole"></a>

```json
{
  "id": 0,
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "roleName": "string",
  "remark": "string",
  "menuIds": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|createdTime|string|false|none||创建时间|
|createdBy|integer(int64)|false|none||创建人id|
|updatedTime|string|false|none||修改时间|
|updatedBy|integer(int64)|false|none||修改者id|
|roleName|string|false|none||none|
|remark|string|false|none||none|
|menuIds|[integer]|false|none||用于接收前端传递的权限配置（菜单和按钮ID列表）|

<h2 id="tocS_ApiPageResponseRole">ApiPageResponseRole</h2>

<a id="schemaapipageresponserole"></a>
<a id="schema_ApiPageResponseRole"></a>
<a id="tocSapipageresponserole"></a>
<a id="tocsapipageresponserole"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "roleName": "string",
      "remark": "string",
      "menuIds": [
        0
      ]
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[Role](#schemarole)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_ApiResponseListRole">ApiResponseListRole</h2>

<a id="schemaapiresponselistrole"></a>
<a id="schema_ApiResponseListRole"></a>
<a id="tocSapiresponselistrole"></a>
<a id="tocsapiresponselistrole"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "roleName": "string",
      "remark": "string",
      "menuIds": [
        0
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[Role](#schemarole)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseString">ApiResponseString</h2>

<a id="schemaapiresponsestring"></a>
<a id="schema_ApiResponseString"></a>
<a id="tocSapiresponsestring"></a>
<a id="tocsapiresponsestring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|string|false|none||请求成功时，返回的数据|

<h2 id="tocS_UserListVO">UserListVO</h2>

<a id="schemauserlistvo"></a>
<a id="schema_UserListVO"></a>
<a id="tocSuserlistvo"></a>
<a id="tocsuserlistvo"></a>

```json
{
  "id": 0,
  "username": "string",
  "number": "string",
  "realName": "string",
  "phone": "string",
  "positionId": "string",
  "status": 0,
  "areaId": 0,
  "areaName": "string",
  "roleId": 0,
  "roleName": "string",
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "positionName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||用户ID|
|username|string|false|none||用户名|
|number|string|false|none||工号|
|realName|string|false|none||真实姓名|
|phone|string|false|none||手机号|
|positionId|string|false|none||岗位ID|
|status|integer|false|none||状态|
|areaId|integer(int64)|false|none||区域ID|
|areaName|string|false|none||区域名称|
|roleId|integer(int64)|false|none||角色ID|
|roleName|string|false|none||角色名称|
|createdTime|string|false|none||创建时间|
|createdBy|integer(int64)|false|none||创建人|
|updatedTime|string|false|none||更新时间|
|updatedBy|integer(int64)|false|none||更新人|
|positionName|string|false|none||岗位名称|

<h2 id="tocS_ApiPageResponseUserListVO">ApiPageResponseUserListVO</h2>

<a id="schemaapipageresponseuserlistvo"></a>
<a id="schema_ApiPageResponseUserListVO"></a>
<a id="tocSapipageresponseuserlistvo"></a>
<a id="tocsapipageresponseuserlistvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "username": "string",
      "number": "string",
      "realName": "string",
      "phone": "string",
      "positionId": "string",
      "status": 0,
      "areaId": 0,
      "areaName": "string",
      "roleId": 0,
      "roleName": "string",
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "positionName": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[UserListVO](#schemauserlistvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_User">User</h2>

<a id="schemauser"></a>
<a id="schema_User"></a>
<a id="tocSuser"></a>
<a id="tocsuser"></a>

```json
{
  "id": 0,
  "createdTime": "string",
  "createdBy": 0,
  "updatedTime": "string",
  "updatedBy": 0,
  "username": "string",
  "number": "string",
  "password": "string",
  "realName": "string",
  "phone": "string",
  "positionId": "string",
  "status": 0,
  "roleIds": [
    0
  ],
  "areaIds": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|createdTime|string|false|none||创建时间|
|createdBy|integer(int64)|false|none||创建人id|
|updatedTime|string|false|none||修改时间|
|updatedBy|integer(int64)|false|none||修改者id|
|username|string|false|none||none|
|number|string|false|none||none|
|password|string|false|none||none|
|realName|string|false|none||none|
|phone|string|false|none||none|
|positionId|string|false|none||none|
|status|integer|false|none||none|
|roleIds|[integer]|false|none||none|
|areaIds|[integer]|false|none||none|

<h2 id="tocS_UserInfoDTO">UserInfoDTO</h2>

<a id="schemauserinfodto"></a>
<a id="schema_UserInfoDTO"></a>
<a id="tocSuserinfodto"></a>
<a id="tocsuserinfodto"></a>

```json
{
  "userId": 0,
  "username": "string",
  "account": "string",
  "phone": "string",
  "realName": "string",
  "position": "string",
  "area": "string",
  "roles": [
    0
  ],
  "permissions": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none||none|
|username|string|false|none||none|
|account|string|false|none||none|
|phone|string|false|none||none|
|realName|string|false|none||none|
|position|string|false|none||none|
|area|string|false|none||none|
|roles|[integer]|false|none||none|
|permissions|[integer]|false|none||none|

<h2 id="tocS_ApiResponseUserInfoDTO">ApiResponseUserInfoDTO</h2>

<a id="schemaapiresponseuserinfodto"></a>
<a id="schema_ApiResponseUserInfoDTO"></a>
<a id="tocSapiresponseuserinfodto"></a>
<a id="tocsapiresponseuserinfodto"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "userId": 0,
    "username": "string",
    "account": "string",
    "phone": "string",
    "realName": "string",
    "position": "string",
    "area": "string",
    "roles": [
      0
    ],
    "permissions": [
      0
    ]
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[UserInfoDTO](#schemauserinfodto)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseListUser">ApiResponseListUser</h2>

<a id="schemaapiresponselistuser"></a>
<a id="schema_ApiResponseListUser"></a>
<a id="tocSapiresponselistuser"></a>
<a id="tocsapiresponselistuser"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "createdTime": "string",
      "createdBy": 0,
      "updatedTime": "string",
      "updatedBy": 0,
      "username": "string",
      "number": "string",
      "password": "string",
      "realName": "string",
      "phone": "string",
      "positionId": "string",
      "status": 0,
      "roleIds": [
        0
      ],
      "areaIds": [
        0
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[User](#schemauser)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ProjectDailyListVO">ProjectDailyListVO</h2>

<a id="schemaprojectdailylistvo"></a>
<a id="schema_ProjectDailyListVO"></a>
<a id="tocSprojectdailylistvo"></a>
<a id="tocsprojectdailylistvo"></a>

```json
{
  "id": 0,
  "projectId": "string",
  "reportDate": "string",
  "otherWorkContent": "string",
  "workContent": "string",
  "completionStatus": "string",
  "problemsEncountered": "string",
  "solutions": "string",
  "imageCount": 0,
  "createdTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||日报ID|
|projectId|string|false|none||项目ID|
|reportDate|string|false|none||填报日期|
|otherWorkContent|string|false|none||其他工作内容|
|workContent|string|false|none||当日工作内容|
|completionStatus|string|false|none||完成情况|
|problemsEncountered|string|false|none||遇到的问题|
|solutions|string|false|none||解决方案|
|imageCount|integer|false|none||图片数量|
|createdTime|string|false|none||创建时间|

<h2 id="tocS_ApiPageResponseProjectDailyListVO">ApiPageResponseProjectDailyListVO</h2>

<a id="schemaapipageresponseprojectdailylistvo"></a>
<a id="schema_ApiPageResponseProjectDailyListVO"></a>
<a id="tocSapipageresponseprojectdailylistvo"></a>
<a id="tocsapipageresponseprojectdailylistvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "projectId": "string",
      "reportDate": "string",
      "otherWorkContent": "string",
      "workContent": "string",
      "completionStatus": "string",
      "problemsEncountered": "string",
      "solutions": "string",
      "imageCount": 0,
      "createdTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectDailyListVO](#schemaprojectdailylistvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_PlanProgressVO">PlanProgressVO</h2>

<a id="schemaplanprogressvo"></a>
<a id="schema_PlanProgressVO"></a>
<a id="tocSplanprogressvo"></a>
<a id="tocsplanprogressvo"></a>

```json
{
  "taskId": 0,
  "taskName": "string",
  "taskStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "ownerName": "string",
  "executorName": "string",
  "progressContent": "string",
  "imageName": "string",
  "imageUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|taskId|integer(int64)|false|none||任务ID|
|taskName|string|false|none||任务名称|
|taskStatus|string|false|none||任务状态|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划完成时间|
|ownerName|string|false|none||负责人|
|executorName|string|false|none||执行人姓名|
|progressContent|string|false|none||当日进展|
|imageName|string|false|none||图片名称|
|imageUrl|string|false|none||图片URL|

<h2 id="tocS_ProjectDailyDetailVO">ProjectDailyDetailVO</h2>

<a id="schemaprojectdailydetailvo"></a>
<a id="schema_ProjectDailyDetailVO"></a>
<a id="tocSprojectdailydetailvo"></a>
<a id="tocsprojectdailydetailvo"></a>

```json
{
  "id": 0,
  "projectId": "string",
  "reportDate": "string",
  "reporterName": "string",
  "startContent": "string",
  "otherContent": "string",
  "completionStatus": "string",
  "problems": "string",
  "solutions": "string",
  "dailyImages": [
    "string"
  ],
  "planProgressList": [
    {
      "taskId": 0,
      "taskName": "string",
      "taskStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "ownerName": "string",
      "executorName": "string",
      "progressContent": "string",
      "imageName": "string",
      "imageUrl": "string"
    }
  ],
  "createdTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||日报ID|
|projectId|string|false|none||项目ID|
|reportDate|string|false|none||填报日期|
|reporterName|string|false|none||填报人|
|startContent|string|false|none||开工说明|
|otherContent|string|false|none||其他工作内容|
|completionStatus|string|false|none||完成情况|
|problems|string|false|none||遇到的问题|
|solutions|string|false|none||解决方案|
|dailyImages|[string]|false|none||日报图片列表|
|planProgressList|[[PlanProgressVO](#schemaplanprogressvo)]|false|none||任务进展列表|
|createdTime|string|false|none||创建时间|

<h2 id="tocS_ApiResponseProjectDailyDetailVO">ApiResponseProjectDailyDetailVO</h2>

<a id="schemaapiresponseprojectdailydetailvo"></a>
<a id="schema_ApiResponseProjectDailyDetailVO"></a>
<a id="tocSapiresponseprojectdailydetailvo"></a>
<a id="tocsapiresponseprojectdailydetailvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "projectId": "string",
    "reportDate": "string",
    "reporterName": "string",
    "startContent": "string",
    "otherContent": "string",
    "completionStatus": "string",
    "problems": "string",
    "solutions": "string",
    "dailyImages": [
      "string"
    ],
    "planProgressList": [
      {
        "taskId": 0,
        "taskName": "string",
        "taskStatus": "string",
        "planStartTime": "string",
        "planEndTime": "string",
        "ownerName": "string",
        "executorName": "string",
        "progressContent": "string",
        "imageName": "string",
        "imageUrl": "string"
      }
    ],
    "createdTime": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[ProjectDailyDetailVO](#schemaprojectdailydetailvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseLong">ApiResponseLong</h2>

<a id="schemaapiresponselong"></a>
<a id="schema_ApiResponseLong"></a>
<a id="tocSapiresponselong"></a>
<a id="tocsapiresponselong"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|integer(int64)|false|none||请求成功时，返回的数据|

<h2 id="tocS_PlanProgressDTO">PlanProgressDTO</h2>

<a id="schemaplanprogressdto"></a>
<a id="schema_PlanProgressDTO"></a>
<a id="tocSplanprogressdto"></a>
<a id="tocsplanprogressdto"></a>

```json
{
  "taskId": 0,
  "progressContent": "string",
  "imageUrl": "string",
  "taskStatus": "NOT_STARTED",
  "btId": "string",
  "objectName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|taskId|integer(int64)|true|none||任务ID|
|progressContent|string|true|none||当日进展|
|imageUrl|string|false|none||图片URL|
|taskStatus|string|false|none||任务状态<br />参考 PlanStatusEnum<br />注意：此字段仅用于更新计划状态，不再保存到日报任务进展表中|
|btId|string|false|none||minio 业务类型ID|
|objectName|string|false|none||minio 文件名称|

#### 枚举值

|属性|值|
|---|---|
|taskStatus|NOT_STARTED|
|taskStatus|IN_PROGRESS|
|taskStatus|COMPLETED|
|taskStatus|OVERDUE|
|taskStatus|OVERDUE_COMPLETED|

<h2 id="tocS_ImgDto">ImgDto</h2>

<a id="schemaimgdto"></a>
<a id="schema_ImgDto"></a>
<a id="tocSimgdto"></a>
<a id="tocsimgdto"></a>

```json
{
  "url": "string",
  "btId": 0,
  "objectName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|url|string|false|none||none|
|btId|integer|false|none||none|
|objectName|string|false|none||none|

<h2 id="tocS_ProjectDailySaveDTO">ProjectDailySaveDTO</h2>

<a id="schemaprojectdailysavedto"></a>
<a id="schema_ProjectDailySaveDTO"></a>
<a id="tocSprojectdailysavedto"></a>
<a id="tocsprojectdailysavedto"></a>

```json
{
  "id": 0,
  "projectId": "string",
  "reportDate": "string",
  "reporterId": 0,
  "startDescription": "string",
  "otherWorkContent": "string",
  "completionStatus": "string",
  "problemsEncountered": "string",
  "solutions": "string",
  "dailyImageUrls": [
    {
      "url": "string",
      "btId": 0,
      "objectName": "string"
    }
  ],
  "planProgressList": [
    {
      "taskId": 0,
      "progressContent": "string",
      "imageUrl": "string",
      "taskStatus": "NOT_STARTED",
      "btId": "string",
      "objectName": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||日报ID（新增时为空或不传，编辑时必填）|
|projectId|string|true|none||项目ID|
|reportDate|string|true|none||填报日期|
|reporterId|integer(int64)|true|none||填报人ID|
|startDescription|string|false|none||开工说明|
|otherWorkContent|string|false|none||其他工作内容|
|completionStatus|string|false|none||完成情况|
|problemsEncountered|string|false|none||遇到的问题|
|solutions|string|false|none||解决方案|
|dailyImageUrls|[[ImgDto](#schemaimgdto)]|false|none||日报图片URL数组|
|planProgressList|[[PlanProgressDTO](#schemaplanprogressdto)]|false|none||任务进展列表|

<h2 id="tocS_ProjectPlanDraftTreeVO">ProjectPlanDraftTreeVO</h2>

<a id="schemaprojectplandrafttreevo"></a>
<a id="schema_ProjectPlanDraftTreeVO"></a>
<a id="tocSprojectplandrafttreevo"></a>
<a id="tocsprojectplandrafttreevo"></a>

```json
{
  "id": 0,
  "planName": "string",
  "projectId": "string",
  "reviewStatus": "string",
  "planStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "planDuration": 0,
  "actualStartTime": "string",
  "actualEndTime": "string",
  "actualDuration": 0,
  "ownerId": 0,
  "executorId": 0,
  "ownerName": "string",
  "executorName": "string",
  "prePlanId": 0,
  "preSeriNum": "string",
  "seriNum": "string",
  "parentPlanId": 0,
  "changeRemark": "string",
  "reviewOpinion": "string",
  "attachments": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "string",
      "uploaderName": "string",
      "uploadedTime": "string",
      "url": "string"
    }
  ],
  "children": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "string",
          "projectId": "string",
          "reviewStatus": "string",
          "planStatus": "string",
          "planStartTime": "string",
          "planEndTime": "string",
          "planDuration": 0,
          "actualStartTime": "string",
          "actualEndTime": "string",
          "actualDuration": 0,
          "ownerId": 0,
          "executorId": 0,
          "ownerName": "string",
          "executorName": "string",
          "prePlanId": 0,
          "preSeriNum": "string",
          "seriNum": "string",
          "parentPlanId": 0,
          "changeRemark": "string",
          "reviewOpinion": "string",
          "attachments": [
            {}
          ],
          "children": [
            {}
          ],
          "rejected": true
        }
      ],
      "rejected": true
    }
  ],
  "rejected": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||计划主键ID|
|planName|string|false|none||计划名称|
|projectId|string|false|none||所属项目ID|
|reviewStatus|string|false|none||审核状态|
|planStatus|string|false|none||计划状态|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划结束时间|
|planDuration|integer|false|none||计划工期（天）|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际结束时间|
|actualDuration|integer|false|none||实际工期（天）|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|ownerName|string|false|none||负责人姓名|
|executorName|string|false|none||执行人姓名|
|prePlanId|integer(int64)|false|none||前置计划ID，如果有依赖关系|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|parentPlanId|integer(int64)|false|none||父级计划ID，用于构建树形结构|
|changeRemark|string|false|none||变更说明|
|reviewOpinion|string|false|none||审核意见|
|attachments|[[AttachmentVO](#schemaattachmentvo)]|false|none||附件列表|
|children|[[ProjectPlanDraftTreeVO](#schemaprojectplandrafttreevo)]|false|none||子计划列表，树形结构的子节点|
|rejected|boolean|false|none||是否被驳回|

<h2 id="tocS_ApiResponseListProjectPlanDraftTreeVO">ApiResponseListProjectPlanDraftTreeVO</h2>

<a id="schemaapiresponselistprojectplandrafttreevo"></a>
<a id="schema_ApiResponseListProjectPlanDraftTreeVO"></a>
<a id="tocSapiresponselistprojectplandrafttreevo"></a>
<a id="tocsapiresponselistprojectplandrafttreevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ],
      "children": [
        {
          "id": 0,
          "planName": "string",
          "projectId": "string",
          "reviewStatus": "string",
          "planStatus": "string",
          "planStartTime": "string",
          "planEndTime": "string",
          "planDuration": 0,
          "actualStartTime": "string",
          "actualEndTime": "string",
          "actualDuration": 0,
          "ownerId": 0,
          "executorId": 0,
          "ownerName": "string",
          "executorName": "string",
          "prePlanId": 0,
          "preSeriNum": "string",
          "seriNum": "string",
          "parentPlanId": 0,
          "changeRemark": "string",
          "reviewOpinion": "string",
          "attachments": [
            {}
          ],
          "children": [
            {}
          ],
          "rejected": true
        }
      ],
      "rejected": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectPlanDraftTreeVO](#schemaprojectplandrafttreevo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_SysAreaVO">SysAreaVO</h2>

<a id="schemasysareavo"></a>
<a id="schema_SysAreaVO"></a>
<a id="tocSsysareavo"></a>
<a id="tocssysareavo"></a>

```json
{
  "areaCode": "string",
  "areaName": "string",
  "parentCode": "string",
  "areaLevel": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|areaCode|string|false|none||区域编码（6位数字）<br />用于前端选择和后端存储的标识|
|areaName|string|false|none||区域名称<br />用于前端显示的中文名称|
|parentCode|string|false|none||父级编码<br />用于构建层级关系|
|areaLevel|integer|false|none||行政级别<br />1-省/直辖市，2-市/地区，3-区/县|

<h2 id="tocS_BasePlanQueryDTO">BasePlanQueryDTO</h2>

<a id="schemabaseplanquerydto"></a>
<a id="schema_BasePlanQueryDTO"></a>
<a id="tocSbaseplanquerydto"></a>
<a id="tocsbaseplanquerydto"></a>

```json
{
  "projectId": "string",
  "planId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|true|none||项目ID|
|planId|integer(int64)|true|none||计划ID|

<h2 id="tocS_ApiResponseSysAreaVO">ApiResponseSysAreaVO</h2>

<a id="schemaapiresponsesysareavo"></a>
<a id="schema_ApiResponseSysAreaVO"></a>
<a id="tocSapiresponsesysareavo"></a>
<a id="tocsapiresponsesysareavo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "areaCode": "string",
    "areaName": "string",
    "parentCode": "string",
    "areaLevel": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[SysAreaVO](#schemasysareavo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_PlanDraftListVO">PlanDraftListVO</h2>

<a id="schemaplandraftlistvo"></a>
<a id="schema_PlanDraftListVO"></a>
<a id="tocSplandraftlistvo"></a>
<a id="tocsplandraftlistvo"></a>

```json
{
  "id": 0,
  "planName": "string",
  "projectId": "string",
  "reviewStatus": "string",
  "planStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "planDuration": 0,
  "actualStartTime": "string",
  "actualEndTime": "string",
  "actualDuration": 0,
  "ownerId": 0,
  "executorId": 0,
  "ownerName": "string",
  "executorName": "string",
  "prePlanId": 0,
  "preSeriNum": "string",
  "seriNum": "string",
  "parentPlanId": 0,
  "changeRemark": "string",
  "reviewOpinion": "string",
  "attachments": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "string",
      "uploaderName": "string",
      "uploadedTime": "string",
      "url": "string"
    }
  ],
  "changeType": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||计划主键ID|
|planName|string|false|none||计划名称|
|projectId|string|false|none||所属项目ID|
|reviewStatus|string|false|none||审核状态|
|planStatus|string|false|none||计划状态|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划结束时间|
|planDuration|integer|false|none||计划工期（天）|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际结束时间|
|actualDuration|integer|false|none||实际工期（天）|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|ownerName|string|false|none||负责人姓名|
|executorName|string|false|none||执行人姓名|
|prePlanId|integer(int64)|false|none||前置计划ID，如果有依赖关系|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|parentPlanId|integer(int64)|false|none||父级计划ID，用于构建树形结构|
|changeRemark|string|false|none||变更说明|
|reviewOpinion|string|false|none||审核意见|
|attachments|[[AttachmentVO](#schemaattachmentvo)]|false|none||附件列表|
|changeType|string|false|none||变更类型：新增、编辑、删除|

<h2 id="tocS_ApiResponseListSysAreaVO">ApiResponseListSysAreaVO</h2>

<a id="schemaapiresponselistsysareavo"></a>
<a id="schema_ApiResponseListSysAreaVO"></a>
<a id="tocSapiresponselistsysareavo"></a>
<a id="tocsapiresponselistsysareavo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "areaCode": "string",
      "areaName": "string",
      "parentCode": "string",
      "areaLevel": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[SysAreaVO](#schemasysareavo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseListPlanDraftListVO">ApiResponseListPlanDraftListVO</h2>

<a id="schemaapiresponselistplandraftlistvo"></a>
<a id="schema_ApiResponseListPlanDraftListVO"></a>
<a id="tocSapiresponselistplandraftlistvo"></a>
<a id="tocsapiresponselistplandraftlistvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "planName": "string",
      "projectId": "string",
      "reviewStatus": "string",
      "planStatus": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "planDuration": 0,
      "actualStartTime": "string",
      "actualEndTime": "string",
      "actualDuration": 0,
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "prePlanId": 0,
      "preSeriNum": "string",
      "seriNum": "string",
      "parentPlanId": 0,
      "changeRemark": "string",
      "reviewOpinion": "string",
      "attachments": [
        {
          "id": 0,
          "planId": 0,
          "fileName": "string",
          "uploaderName": "string",
          "uploadedTime": "string",
          "url": "string"
        }
      ],
      "changeType": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[PlanDraftListVO](#schemaplandraftlistvo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_key">key</h2>

<a id="schemakey"></a>
<a id="schema_key"></a>
<a id="tocSkey"></a>
<a id="tocskey"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_PlanCreateDTO">PlanCreateDTO</h2>

<a id="schemaplancreatedto"></a>
<a id="schema_PlanCreateDTO"></a>
<a id="tocSplancreatedto"></a>
<a id="tocsplancreatedto"></a>

```json
{
  "id": 0,
  "projectId": "string",
  "planName": "string",
  "prePlanId": 0,
  "planStatus": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "actualStartTime": "string",
  "actualEndTime": "string",
  "ownerId": 0,
  "executorId": 0,
  "parentPlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "changeRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||计划ID|
|projectId|string|true|none||项目ID|
|planName|string|true|none||计划名称|
|prePlanId|integer(int64)|false|none||前置计划ID|
|planStatus|string|false|none||计划状态|
|planStartTime|string|true|none||计划开始时间|
|planEndTime|string|true|none||计划完成时间|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际完成时间|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|parentPlanId|integer(int64)|false|none||父级计划ID|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|changeRemark|string|false|none||变更说明 - 新增计划的原因说明<br />用于审核时了解新增的具体原因，可选字段|

<h2 id="tocS_ApiResponseMapObject">ApiResponseMapObject</h2>

<a id="schemaapiresponsemapobject"></a>
<a id="schema_ApiResponseMapObject"></a>
<a id="tocSapiresponsemapobject"></a>
<a id="tocsapiresponsemapobject"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "success": true,
    "message": "string",
    "timestamp": 0,
    "service": "string",
    "status": "string"
  },
  "success": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[MapObject](#schemamapobject)|false|none||请求成功时，返回的数据|
|success|boolean|false|none||判断响应结果是否成功|

<h2 id="tocS_PlanUpdateDTO">PlanUpdateDTO</h2>

<a id="schemaplanupdatedto"></a>
<a id="schema_PlanUpdateDTO"></a>
<a id="tocSplanupdatedto"></a>
<a id="tocsplanupdatedto"></a>

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "id": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|planStatus|string|false|none||计划状态|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|prePlanId|integer(int64)|false|none||前置计划ID|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|id|integer(int64)|true|none||计划ID|

#### 枚举值

|属性|值|
|---|---|
|planStatus|NOT_STARTED|
|planStatus|IN_PROGRESS|
|planStatus|COMPLETED|
|planStatus|OVERDUE|
|planStatus|OVERDUE_COMPLETED|

<h2 id="tocS_PlanDraftCreateDTO">PlanDraftCreateDTO</h2>

<a id="schemaplandraftcreatedto"></a>
<a id="schema_PlanDraftCreateDTO"></a>
<a id="tocSplandraftcreatedto"></a>
<a id="tocsplandraftcreatedto"></a>

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "planName": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "changeRemark": "string",
  "id": 0,
  "projectId": "string",
  "actualStartTime": "string",
  "actualEndTime": "string",
  "parentPlanId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|planStatus|string|false|none||计划状态|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|prePlanId|integer(int64)|false|none||前置计划ID|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|planName|string|true|none||计划名称|
|planStartTime|string|true|none||计划开始时间（年月日）|
|planEndTime|string|true|none||计划完成时间（年月日）|
|changeRemark|string|false|none||变更说明|
|id|integer(int64)|false|none||计划ID|
|projectId|string|true|none||项目ID|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际完成时间|
|parentPlanId|integer(int64)|false|none||父级计划ID|

#### 枚举值

|属性|值|
|---|---|
|planStatus|NOT_STARTED|
|planStatus|IN_PROGRESS|
|planStatus|COMPLETED|
|planStatus|OVERDUE|
|planStatus|OVERDUE_COMPLETED|

<h2 id="tocS_PlanTaskDTO">PlanTaskDTO</h2>

<a id="schemaplantaskdto"></a>
<a id="schema_PlanTaskDTO"></a>
<a id="tocSplantaskdto"></a>
<a id="tocsplantaskdto"></a>

```json
{
  "start_date": "string",
  "end_date": "string",
  "number": "string",
  "name": "string",
  "children": [
    {
      "start_date": "string",
      "end_date": "string",
      "number": "string",
      "name": "string",
      "children": [
        {
          "start_date": "string",
          "end_date": "string",
          "number": "string",
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|start_date|string|false|none||计划开始时间|
|end_date|string|false|none||计划结束时间|
|number|string|true|none||任务编号|
|name|string|true|none||任务名称|
|children|[[PlanTaskDTO](#schemaplantaskdto)]|false|none||子任务列表|

<h2 id="tocS_PlanDraftUpdateDTO">PlanDraftUpdateDTO</h2>

<a id="schemaplandraftupdatedto"></a>
<a id="schema_PlanDraftUpdateDTO"></a>
<a id="tocSplandraftupdatedto"></a>
<a id="tocsplandraftupdatedto"></a>

```json
{
  "planStatus": "NOT_STARTED",
  "ownerId": 0,
  "executorId": 0,
  "prePlanId": 0,
  "seriNum": "string",
  "preSeriNum": "string",
  "planName": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "changeRemark": "string",
  "id": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|planStatus|string|false|none||计划状态|
|ownerId|integer(int64)|false|none||负责人ID|
|executorId|integer(int64)|false|none||执行人ID|
|prePlanId|integer(int64)|false|none||前置计划ID|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|
|planName|string|true|none||计划名称|
|planStartTime|string|true|none||计划开始时间（年月日）|
|planEndTime|string|true|none||计划完成时间（年月日）|
|changeRemark|string|false|none||变更说明|
|id|integer(int64)|true|none||计划ID|

#### 枚举值

|属性|值|
|---|---|
|planStatus|NOT_STARTED|
|planStatus|IN_PROGRESS|
|planStatus|COMPLETED|
|planStatus|OVERDUE|
|planStatus|OVERDUE_COMPLETED|

<h2 id="tocS_PlanStageDTO">PlanStageDTO</h2>

<a id="schemaplanstagedto"></a>
<a id="schema_PlanStageDTO"></a>
<a id="tocSplanstagedto"></a>
<a id="tocsplanstagedto"></a>

```json
{
  "stage": "string",
  "tasks": [
    {
      "start_date": "string",
      "end_date": "string",
      "number": "string",
      "name": "string",
      "children": [
        {
          "start_date": "string",
          "end_date": "string",
          "number": "string",
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|stage|string|true|none||阶段名称|
|tasks|[[PlanTaskDTO](#schemaplantaskdto)]|true|none||阶段下的任务列表|

<h2 id="tocS_BatchPlanUpdateDTO">BatchPlanUpdateDTO</h2>

<a id="schemabatchplanupdatedto"></a>
<a id="schema_BatchPlanUpdateDTO"></a>
<a id="tocSbatchplanupdatedto"></a>
<a id="tocsbatchplanupdatedto"></a>

```json
{
  "projectId": "string",
  "createList": "new ArrayList<>()",
  "updateList": "new ArrayList<>()",
  "deleteList": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|true|none||项目ID|
|createList|[[PlanDraftCreateDTO](#schemaplandraftcreatedto)]|false|none||新增计划列表<br />包含需要新增的计划信息，可以为空|
|updateList|[[PlanDraftUpdateDTO](#schemaplandraftupdatedto)]|false|none||编辑计划列表<br />包含需要编辑的现有计划信息，可以为空|
|deleteList|[[PlanDeleteDTO](#schemaplandeletedto)]|false|none||删除计划列表<br />包含需要删除的计划ID信息，可以为空|

<h2 id="tocS_PlanDeleteDTO">PlanDeleteDTO</h2>

<a id="schemaplandeletedto"></a>
<a id="schema_PlanDeleteDTO"></a>
<a id="tocSplandeletedto"></a>
<a id="tocsplandeletedto"></a>

```json
{
  "id": 0,
  "changeRemark": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||计划ID - 要删除的计划草稿ID|
|changeRemark|string|false|none||删除说明 - 可选的删除原因说明<br />用于审核时了解删除的具体原因|

<h2 id="tocS_SavePlanTreeToDraftDTO">SavePlanTreeToDraftDTO</h2>

<a id="schemasaveplantreetodraftdto"></a>
<a id="schema_SavePlanTreeToDraftDTO"></a>
<a id="tocSsaveplantreetodraftdto"></a>
<a id="tocssaveplantreetodraftdto"></a>

```json
{
  "projectId": "string",
  "plan": [
    {
      "stage": "string",
      "tasks": [
        {
          "start_date": "string",
          "end_date": "string",
          "number": "string",
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|true|none||项目ID|
|plan|[[PlanStageDTO](#schemaplanstagedto)]|true|none||计划阶段列表|

<h2 id="tocS_PlanDetailVO">PlanDetailVO</h2>

<a id="schemaplandetailvo"></a>
<a id="schema_PlanDetailVO"></a>
<a id="tocSplandetailvo"></a>
<a id="tocsplandetailvo"></a>

```json
{
  "planId": 0,
  "planName": "string",
  "prePlan": "string",
  "ownerName": "string",
  "executorName": "string",
  "reviewStatus": "string",
  "planStatus": "string",
  "version": 0,
  "planStartTime": "string",
  "planEndTime": "string",
  "planDuration": 0,
  "actualStartTime": "string",
  "actualEndTime": "string",
  "actualDuration": 0,
  "seriNum": "string",
  "preSeriNum": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|planId|integer(int64)|false|none||计划ID|
|planName|string|false|none||计划名称|
|prePlan|string|false|none||前置计划|
|ownerName|string|false|none||负责人|
|executorName|string|false|none||执行人|
|reviewStatus|string|false|none||审核状态|
|planStatus|string|false|none||计划状态|
|version|integer|false|none||计划版本|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划结束时间|
|planDuration|integer|false|none||计划工期（天）|
|actualStartTime|string|false|none||实际开始时间|
|actualEndTime|string|false|none||实际结束时间|
|actualDuration|integer|false|none||实际工期（天）|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|preSeriNum|string|false|none||前置计划序号（如：1、1.1、1.1.2）|

<h2 id="tocS_FileUploadConfigVO">FileUploadConfigVO</h2>

<a id="schemafileuploadconfigvo"></a>
<a id="schema_FileUploadConfigVO"></a>
<a id="tocSfileuploadconfigvo"></a>
<a id="tocsfileuploadconfigvo"></a>

```json
{
  "key": "string",
  "url": "string",
  "expireTime": 0,
  "accessDomain": "string",
  "formData": {
    "key": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||包括前缀的完整文件名称|
|url|string|false|none||上传文件的完整url|
|expireTime|integer(int64)|false|none||上传凭证过期时间戳|
|accessDomain|string|false|none||公共空间需要返回文件的访问域，完整的文件访问地址是 accessDomain + key|
|formData|[MapString](#schemamapstring)|false|none||上传文件时附加表单数据,上传文件时要在表单中附上这些内容|

<h2 id="tocS_ApiResponsePlanDetailVO">ApiResponsePlanDetailVO</h2>

<a id="schemaapiresponseplandetailvo"></a>
<a id="schema_ApiResponsePlanDetailVO"></a>
<a id="tocSapiresponseplandetailvo"></a>
<a id="tocsapiresponseplandetailvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "planId": 0,
    "planName": "string",
    "prePlan": "string",
    "ownerName": "string",
    "executorName": "string",
    "reviewStatus": "string",
    "planStatus": "string",
    "version": 0,
    "planStartTime": "string",
    "planEndTime": "string",
    "planDuration": 0,
    "actualStartTime": "string",
    "actualEndTime": "string",
    "actualDuration": 0,
    "seriNum": "string",
    "preSeriNum": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[PlanDetailVO](#schemaplandetailvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseFileUploadConfigVO">ApiResponseFileUploadConfigVO</h2>

<a id="schemaapiresponsefileuploadconfigvo"></a>
<a id="schema_ApiResponseFileUploadConfigVO"></a>
<a id="tocSapiresponsefileuploadconfigvo"></a>
<a id="tocsapiresponsefileuploadconfigvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "key": "string",
    "url": "string",
    "expireTime": 0,
    "accessDomain": "string",
    "formData": {
      "key": "string"
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[FileUploadConfigVO](#schemafileuploadconfigvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_PlanDynamicVO">PlanDynamicVO</h2>

<a id="schemaplandynamicvo"></a>
<a id="schema_PlanDynamicVO"></a>
<a id="tocSplandynamicvo"></a>
<a id="tocsplandynamicvo"></a>

```json
{
  "dynamicTime": "string",
  "operatorName": "string",
  "dynamicContent": "string",
  "dynamicType": "string",
  "actionSubType": "string",
  "oldValue": "string",
  "newValue": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dynamicTime|string|false|none||动态时间|
|operatorName|string|false|none||操作人|
|dynamicContent|string|false|none||动态内容|
|dynamicType|string|false|none||动态类型（action 操作、report 日报）|
|actionSubType|string|false|none||操作子类型（当动态类型为action时：新增、变更、删除、编辑等；为report时为空）|
|oldValue|string|false|none||修改前|
|newValue|string|false|none||修改后|

<h2 id="tocS_FileUploadConfigDTO">FileUploadConfigDTO</h2>

<a id="schemafileuploadconfigdto"></a>
<a id="schema_FileUploadConfigDTO"></a>
<a id="tocSfileuploadconfigdto"></a>
<a id="tocsfileuploadconfigdto"></a>

```json
{
  "btId": 0,
  "objectName": "string",
  "expireTime": 0,
  "mimeType": "string",
  "maxSize": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|btId|integer|false|none||业务类型id|
|objectName|string|false|none||文件名称|
|expireTime|integer|false|none||访问过期秒数,不传默认30分钟，最长可设置24小时|
|mimeType|string|false|none||文件类型,可以通过前缀声明大类，比如：image/。更多类型说明参考 https://www.runoob.com/http/http-content-type.html 的 Content-Type。不传不限制|
|maxSize|integer|false|none||上传文件的时候用于限制文件最大字节数的，不传默认10MB,最大可设置1GB|

<h2 id="tocS_ApiPageResponsePlanDynamicVO">ApiPageResponsePlanDynamicVO</h2>

<a id="schemaapipageresponseplandynamicvo"></a>
<a id="schema_ApiPageResponsePlanDynamicVO"></a>
<a id="tocSapipageresponseplandynamicvo"></a>
<a id="tocsapipageresponseplandynamicvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "dynamicTime": "string",
      "operatorName": "string",
      "dynamicContent": "string",
      "dynamicType": "string",
      "actionSubType": "string",
      "oldValue": "string",
      "newValue": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[PlanDynamicVO](#schemaplandynamicvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_FileUploadResultVO">FileUploadResultVO</h2>

<a id="schemafileuploadresultvo"></a>
<a id="schema_FileUploadResultVO"></a>
<a id="tocSfileuploadresultvo"></a>
<a id="tocsfileuploadresultvo"></a>

```json
{
  "fileUrl": "string",
  "fileName": "string",
  "fileSize": 0,
  "success": true,
  "errorMessage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|fileUrl|string|false|none||文件访问地址|
|fileName|string|false|none||文件名称（包含路径）|
|fileSize|integer(int64)|false|none||文件大小（字节）|
|success|boolean|false|none||上传是否成功|
|errorMessage|string|false|none||错误信息（如果上传失败）|

<h2 id="tocS_ApiResponseListLong">ApiResponseListLong</h2>

<a id="schemaapiresponselistlong"></a>
<a id="schema_ApiResponseListLong"></a>
<a id="tocSapiresponselistlong"></a>
<a id="tocsapiresponselistlong"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[integer]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ImportWarning">ImportWarning</h2>

<a id="schemaimportwarning"></a>
<a id="schema_ImportWarning"></a>
<a id="tocSimportwarning"></a>
<a id="tocsimportwarning"></a>

```json
{
  "rowNumber": 0,
  "fieldName": "string",
  "warningMessage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|rowNumber|integer|false|none||行号（Excel中的行号，从1开始）|
|fieldName|string|false|none||字段名称|
|warningMessage|string|false|none||警告信息|

<h2 id="tocS_DynamicQueryDTO">DynamicQueryDTO</h2>

<a id="schemadynamicquerydto"></a>
<a id="schema_DynamicQueryDTO"></a>
<a id="tocSdynamicquerydto"></a>
<a id="tocsdynamicquerydto"></a>

```json
{
  "pageNum": 1,
  "pageSize": 20,
  "planId": 0,
  "dynamicType": "ACTION"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||页码（从1开始）|
|pageSize|integer|false|none||每页条数|
|planId|integer(int64)|true|none||计划ID|
|dynamicType|string|false|none||动态类型{@link PlanDynamicTypeEnum}|

#### 枚举值

|属性|值|
|---|---|
|dynamicType|ACTION|
|dynamicType|REPORT|

<h2 id="tocS_ApiResponseFileUploadResultVO">ApiResponseFileUploadResultVO</h2>

<a id="schemaapiresponsefileuploadresultvo"></a>
<a id="schema_ApiResponseFileUploadResultVO"></a>
<a id="tocSapiresponsefileuploadresultvo"></a>
<a id="tocsapiresponsefileuploadresultvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "fileUrl": "string",
    "fileName": "string",
    "fileSize": 0,
    "success": true,
    "errorMessage": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[FileUploadResultVO](#schemafileuploadresultvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ExcelImportResultVO">ExcelImportResultVO</h2>

<a id="schemaexcelimportresultvo"></a>
<a id="schema_ExcelImportResultVO"></a>
<a id="tocSexcelimportresultvo"></a>
<a id="tocsexcelimportresultvo"></a>

```json
{
  "success": true,
  "totalRows": 0,
  "processedRows": 0,
  "warningCount": 0,
  "importTime": "string",
  "projectId": "string",
  "warnings": [
    {
      "rowNumber": 0,
      "fieldName": "string",
      "warningMessage": "string"
    }
  ],
  "errorMessage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||导入是否成功<br />true: 导入完成（可能有警告）<br />false: 导入失败（严重错误）|
|totalRows|integer|false|none||总行数（不包括表头）|
|processedRows|integer|false|none||实际处理的行数|
|warningCount|integer|false|none||警告数量|
|importTime|string|false|none||导入时间|
|projectId|string|false|none||项目ID|
|warnings|[[ImportWarning](#schemaimportwarning)]|false|none||警告详情列表|
|errorMessage|string|false|none||错误信息（导入失败时的错误原因）|

<h2 id="tocS_ReviewInfo">ReviewInfo</h2>

<a id="schemareviewinfo"></a>
<a id="schema_ReviewInfo"></a>
<a id="tocSreviewinfo"></a>
<a id="tocsreviewinfo"></a>

```json
{
  "draftId": 0,
  "reviewOpinion": "string",
  "version": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|draftId|integer(int64)|true|none||草稿ID|
|reviewOpinion|string|false|none||草稿审核意见|
|version|integer|true|none||草稿版本号 - 用于乐观锁控制|

<h2 id="tocS_ApiResponseExcelImportResultVO">ApiResponseExcelImportResultVO</h2>

<a id="schemaapiresponseexcelimportresultvo"></a>
<a id="schema_ApiResponseExcelImportResultVO"></a>
<a id="tocSapiresponseexcelimportresultvo"></a>
<a id="tocsapiresponseexcelimportresultvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "success": true,
    "totalRows": 0,
    "processedRows": 0,
    "warningCount": 0,
    "importTime": "string",
    "projectId": "string",
    "warnings": [
      {
        "rowNumber": 0,
        "fieldName": "string",
        "warningMessage": "string"
      }
    ],
    "errorMessage": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[ExcelImportResultVO](#schemaexcelimportresultvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_WorkItmeReviewDTO">WorkItmeReviewDTO</h2>

<a id="schemaworkitmereviewdto"></a>
<a id="schema_WorkItmeReviewDTO"></a>
<a id="tocSworkitmereviewdto"></a>
<a id="tocsworkitmereviewdto"></a>

```json
{
  "projectId": "string",
  "reviewResult": "string",
  "reviewInfoList": [
    {
      "draftId": 0,
      "reviewOpinion": "string",
      "version": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|true|none||项目ID|
|reviewResult|string|true|none||审核结果 - 本次传入的所有计划的统一审核状态（都通过或都驳回）<br />{@link ReviewStatusEnum}|
|reviewInfoList|[[ReviewInfo](#schemareviewinfo)]|true|none||审核信息列表 - 每个计划的版本和审核意见信息|

<h2 id="tocS_PlanReviewDTO">PlanReviewDTO</h2>

<a id="schemaplanreviewdto"></a>
<a id="schema_PlanReviewDTO"></a>
<a id="tocSplanreviewdto"></a>
<a id="tocsplanreviewdto"></a>

```json
{
  "projectId": "string",
  "reviewResult": "DRAFT",
  "reviewInfoList": [
    {
      "draftId": 0,
      "reviewOpinion": "string",
      "version": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|true|none||项目ID|
|reviewResult|string|true|none||审核结果 - 本次传入的所有计划的统一审核状态（都通过或都驳回）|
|reviewInfoList|[[ReviewInfo](#schemareviewinfo)]|true|none||审核信息列表 - 每个计划的版本和审核意见信息|

#### 枚举值

|属性|值|
|---|---|
|reviewResult|DRAFT|
|reviewResult|PENDING|
|reviewResult|EFFECTIVE|
|reviewResult|REJECTED|

<h2 id="tocS_FileDeleteResultVO">FileDeleteResultVO</h2>

<a id="schemafiledeleteresultvo"></a>
<a id="schema_FileDeleteResultVO"></a>
<a id="tocSfiledeleteresultvo"></a>
<a id="tocsfiledeleteresultvo"></a>

```json
{
  "success": true,
  "message": "string",
  "deletedFileName": "string",
  "errorMessage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||删除是否成功|
|message|string|false|none||结果描述信息|
|deletedFileName|string|false|none||被删除的文件名称|
|errorMessage|string|false|none||错误信息（如果删除失败）|

<h2 id="tocS_ProjectPlanReviewListVO">ProjectPlanReviewListVO</h2>

<a id="schemaprojectplanreviewlistvo"></a>
<a id="schema_ProjectPlanReviewListVO"></a>
<a id="tocSprojectplanreviewlistvo"></a>
<a id="tocsprojectplanreviewlistvo"></a>

```json
{
  "seriNum": "string",
  "draftId": 0,
  "reviewId": 0,
  "planName": "string",
  "ownerId": 0,
  "executorId": 0,
  "ownerName": "string",
  "executorName": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "changeType": "ADD",
  "changeRemark": "string",
  "version": 0,
  "parentPlanId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seriNum|string|false|none||计划序号（如：1、1.1、1.1.2）|
|draftId|integer(int64)|false|none||草稿ID|
|reviewId|integer(int64)|false|none||审核记录ID|
|planName|string|false|none||计划名称|
|ownerId|integer(int64)|false|none||责任人ID|
|executorId|integer(int64)|false|none||执行人ID|
|ownerName|string|false|none||责任人姓名|
|executorName|string|false|none||执行人姓名|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划完成时间|
|changeType|string|false|none||类型{@link ChangeTypeEnum}|
|changeRemark|string|false|none||变更说明|
|version|integer|false|none||草稿版本号|
|parentPlanId|integer(int64)|false|none||父级计划ID|

#### 枚举值

|属性|值|
|---|---|
|changeType|ADD|
|changeType|UPDATE|
|changeType|DELETE|
|changeType|CHANGE|

<h2 id="tocS_ApiResponseFileDeleteResultVO">ApiResponseFileDeleteResultVO</h2>

<a id="schemaapiresponsefiledeleteresultvo"></a>
<a id="schema_ApiResponseFileDeleteResultVO"></a>
<a id="tocSapiresponsefiledeleteresultvo"></a>
<a id="tocsapiresponsefiledeleteresultvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "success": true,
    "message": "string",
    "deletedFileName": "string",
    "errorMessage": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[FileDeleteResultVO](#schemafiledeleteresultvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_FollowedProjectSummaryVO">FollowedProjectSummaryVO</h2>

<a id="schemafollowedprojectsummaryvo"></a>
<a id="schema_FollowedProjectSummaryVO"></a>
<a id="tocSfollowedprojectsummaryvo"></a>
<a id="tocsfollowedprojectsummaryvo"></a>

```json
{
  "todayTaskCount": 0,
  "overdueTaskCount": 0,
  "dueTodayTaskCount": 0,
  "dueThisWeekTaskCount": 0,
  "dueThisMonthTaskCount": 0,
  "totalTaskCount": 0,
  "projectId": "string",
  "projectName": "string",
  "projectManagerName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|todayTaskCount|integer|false|none||当日计划数量（日报口径）|
|overdueTaskCount|integer|false|none||逾期计划数量（状态=逾期）|
|dueTodayTaskCount|integer|false|none||当日到期计划数量（计划结束时间=今天，未过滤状态）|
|dueThisWeekTaskCount|integer|false|none||本周到期计划数量（计划结束时间=本周日）|
|dueThisMonthTaskCount|integer|false|none||本月到期计划数量（计划结束时间=本月最后一天）|
|totalTaskCount|integer|false|none||非根计划数量（总计划数量）|
|projectId|string|false|none||项目ID|
|projectName|string|false|none||项目名称|
|projectManagerName|string|false|none||项目经理姓名|

<h2 id="tocS_FollowedProjectSummaryBaseVO">FollowedProjectSummaryBaseVO</h2>

<a id="schemafollowedprojectsummarybasevo"></a>
<a id="schema_FollowedProjectSummaryBaseVO"></a>
<a id="tocSfollowedprojectsummarybasevo"></a>
<a id="tocsfollowedprojectsummarybasevo"></a>

```json
{
  "todayTaskCount": 0,
  "overdueTaskCount": 0,
  "dueTodayTaskCount": 0,
  "dueThisWeekTaskCount": 0,
  "dueThisMonthTaskCount": 0,
  "totalTaskCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|todayTaskCount|integer|false|none||当日计划数量（日报口径）|
|overdueTaskCount|integer|false|none||逾期计划数量（状态=逾期）|
|dueTodayTaskCount|integer|false|none||当日到期计划数量（计划结束时间=今天，未过滤状态）|
|dueThisWeekTaskCount|integer|false|none||本周到期计划数量（计划结束时间=本周日）|
|dueThisMonthTaskCount|integer|false|none||本月到期计划数量（计划结束时间=本月最后一天）|
|totalTaskCount|integer|false|none||非根计划数量（总计划数量）|

<h2 id="tocS_ApiResponseListProjectPlanReviewListVO">ApiResponseListProjectPlanReviewListVO</h2>

<a id="schemaapiresponselistprojectplanreviewlistvo"></a>
<a id="schema_ApiResponseListProjectPlanReviewListVO"></a>
<a id="tocSapiresponselistprojectplanreviewlistvo"></a>
<a id="tocsapiresponselistprojectplanreviewlistvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "seriNum": "string",
      "draftId": 0,
      "reviewId": 0,
      "planName": "string",
      "ownerId": 0,
      "executorId": 0,
      "ownerName": "string",
      "executorName": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "changeType": "ADD",
      "changeRemark": "string",
      "version": 0,
      "parentPlanId": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectPlanReviewListVO](#schemaprojectplanreviewlistvo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_FileDeleteDTO">FileDeleteDTO</h2>

<a id="schemafiledeletedto"></a>
<a id="schema_FileDeleteDTO"></a>
<a id="tocSfiledeletedto"></a>
<a id="tocsfiledeletedto"></a>

```json
{
  "btId": 0,
  "objectName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|btId|integer|true|none||业务类型ID|
|objectName|string|true|none||文件名称|

<h2 id="tocS_ApiResponseListFollowedProjectSummaryVO">ApiResponseListFollowedProjectSummaryVO</h2>

<a id="schemaapiresponselistfollowedprojectsummaryvo"></a>
<a id="schema_ApiResponseListFollowedProjectSummaryVO"></a>
<a id="tocSapiresponselistfollowedprojectsummaryvo"></a>
<a id="tocsapiresponselistfollowedprojectsummaryvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "todayTaskCount": 0,
      "overdueTaskCount": 0,
      "dueTodayTaskCount": 0,
      "dueThisWeekTaskCount": 0,
      "dueThisMonthTaskCount": 0,
      "totalTaskCount": 0,
      "projectId": "string",
      "projectName": "string",
      "projectManagerName": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[FollowedProjectSummaryVO](#schemafollowedprojectsummaryvo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseFollowedProjectSummaryBaseVO">ApiResponseFollowedProjectSummaryBaseVO</h2>

<a id="schemaapiresponsefollowedprojectsummarybasevo"></a>
<a id="schema_ApiResponseFollowedProjectSummaryBaseVO"></a>
<a id="tocSapiresponsefollowedprojectsummarybasevo"></a>
<a id="tocsapiresponsefollowedprojectsummarybasevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "todayTaskCount": 0,
    "overdueTaskCount": 0,
    "dueTodayTaskCount": 0,
    "dueThisWeekTaskCount": 0,
    "dueThisMonthTaskCount": 0,
    "totalTaskCount": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[FollowedProjectSummaryBaseVO](#schemafollowedprojectsummarybasevo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ProjectManageVO">ProjectManageVO</h2>

<a id="schemaprojectmanagevo"></a>
<a id="schema_ProjectManageVO"></a>
<a id="tocSprojectmanagevo"></a>
<a id="tocsprojectmanagevo"></a>

```json
{
  "id": "string",
  "contractNo": "string",
  "projectName": "string",
  "projectShortName": "string",
  "projectState": "string",
  "projectType": "string",
  "department": "string",
  "biddingDate": "string",
  "winningDate": "string",
  "signDate": "string",
  "projectAddress": "string",
  "firstPartyId": "string",
  "firstPartyName": "string",
  "userSideId": "string",
  "userSideName": "string",
  "otherSidId": "string",
  "otherSidName": "string",
  "province": "string",
  "provinceName": "string",
  "city": "string",
  "cityName": "string",
  "projectDuration": "string",
  "contractAmount": 0,
  "submittedAmount": 0,
  "changeAmount": 0,
  "finalAmount": 0,
  "paymentMethod": "string",
  "paymentFactor": 0,
  "constructionProgress": "string",
  "constructionStartDate": "string",
  "constructionEndDate": "string",
  "acceptDate": "string",
  "guaranteeStartDate": "string",
  "guaranteeEndDate": "string",
  "guarantee": "string",
  "createTime": "string",
  "userId": "string",
  "expireDate": "string",
  "paymentCollection": 0,
  "remark": "string",
  "biddingType": "string",
  "payCountMoney": 0,
  "businessType": "string",
  "areaId": "string",
  "currency": "string",
  "areaSsId": "string",
  "areaShId": "string",
  "contractState": "string",
  "planid": "string",
  "jiduDate": "string",
  "zljjzt": "string",
  "shjjzt": "string",
  "nbysz": "string",
  "pzzt": "string",
  "protaxrate": "string",
  "maxvisionCode": "string",
  "contractType": "string",
  "contractSonNo": "string",
  "sdysktype": "string",
  "collectionCheckState": "string",
  "checkPerson": "string",
  "provisionalSum": 0,
  "userName": "string",
  "checkstate": "string",
  "storagestate": "string",
  "plusMinus": "string",
  "jscollectionCheckState": "string",
  "jscheckPerson": "string",
  "dataProgress": "string",
  "auditProgress": "string",
  "constructPortName": "string",
  "settleRemark": "string",
  "lockState": "string",
  "yqsqbzt": "string",
  "yqsqbz": "string",
  "firstInstanceState": "string",
  "financePerson": "string",
  "financeState": "string",
  "deliveryNotice": "string",
  "crmNo": "string",
  "pccmState": "string",
  "okPay": 0,
  "payIsnot": "string",
  "addTime": "string",
  "numberNos": "string",
  "olldContractNo": "string",
  "zbContent": "string",
  "erpTime": "string",
  "areaIdOld": "string",
  "isStarted": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "startTime": "string",
  "acceptDateNew": "string",
  "projectManagerId": "string",
  "projectManagerName": "string",
  "followed": true,
  "aiGenerationStatus": "NOT_STARTED",
  "version": 0,
  "reviewStatus": "string",
  "versionStatus": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|contractNo|string|false|none||none|
|projectName|string|false|none||none|
|projectShortName|string|false|none||none|
|projectState|string|false|none||none|
|projectType|string|false|none||none|
|department|string|false|none||none|
|biddingDate|string|false|none||none|
|winningDate|string|false|none||none|
|signDate|string|false|none||none|
|projectAddress|string|false|none||none|
|firstPartyId|string|false|none||none|
|firstPartyName|string|false|none||none|
|userSideId|string|false|none||none|
|userSideName|string|false|none||none|
|otherSidId|string|false|none||none|
|otherSidName|string|false|none||none|
|province|string|false|none||none|
|provinceName|string|false|none||none|
|city|string|false|none||none|
|cityName|string|false|none||none|
|projectDuration|string|false|none||none|
|contractAmount|number|false|none||none|
|submittedAmount|number|false|none||none|
|changeAmount|number|false|none||none|
|finalAmount|number|false|none||none|
|paymentMethod|string|false|none||none|
|paymentFactor|number|false|none||none|
|constructionProgress|string|false|none||none|
|constructionStartDate|string|false|none||none|
|constructionEndDate|string|false|none||none|
|acceptDate|string|false|none||none|
|guaranteeStartDate|string|false|none||none|
|guaranteeEndDate|string|false|none||none|
|guarantee|string|false|none||none|
|createTime|string|false|none||none|
|userId|string|false|none||none|
|expireDate|string|false|none||none|
|paymentCollection|number|false|none||none|
|remark|string|false|none||none|
|biddingType|string|false|none||none|
|payCountMoney|number|false|none||none|
|businessType|string|false|none||none|
|areaId|string|false|none||none|
|currency|string|false|none||none|
|areaSsId|string|false|none||none|
|areaShId|string|false|none||none|
|contractState|string|false|none||none|
|planid|string|false|none||none|
|jiduDate|string|false|none||none|
|zljjzt|string|false|none||none|
|shjjzt|string|false|none||none|
|nbysz|string|false|none||none|
|pzzt|string|false|none||none|
|protaxrate|string|false|none||none|
|maxvisionCode|string|false|none||none|
|contractType|string|false|none||none|
|contractSonNo|string|false|none||none|
|sdysktype|string|false|none||none|
|collectionCheckState|string|false|none||none|
|checkPerson|string|false|none||none|
|provisionalSum|number|false|none||none|
|userName|string|false|none||none|
|checkstate|string|false|none||none|
|storagestate|string|false|none||none|
|plusMinus|string|false|none||none|
|jscollectionCheckState|string|false|none||none|
|jscheckPerson|string|false|none||none|
|dataProgress|string|false|none||none|
|auditProgress|string|false|none||none|
|constructPortName|string|false|none||none|
|settleRemark|string|false|none||none|
|lockState|string|false|none||none|
|yqsqbzt|string|false|none||none|
|yqsqbz|string|false|none||none|
|firstInstanceState|string|false|none||none|
|financePerson|string|false|none||none|
|financeState|string|false|none||none|
|deliveryNotice|string|false|none||none|
|crmNo|string|false|none||none|
|pccmState|string|false|none||none|
|okPay|number|false|none||none|
|payIsnot|string|false|none||none|
|addTime|string|false|none||none|
|numberNos|string|false|none||none|
|olldContractNo|string|false|none||none|
|zbContent|string|false|none||none|
|erpTime|string|false|none||none|
|areaIdOld|string|false|none||none|
|isStarted|string|false|none||none|
|planStartTime|string|false|none||none|
|planEndTime|string|false|none||none|
|startTime|string|false|none||none|
|acceptDateNew|string|false|none||none|
|projectManagerId|string|false|none||项目经理id|
|projectManagerName|string|false|none||项目经理姓名|
|followed|boolean|false|none||是否已关注：true=已关注，false=未关注|
|aiGenerationStatus|string|false|none||AI生成状态{@link  AiGenerationStatusEnum}|
|version|integer|false|none||none|
|reviewStatus|string|false|none||none|
|versionStatus|integer|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|aiGenerationStatus|NOT_STARTED|
|aiGenerationStatus|GENERATING|
|aiGenerationStatus|COMPLETED|
|aiGenerationStatus|FAILED|

<h2 id="tocS_FollowedProjectListItemVO">FollowedProjectListItemVO</h2>

<a id="schemafollowedprojectlistitemvo"></a>
<a id="schema_FollowedProjectListItemVO"></a>
<a id="tocSfollowedprojectlistitemvo"></a>
<a id="tocsfollowedprojectlistitemvo"></a>

```json
{
  "projectId": "string",
  "projectName": "string",
  "projectManagerName": "string",
  "totalTaskCount": 0,
  "overdueTaskCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none||项目ID|
|projectName|string|false|none||项目名称|
|projectManagerName|string|false|none||项目经理姓名|
|totalTaskCount|integer|false|none||总计划数量（未删除且排除根计划）|
|overdueTaskCount|integer|false|none||逾期计划数量（状态=逾期）|

<h2 id="tocS_ApiPageResponseProjectManageVO">ApiPageResponseProjectManageVO</h2>

<a id="schemaapipageresponseprojectmanagevo"></a>
<a id="schema_ApiPageResponseProjectManageVO"></a>
<a id="tocSapipageresponseprojectmanagevo"></a>
<a id="tocsapipageresponseprojectmanagevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "contractNo": "string",
      "projectName": "string",
      "projectShortName": "string",
      "projectState": "string",
      "projectType": "string",
      "department": "string",
      "biddingDate": "string",
      "winningDate": "string",
      "signDate": "string",
      "projectAddress": "string",
      "firstPartyId": "string",
      "firstPartyName": "string",
      "userSideId": "string",
      "userSideName": "string",
      "otherSidId": "string",
      "otherSidName": "string",
      "province": "string",
      "provinceName": "string",
      "city": "string",
      "cityName": "string",
      "projectDuration": "string",
      "contractAmount": 0,
      "submittedAmount": 0,
      "changeAmount": 0,
      "finalAmount": 0,
      "paymentMethod": "string",
      "paymentFactor": 0,
      "constructionProgress": "string",
      "constructionStartDate": "string",
      "constructionEndDate": "string",
      "acceptDate": "string",
      "guaranteeStartDate": "string",
      "guaranteeEndDate": "string",
      "guarantee": "string",
      "createTime": "string",
      "userId": "string",
      "expireDate": "string",
      "paymentCollection": 0,
      "remark": "string",
      "biddingType": "string",
      "payCountMoney": 0,
      "businessType": "string",
      "areaId": "string",
      "currency": "string",
      "areaSsId": "string",
      "areaShId": "string",
      "contractState": "string",
      "planid": "string",
      "jiduDate": "string",
      "zljjzt": "string",
      "shjjzt": "string",
      "nbysz": "string",
      "pzzt": "string",
      "protaxrate": "string",
      "maxvisionCode": "string",
      "contractType": "string",
      "contractSonNo": "string",
      "sdysktype": "string",
      "collectionCheckState": "string",
      "checkPerson": "string",
      "provisionalSum": 0,
      "userName": "string",
      "checkstate": "string",
      "storagestate": "string",
      "plusMinus": "string",
      "jscollectionCheckState": "string",
      "jscheckPerson": "string",
      "dataProgress": "string",
      "auditProgress": "string",
      "constructPortName": "string",
      "settleRemark": "string",
      "lockState": "string",
      "yqsqbzt": "string",
      "yqsqbz": "string",
      "firstInstanceState": "string",
      "financePerson": "string",
      "financeState": "string",
      "deliveryNotice": "string",
      "crmNo": "string",
      "pccmState": "string",
      "okPay": 0,
      "payIsnot": "string",
      "addTime": "string",
      "numberNos": "string",
      "olldContractNo": "string",
      "zbContent": "string",
      "erpTime": "string",
      "areaIdOld": "string",
      "isStarted": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "startTime": "string",
      "acceptDateNew": "string",
      "projectManagerId": "string",
      "projectManagerName": "string",
      "followed": true,
      "aiGenerationStatus": "NOT_STARTED",
      "version": 0,
      "reviewStatus": "string",
      "versionStatus": 0
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectManageVO](#schemaprojectmanagevo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_ApiPageResponseFollowedProjectListItemVO">ApiPageResponseFollowedProjectListItemVO</h2>

<a id="schemaapipageresponsefollowedprojectlistitemvo"></a>
<a id="schema_ApiPageResponseFollowedProjectListItemVO"></a>
<a id="tocSapipageresponsefollowedprojectlistitemvo"></a>
<a id="tocsapipageresponsefollowedprojectlistitemvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "projectId": "string",
      "projectName": "string",
      "projectManagerName": "string",
      "totalTaskCount": 0,
      "overdueTaskCount": 0
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[FollowedProjectListItemVO](#schemafollowedprojectlistitemvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_ApiResponseListProjectManageVO">ApiResponseListProjectManageVO</h2>

<a id="schemaapiresponselistprojectmanagevo"></a>
<a id="schema_ApiResponseListProjectManageVO"></a>
<a id="tocSapiresponselistprojectmanagevo"></a>
<a id="tocsapiresponselistprojectmanagevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "contractNo": "string",
      "projectName": "string",
      "projectShortName": "string",
      "projectState": "string",
      "projectType": "string",
      "department": "string",
      "biddingDate": "string",
      "winningDate": "string",
      "signDate": "string",
      "projectAddress": "string",
      "firstPartyId": "string",
      "firstPartyName": "string",
      "userSideId": "string",
      "userSideName": "string",
      "otherSidId": "string",
      "otherSidName": "string",
      "province": "string",
      "provinceName": "string",
      "city": "string",
      "cityName": "string",
      "projectDuration": "string",
      "contractAmount": 0,
      "submittedAmount": 0,
      "changeAmount": 0,
      "finalAmount": 0,
      "paymentMethod": "string",
      "paymentFactor": 0,
      "constructionProgress": "string",
      "constructionStartDate": "string",
      "constructionEndDate": "string",
      "acceptDate": "string",
      "guaranteeStartDate": "string",
      "guaranteeEndDate": "string",
      "guarantee": "string",
      "createTime": "string",
      "userId": "string",
      "expireDate": "string",
      "paymentCollection": 0,
      "remark": "string",
      "biddingType": "string",
      "payCountMoney": 0,
      "businessType": "string",
      "areaId": "string",
      "currency": "string",
      "areaSsId": "string",
      "areaShId": "string",
      "contractState": "string",
      "planid": "string",
      "jiduDate": "string",
      "zljjzt": "string",
      "shjjzt": "string",
      "nbysz": "string",
      "pzzt": "string",
      "protaxrate": "string",
      "maxvisionCode": "string",
      "contractType": "string",
      "contractSonNo": "string",
      "sdysktype": "string",
      "collectionCheckState": "string",
      "checkPerson": "string",
      "provisionalSum": 0,
      "userName": "string",
      "checkstate": "string",
      "storagestate": "string",
      "plusMinus": "string",
      "jscollectionCheckState": "string",
      "jscheckPerson": "string",
      "dataProgress": "string",
      "auditProgress": "string",
      "constructPortName": "string",
      "settleRemark": "string",
      "lockState": "string",
      "yqsqbzt": "string",
      "yqsqbz": "string",
      "firstInstanceState": "string",
      "financePerson": "string",
      "financeState": "string",
      "deliveryNotice": "string",
      "crmNo": "string",
      "pccmState": "string",
      "okPay": 0,
      "payIsnot": "string",
      "addTime": "string",
      "numberNos": "string",
      "olldContractNo": "string",
      "zbContent": "string",
      "erpTime": "string",
      "areaIdOld": "string",
      "isStarted": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "startTime": "string",
      "acceptDateNew": "string",
      "projectManagerId": "string",
      "projectManagerName": "string",
      "followed": true,
      "aiGenerationStatus": "NOT_STARTED",
      "version": 0,
      "reviewStatus": "string",
      "versionStatus": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectManageVO](#schemaprojectmanagevo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_OverduePlanListItemVO">OverduePlanListItemVO</h2>

<a id="schemaoverdueplanlistitemvo"></a>
<a id="schema_OverduePlanListItemVO"></a>
<a id="tocSoverdueplanlistitemvo"></a>
<a id="tocsoverdueplanlistitemvo"></a>

```json
{
  "seriNum": "string",
  "planName": "string",
  "ownerName": "string",
  "executorName": "string",
  "planStartTime": "string",
  "planEndTime": "string",
  "version": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seriNum|string|false|none||计划序号（seriNum）|
|planName|string|false|none||计划名称|
|ownerName|string|false|none||责任人姓名（plan.ownerId 对应用户真实姓名）|
|executorName|string|false|none||执行人姓名（plan.executorId 对应用户真实姓名）|
|planStartTime|string|false|none||计划开始时间|
|planEndTime|string|false|none||计划完成时间|
|version|integer|false|none||变更次数（Plan.version）|

<h2 id="tocS_ProjectManageEditDTO">ProjectManageEditDTO</h2>

<a id="schemaprojectmanageeditdto"></a>
<a id="schema_ProjectManageEditDTO"></a>
<a id="tocSprojectmanageeditdto"></a>
<a id="tocsprojectmanageeditdto"></a>

```json
{
  "id": "string",
  "projectState": "string",
  "projectShortName": "string",
  "projectManagerId": "string",
  "projectManagerName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|projectState|string|false|none||none|
|projectShortName|string|false|none||none|
|projectManagerId|string|false|none||none|
|projectManagerName|string|false|none||none|

<h2 id="tocS_ApiPageResponseOverduePlanListItemVO">ApiPageResponseOverduePlanListItemVO</h2>

<a id="schemaapipageresponseoverdueplanlistitemvo"></a>
<a id="schema_ApiPageResponseOverduePlanListItemVO"></a>
<a id="tocSapipageresponseoverdueplanlistitemvo"></a>
<a id="tocsapipageresponseoverdueplanlistitemvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "seriNum": "string",
      "planName": "string",
      "ownerName": "string",
      "executorName": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "version": 0
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[OverduePlanListItemVO](#schemaoverdueplanlistitemvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_ApiResponseListOverduePlanListItemVO">ApiResponseListOverduePlanListItemVO</h2>

<a id="schemaapiresponselistoverdueplanlistitemvo"></a>
<a id="schema_ApiResponseListOverduePlanListItemVO"></a>
<a id="tocSapiresponselistoverdueplanlistitemvo"></a>
<a id="tocsapiresponselistoverdueplanlistitemvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "seriNum": "string",
      "planName": "string",
      "ownerName": "string",
      "executorName": "string",
      "planStartTime": "string",
      "planEndTime": "string",
      "version": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[OverduePlanListItemVO](#schemaoverdueplanlistitemvo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ProjectVersionVO">ProjectVersionVO</h2>

<a id="schemaprojectversionvo"></a>
<a id="schema_ProjectVersionVO"></a>
<a id="tocSprojectversionvo"></a>
<a id="tocsprojectversionvo"></a>

```json
{
  "projectId": "string",
  "version": 0,
  "versionStatus": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none||none|
|version|integer|false|none||none|
|versionStatus|integer|false|none||none|

<h2 id="tocS_ApiResponseProjectVersionVO">ApiResponseProjectVersionVO</h2>

<a id="schemaapiresponseprojectversionvo"></a>
<a id="schema_ApiResponseProjectVersionVO"></a>
<a id="tocSapiresponseprojectversionvo"></a>
<a id="tocsapiresponseprojectversionvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "projectId": "string",
    "version": 0,
    "versionStatus": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[ProjectVersionVO](#schemaprojectversionvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_ProjectOverviewVO">ProjectOverviewVO</h2>

<a id="schemaprojectoverviewvo"></a>
<a id="schema_ProjectOverviewVO"></a>
<a id="tocSprojectoverviewvo"></a>
<a id="tocsprojectoverviewvo"></a>

```json
{
  "inProgressProjectCount": 0,
  "totalContractAmount": 0,
  "notStartedProjectCount": 0,
  "notStartedTotalAmount": 0,
  "totalReceivedAmount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|inProgressProjectCount|integer|false|none||在建项目总数|
|totalContractAmount|number|false|none||合同总金额|
|notStartedProjectCount|integer|false|none||未开工项目总数|
|notStartedTotalAmount|number|false|none||未开工总金额|
|totalReceivedAmount|number|false|none||回款总金额|

<h2 id="tocS_ApiResponseProjectOverviewVO">ApiResponseProjectOverviewVO</h2>

<a id="schemaapiresponseprojectoverviewvo"></a>
<a id="schema_ApiResponseProjectOverviewVO"></a>
<a id="tocSapiresponseprojectoverviewvo"></a>
<a id="tocsapiresponseprojectoverviewvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "inProgressProjectCount": 0,
    "totalContractAmount": 0,
    "notStartedProjectCount": 0,
    "notStartedTotalAmount": 0,
    "totalReceivedAmount": 0
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[ProjectOverviewVO](#schemaprojectoverviewvo)|false|none||请求成功时，返回的数据|

<h2 id="tocS_MyProjectOverviewVO">MyProjectOverviewVO</h2>

<a id="schemamyprojectoverviewvo"></a>
<a id="schema_MyProjectOverviewVO"></a>
<a id="tocSmyprojectoverviewvo"></a>
<a id="tocsmyprojectoverviewvo"></a>

```json
{
  "projectId": "string",
  "contractNo": "string",
  "projectName": "string",
  "region": "string",
  "projectState": "string",
  "progress": "string",
  "duration": "string",
  "planVersion": "string",
  "planReviewStatus": "string",
  "todayTaskCount": 0,
  "upcomingTaskCount": 0,
  "overdueTaskCount": 0,
  "todayReportStatus": "string",
  "reportId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|projectId|string|false|none||项目ID|
|contractNo|string|false|none||项目编号|
|projectName|string|false|none||项目名称|
|region|string|false|none||区域|
|projectState|string|false|none||项目状态|
|progress|string|false|none||项目进度|
|duration|string|false|none||项目工期|
|planVersion|string|false|none||计划版本|
|planReviewStatus|string|false|none||计划审核状态|
|todayTaskCount|integer|false|none||当日任务数量|
|upcomingTaskCount|integer|false|none||将到期任务|
|overdueTaskCount|integer|false|none||逾期任务|
|todayReportStatus|string|false|none||当日日报状态|
|reportId|integer(int64)|false|none||日报ID（仅当日报已提交时返回）|

<h2 id="tocS_ApiPageResponseMyProjectOverviewVO">ApiPageResponseMyProjectOverviewVO</h2>

<a id="schemaapipageresponsemyprojectoverviewvo"></a>
<a id="schema_ApiPageResponseMyProjectOverviewVO"></a>
<a id="tocSapipageresponsemyprojectoverviewvo"></a>
<a id="tocsapipageresponsemyprojectoverviewvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "projectId": "string",
      "contractNo": "string",
      "projectName": "string",
      "region": "string",
      "projectState": "string",
      "progress": "string",
      "duration": "string",
      "planVersion": "string",
      "planReviewStatus": "string",
      "todayTaskCount": 0,
      "upcomingTaskCount": 0,
      "overdueTaskCount": 0,
      "todayReportStatus": "string",
      "reportId": 0
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[MyProjectOverviewVO](#schemamyprojectoverviewvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_AttachmentUploadQueryDTO">AttachmentUploadQueryDTO</h2>

<a id="schemaattachmentuploadquerydto"></a>
<a id="schema_AttachmentUploadQueryDTO"></a>
<a id="tocSattachmentuploadquerydto"></a>
<a id="tocsattachmentuploadquerydto"></a>

```json
{
  "url": "string",
  "planId": 0,
  "btId": 0,
  "objectName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|url|string|true|none||文件url|
|planId|integer(int64)|true|none||计划ID|
|btId|integer|false|none||minio 业务类型ID|
|objectName|string|false|none||minio 文件名称|

<h2 id="tocS_ApiPageResponseAttachmentVO">ApiPageResponseAttachmentVO</h2>

<a id="schemaapipageresponseattachmentvo"></a>
<a id="schema_ApiPageResponseAttachmentVO"></a>
<a id="tocSapipageresponseattachmentvo"></a>
<a id="tocsapipageresponseattachmentvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "planId": 0,
      "fileName": "string",
      "uploaderName": "string",
      "uploadedTime": "string",
      "url": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[AttachmentVO](#schemaattachmentvo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_AttachmentQueryDTO">AttachmentQueryDTO</h2>

<a id="schemaattachmentquerydto"></a>
<a id="schema_AttachmentQueryDTO"></a>
<a id="tocSattachmentquerydto"></a>
<a id="tocsattachmentquerydto"></a>

```json
{
  "pageNum": 1,
  "pageSize": 20,
  "planId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|false|none||页码（从1开始）|
|pageSize|integer|false|none||每页条数|
|planId|integer(int64)|true|none||计划ID|

<h2 id="tocS_ProjectStakeholderAddExternalDTO">ProjectStakeholderAddExternalDTO</h2>

<a id="schemaprojectstakeholderaddexternaldto"></a>
<a id="schema_ProjectStakeholderAddExternalDTO"></a>
<a id="tocSprojectstakeholderaddexternaldto"></a>
<a id="tocsprojectstakeholderaddexternaldto"></a>

```json
{
  "stakeholderUserName": "string",
  "department": "string",
  "projectRole": "string",
  "mobile": "string",
  "source": "string",
  "contractNo": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|stakeholderUserName|string|false|none||none|
|department|string|false|none||none|
|projectRole|string|false|none||none|
|mobile|string|false|none||none|
|source|string|false|none||none|
|contractNo|string|false|none||none|

<h2 id="tocS_ProjectStakeholderAddInternalDTO">ProjectStakeholderAddInternalDTO</h2>

<a id="schemaprojectstakeholderaddinternaldto"></a>
<a id="schema_ProjectStakeholderAddInternalDTO"></a>
<a id="tocSprojectstakeholderaddinternaldto"></a>
<a id="tocsprojectstakeholderaddinternaldto"></a>

```json
{
  "stakeholderUserId": "string",
  "projectRole": "string",
  "source": "string",
  "contractNo": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|stakeholderUserId|string|false|none||none|
|projectRole|string|false|none||none|
|source|string|false|none||none|
|contractNo|string|false|none||none|

<h2 id="tocS_ProjectStakeholderEditExternalDTO">ProjectStakeholderEditExternalDTO</h2>

<a id="schemaprojectstakeholdereditexternaldto"></a>
<a id="schema_ProjectStakeholderEditExternalDTO"></a>
<a id="tocSprojectstakeholdereditexternaldto"></a>
<a id="tocsprojectstakeholdereditexternaldto"></a>

```json
{
  "id": "string",
  "stakeholderUserName": "string",
  "department": "string",
  "projectRole": "string",
  "mobile": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|stakeholderUserName|string|false|none||none|
|department|string|false|none||none|
|projectRole|string|false|none||none|
|mobile|string|false|none||none|

<h2 id="tocS_ProjectStakeholderEditInternalDTO">ProjectStakeholderEditInternalDTO</h2>

<a id="schemaprojectstakeholdereditinternaldto"></a>
<a id="schema_ProjectStakeholderEditInternalDTO"></a>
<a id="tocSprojectstakeholdereditinternaldto"></a>
<a id="tocsprojectstakeholdereditinternaldto"></a>

```json
{
  "id": "string",
  "stakeholderUserId": "string",
  "projectRole": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|stakeholderUserId|string|false|none||none|
|projectRole|string|false|none||none|

<h2 id="tocS_ProjectStakeholderVO">ProjectStakeholderVO</h2>

<a id="schemaprojectstakeholdervo"></a>
<a id="schema_ProjectStakeholderVO"></a>
<a id="tocSprojectstakeholdervo"></a>
<a id="tocsprojectstakeholdervo"></a>

```json
{
  "id": "string",
  "contractNo": "string",
  "stakeholderType": "string",
  "stakeholderUserId": "string",
  "stakeholderUserName": "string",
  "source": "string",
  "department": "string",
  "projectRole": "string",
  "mobile": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||none|
|contractNo|string|false|none||none|
|stakeholderType|string|false|none||none|
|stakeholderUserId|string|false|none||none|
|stakeholderUserName|string|false|none||none|
|source|string|false|none||none|
|department|string|false|none||none|
|projectRole|string|false|none||none|
|mobile|string|false|none||none|

<h2 id="tocS_ApiPageResponseProjectStakeholderVO">ApiPageResponseProjectStakeholderVO</h2>

<a id="schemaapipageresponseprojectstakeholdervo"></a>
<a id="schema_ApiPageResponseProjectStakeholderVO"></a>
<a id="tocSapipageresponseprojectstakeholdervo"></a>
<a id="tocsapipageresponseprojectstakeholdervo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "contractNo": "string",
      "stakeholderType": "string",
      "stakeholderUserId": "string",
      "stakeholderUserName": "string",
      "source": "string",
      "department": "string",
      "projectRole": "string",
      "mobile": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectStakeholderVO](#schemaprojectstakeholdervo)]|false|none||页记录|
|total|integer(int64)|false|none||总记录数|

<h2 id="tocS_ApiResponseListProjectStakeholderVO">ApiResponseListProjectStakeholderVO</h2>

<a id="schemaapiresponselistprojectstakeholdervo"></a>
<a id="schema_ApiResponseListProjectStakeholderVO"></a>
<a id="tocSapiresponselistprojectstakeholdervo"></a>
<a id="tocsapiresponselistprojectstakeholdervo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "contractNo": "string",
      "stakeholderType": "string",
      "stakeholderUserId": "string",
      "stakeholderUserName": "string",
      "source": "string",
      "department": "string",
      "projectRole": "string",
      "mobile": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[[ProjectStakeholderVO](#schemaprojectstakeholdervo)]|false|none||请求成功时，返回的数据|

<h2 id="tocS_ApiResponseListString">ApiResponseListString</h2>

<a id="schemaapiresponseliststring"></a>
<a id="schema_ApiResponseListString"></a>
<a id="tocSapiresponseliststring"></a>
<a id="tocsapiresponseliststring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码|
|msg|string|false|none||返回消息|
|data|[string]|false|none||请求成功时，返回的数据|

