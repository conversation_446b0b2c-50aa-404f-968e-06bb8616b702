import request from '@/utils/request'

// 文件上传预处理接口
export function uploadFile(file, btId = 50023, options = {}) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('btId', btId)

  // 可选参数
  if (options.objectName) {
    formData.append('objectName', options.objectName)
  }
  if (options.expireTime) {
    formData.append('expireTime', options.expireTime)
  }
  if (options.mimeType) {
    formData.append('mimeType', options.mimeType)
  }
  if (options.maxSize) {
    formData.append('maxSize', options.maxSize)
  }



  return request({
    url: '/file-upload/upload',
    method: 'post',
    data: formData,
    // 注意：对于FormData，不要手动设置Content-Type，让浏览器自动设置boundary
    timeout: 60000 // 1分钟超时
  })
}



// 获取上传配置接口
export function getUploadConfig(btId = 50023, options = {}) {
  const params = {
    btId,
    ...options
  }

  return request({
    url: '/file-upload/config',
    method: 'get',
    params
  })
}


