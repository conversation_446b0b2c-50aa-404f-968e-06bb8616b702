<template>
  <div class="project-detail">
    <div class="block-container">
      <!-- 横向导航栏 -->
      <div class="project-tabs">
        <el-tabs v-model="activeTab" class="project-tabs-nav">
          <!-- 交换位置：项目计划放在前面 -->
          <el-tab-pane label="项目计划" name="plan">
            <ProjectPlan
              :plan-data="planData"
              :project-id="id"
              :loading="planLoading"
              :status-options="statusOptions"
              :read-only="isReadOnlyMode"
              :stakeholders="stakeholdersData"
              :contract-no="contractNo"
              :project-name="projectData.projectName"
              @refresh-data="handleRefreshPlanData"
            />
          </el-tab-pane>

          <el-tab-pane label="基本信息" name="basic">
            <BasicInfo
              :project-data="projectData"
              :loading="projectLoading"
              :getProjectTypeDisplay="getProjectTypeDisplay"
              :getProjectStateDisplay="getProjectStateDisplay"
              :getStatusClass="getStatusClass"
              :read-only="isReadOnlyMode"
              @update:projectData="handleProjectDataUpdate"
            />
          </el-tab-pane>

          <el-tab-pane label="干系人" name="stakeholders">
            <Stakeholders
              :stakeholders="stakeholdersData"
              :project-id="id"
              :contract-no="contractNo"
              :read-only="isReadOnlyMode"
              @update:stakeholders="updateStakeholders"
            />
          </el-tab-pane>

          <el-tab-pane label="日报" name="daily">
            <DailyReports
              :project-id="id"
              :read-only="isReadOnlyMode"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
// 导入子组件
import BasicInfo from './BasicInfo.vue';
import ProjectPlan from './ProjectPlan.vue';
import Stakeholders from './Stakeholders.vue';
import DailyReports from './DailyReports.vue';
// 导入API
import { getWorkItemTree } from '@/api/workItem';
import { getAllUserProjects, getProjectList } from '@/api/project';
import { getDictionaryTypes, getDictionaryMap } from '@/api/dictionary';
import { getPlanStatus } from '@/api/enums';
// 导入用户store
import { useUserStore } from '@/store/modules/user';


const route = useRoute();
const userStore = useUserStore();

// 获取项目ID，使用computed来响应路由变化
const id = computed(() => {
  const projectId = route.params.id;
  return projectId ? String(projectId) : null;
});

// 获取contractNo，优先从查询参数获取，如果没有则从sessionStorage获取
const contractNo = computed(() => {
  const queryContractNo = route.query.contractNo;
  const storedContractNo = sessionStorage.getItem('currentContractNo');

  // 如果URL中有contractNo，存储到sessionStorage并返回
  if (queryContractNo) {
    sessionStorage.setItem('currentContractNo', queryContractNo);
    return queryContractNo;
  }

  // 如果URL中没有，返回存储的值
  return storedContractNo || null;
});

const activeTab = ref('plan');

// 权限控制：根据项目状态判断是否允许编辑
const hasEditPermission = ref(true); // 默认有权限，获取数据后再判断
const isReadOnlyMode = computed(() => !hasEditPermission.value);

// 可编辑的项目状态列表
const editableProjectStates = [
  'xmzt01', // 待验收
  'xmzt03', // 未结算
  'xmzt04', // 正在结算
  'xmzt07', // 项目暂停
  'xmzt12', // 未实施
  'xmzt14'  // 正在实施
];

// 初始化标签页状态
const initializeTab = () => {
  const tabParam = route.query.tab;

  if (tabParam && ['plan', 'basic', 'stakeholders', 'daily'].includes(tabParam)) {
    activeTab.value = tabParam;
  } else {
    activeTab.value = 'plan';
  }
};

// 监听路由查询参数，自动切换标签页
watch(() => route.query.tab, (newTab) => {
  if (newTab && ['plan', 'basic', 'stakeholders', 'daily'].includes(newTab)) {
    activeTab.value = newTab;
  } else if (!newTab) {
    activeTab.value = 'plan';
  }
}, { immediate: true });

// 监听路由变化，重新初始化标签页
watch(() => route.params.id, () => {
  initializeTab();
}, { immediate: true });

// 加载状态
const planLoading = ref(false);
const projectLoading = ref(false);

// 字典数据
const dictionaryTypes = ref({});
const projectTypeDictionary = ref({});
const projectStateDictionary = ref({});
const dictionaryLoaded = ref(false);

// 计划状态枚举
const planStatusEnum = ref({});
const planStatusLoaded = ref(false);

// 项目基本信息数据
const projectData = reactive({
  // 必要的ID字段
  id: '',
  
  // 基本信息
  projectName: '',
  projectShortName: '',
  contractNo: '',
  projectState: '',
  projectType: '',
  
  // 合同信息
  signDate: '',
  acceptDate: '',
  contractAmount: 0,
  payCountMoney: 0,
  
  // 项目相关
  firstPartyName: '',
  provinceName: '',
  cityName: '',
  projectDuration: '',
  projectManagerId: '', // 项目经理ID
  projectManagerName: '', // 项目经理姓名
  
  // 其他信息
  createTime: '',
  remark: ''
});

// 数据转换函数：将API返回的项目计划数据转换为组件需要的格式
const transformWorkItemData = (apiData, level = 1, parentSerialNum = '') => {
  if (!Array.isArray(apiData)) return []

  // 如果是顶层数据且只有一个项目，直接处理其children（跳过项目级别）
  if (level === 1 && apiData.length === 1 && apiData[0].children) {
    return transformWorkItemData(apiData[0].children, level, '')
  }

  return apiData.map((item, index) => {
    // 使用后端返回的seriNum作为序号
    // 如果seriNum为null或空，则不显示序号（阶段类型）
    let serialNumber = '';
    if (item.seriNum !== null && item.seriNum !== undefined && item.seriNum !== '') {
      serialNumber = item.seriNum;
    } else {
      // 对于阶段类型（seriNum为null），不显示序号
      serialNumber = '';
    }
    
    // 先处理子项，确保hasChildren字段正确
    let children = []
    if (item.children && item.children.length > 0) {
      children = transformWorkItemData(item.children, level + 1, serialNumber)
    }
    
    // 强制生成唯一ID - 基于序列号确保每行都有唯一标识
    // 即使原始ID存在，我们也要确保在前端表格中每行都有唯一ID
    const uniqueId = `plan-${serialNumber ? serialNumber.replace(/\./g, '-') : 'no-serial'}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

 // 确保ID唯一性



    // 转换存储序号为显示序号 - 不进行任何转换，直接返回原值
    const convertStorageToDisplaySerial = (storageSerial) => {
      if (!storageSerial) return '';
      // 直接返回原值，不进行任何数值转换
      return storageSerial;
    };

    const transformed = {
      id: uniqueId,
      originalId: item.id, // 保存原始ID用于API调用
      taskName: item.planName || '',
      parentTask: convertStorageToDisplaySerial(item.preSeriNum),
      responsible: item.ownerName || '',
      executorName: item.executorName || '',
      status: getPlanStatusDisplay(item.planStatus),
      planStartDate: item.planStartTime ? item.planStartTime.split(' ')[0] : '',
      planEndDate: item.planEndTime ? item.planEndTime.split(' ')[0] : '',
      planDuration: item.planDuration || 0,
      note: item.changeRemark || '',
      actualStartDate: item.actualStartTime ? item.actualStartTime.split(' ')[0] : '',
      actualEndDate: item.actualEndTime ? item.actualEndTime.split(' ')[0] : '',
      actualDuration: item.actualDuration || 0,
      level: level,
      serialNumber: serialNumber,
      hasChildren: children.length > 0, // 只有真正有子项时才为true
      children: children, // 使用处理后的children数组
      attachments: item.attachments || [],
      reviewStatus: item.reviewStatus || '',
      reviewOpinion: item.reviewOpinion || '',
      // 保存API调用需要的原始字段
      originalData: {
        ownerId: item.ownerId,
        executorId: item.executorId,
        prePlanId: item.prePlanId,
        planStatus: item.planStatus, // 原始状态码
        stageId: item.stageId,
        seriNum: item.seriNum || serialNumber, // 如果API没有序号，使用生成的序号
        preSeriNum: item.preSeriNum
      }
    }

    return transformed
  })
}

// 获取字典类型数据
const getDictionaryTypesData = async () => {
  try {
    const response = await getDictionaryTypes()
    if (response && response.data) {
      dictionaryTypes.value = response.data
      await loadDictionaryMappings()
    }
  } catch (error) {
    console.error('获取字典类型失败:', error)
  }
}

// 加载字典映射
const loadDictionaryMappings = async () => {
  try {


    // 查找项目类型和项目状态对应的字典类型键
    const projectTypeKey = Object.keys(dictionaryTypes.value).find(key =>
      dictionaryTypes.value[key].includes('项目类型') || key.includes('projectType')
    )
    const projectStateKey = Object.keys(dictionaryTypes.value).find(key =>
      dictionaryTypes.value[key].includes('项目状态') || key.includes('projectState')
    )



    // 获取项目类型字典
    if (projectTypeKey) {
      const projectTypeResponse = await getDictionaryMap(projectTypeKey)
      if (projectTypeResponse && projectTypeResponse.data) {
        projectTypeDictionary.value = projectTypeResponse.data
      }
    }

    // 获取项目状态字典
    if (projectStateKey) {
      const projectStateResponse = await getDictionaryMap(projectStateKey)
      if (projectStateResponse && projectStateResponse.data) {
        projectStateDictionary.value = projectStateResponse.data
      }
    }
  } catch (error) {
    console.error('获取字典映射失败:', error)
  } finally {
    dictionaryLoaded.value = true
  }
}

// 转换项目类型码值为显示名称
const getProjectTypeDisplay = (code) => {
  return projectTypeDictionary.value[code] || code;
}

// 转换项目状态码值为显示名称
const getProjectStateDisplay = (code) => {
  return projectStateDictionary.value[code] || code
}

// 获取状态样式类
const getStatusClass = (status) => {
  // 这里可以根据实际的状态码值来设置不同的样式
  const statusMap = {
    '1': 'status-implementing', // 正在实施
    '2': 'status-paused',       // 暂停
    '3': 'status-pending'       // 待验收
  };
  return statusMap[status] || 'status-default';
}

// 获取项目基本信息
const getProjectData = async () => {
  if (!id.value) {
    return;
  }

  projectLoading.value = true
  try {
    let currentProject = null

    // 优先使用contractNo进行精确查询，避免大量分页请求
    if (contractNo.value) {
      try {
        const contractResponse = await getProjectList({
          pageNum: 1,
          pageSize: 1,
          contractNo: contractNo.value
        })

        if (contractResponse && (contractResponse.code === 200 || contractResponse.code === 0) && contractResponse.data && contractResponse.data.length > 0) {
          currentProject = contractResponse.data[0]
        }
      } catch (contractError) {
        // 静默处理contractNo查询失败，继续尝试其他方式
      }
    }

    // 如果contractNo查询失败，尝试用户项目接口
    if (!currentProject) {
      try {
        const userProjectResponse = await getAllUserProjects()

        if (userProjectResponse && (userProjectResponse.code === 200 || userProjectResponse.code === 0) && userProjectResponse.data) {
          // 用户项目接口可能返回projectId字段，需要兼容处理
          currentProject = userProjectResponse.data.find(project =>
            project.id === id.value || project.projectId === id.value
          )
        }
      } catch (userProjectError) {
        // 静默处理用户项目查询失败，继续尝试其他方式
      }
    }

    // 如果在用户项目中没找到，进行常规分页查找
    if (!currentProject) {
      try {
        // 先尝试获取第一页
        let projectListResponse = await getProjectList({
          pageNum: 1,
          pageSize: 1000
        })

        if (projectListResponse && (projectListResponse.code === 200 || projectListResponse.code === 0) && projectListResponse.data) {
          // 在第一页中查找
          currentProject = projectListResponse.data.find(project => project.id === id.value)

          // 如果第一页没找到，继续查找后续页面（限制查找范围）
          if (!currentProject && projectListResponse.total > 1000) {
            const totalPages = Math.ceil(projectListResponse.total / 1000)

            for (let page = 2; page <= Math.min(totalPages, 5); page++) { // 最多查询5页
              try {
                const pageResponse = await getProjectList({
                  pageNum: page,
                  pageSize: 1000
                })

                if (pageResponse && (pageResponse.code === 200 || pageResponse.code === 0) && pageResponse.data) {
                  currentProject = pageResponse.data.find(project => project.id === id.value)

                  if (currentProject) {
                    break
                  }
                }
              } catch (pageError) {
                // 静默处理分页查询失败，继续下一页
              }
            }
          }
        }
      } catch (projectListError) {
        // 静默处理项目列表查询失败
      }
    }

    if (currentProject) {
      // 更新项目基本信息
      Object.assign(projectData, {
        id: currentProject.id || currentProject.projectId, // 兼容不同API返回的ID字段
        projectName: currentProject.projectName || '',
        projectShortName: currentProject.projectShortName || '',
        contractNo: currentProject.contractNo || '',
        projectState: currentProject.projectState || '',
        projectType: currentProject.projectType || '',
        signDate: currentProject.signDate || '',
        acceptDate: currentProject.acceptDate || '',
        contractAmount: currentProject.contractAmount || 0,
        payCountMoney: currentProject.payCountMoney || 0,
        firstPartyName: currentProject.firstPartyName || '',
        provinceName: currentProject.provinceName || '',
        cityName: currentProject.cityName || '',
        projectDuration: currentProject.projectDuration || '',
        projectManagerName: currentProject.projectManagerName || '',
        projectManagerId: currentProject.projectManagerId || '', // 添加projectManagerId
        createTime: currentProject.createTime || '',
        remark: currentProject.remark || ''
      })

      // 根据项目状态判断是否允许编辑
      const projectState = currentProject.projectState || ''
      const canEditByState = editableProjectStates.includes(projectState)
      hasEditPermission.value = canEditByState

      // 如果是只读模式，显示提示信息
      if (!canEditByState) {
        const stateDisplayName = getProjectStateDisplay(projectState) || '未知状态'
        ElMessage.info(`当前项目状态为"${stateDisplayName}"，不允许编辑，您正在以只读模式查看此项目`)
      }
    } else {
      ElMessage.error(`未找到项目信息 (项目ID: ${id.value})`)
      hasEditPermission.value = false
    }
  } catch (error) {
    console.error('获取项目信息失败:', error)
    ElMessage.error('获取项目信息失败：' + (error.message || '网络错误'))
    hasEditPermission.value = false
  } finally {
    projectLoading.value = false
  }
}

// 获取项目计划数据
const getPlanData = async () => {
  if (!id.value) {
    return;
  }

  planLoading.value = true
  try {

    const response = await getWorkItemTree(id.value)
    if (response && (response.code === 200 || response.code === 0) && response.data) {
      planData.value = transformWorkItemData(response.data)
    } else {
      planData.value = getMockPlanData();
      ElMessage.warning('使用Mock数据显示项目计划');
    }
  } catch (error) {
    console.error('获取项目计划失败，使用Mock数据:', error)
    planData.value = getMockPlanData();
    ElMessage.warning('API不可用，使用Mock数据显示项目计划');
  } finally {
    planLoading.value = false
  }
}

// Mock计划数据
const getMockPlanData = () => {
  return [
    {
      id: 'mock-1',
      originalId: 'mock-1',
      taskName: '项目启动阶段',
      parentTask: '',
      responsible: '张三',
      status: '进行中',
      planStartDate: '2024-01-01',
      planEndDate: '2024-01-15',
      planDuration: 15,
      note: 'Mock数据 - 项目启动',
      actualStartDate: '2024-01-02',
      actualEndDate: '',
      actualDuration: 0,
      level: 1,
      serialNumber: '1',
      hasChildren: true,
      children: [
        {
          id: 'mock-1-1',
          originalId: 'mock-1-1',
          taskName: '需求分析',
          parentTask: '1',
          responsible: '李四',
          status: '已完成',
          planStartDate: '2024-01-01',
          planEndDate: '2024-01-05',
          planDuration: 5,
          note: 'Mock数据 - 需求分析',
          actualStartDate: '2024-01-01',
          actualEndDate: '2024-01-04',
          actualDuration: 4,
          level: 2,
          serialNumber: '1.1',
          hasChildren: false,
          children: []
        },
        {
          id: 'mock-1-2',
          originalId: 'mock-1-2',
          taskName: '技术方案设计',
          parentTask: '1',
          responsible: '王五',
          status: '进行中',
          planStartDate: '2024-01-06',
          planEndDate: '2024-01-15',
          planDuration: 10,
          note: 'Mock数据 - 技术方案',
          actualStartDate: '2024-01-06',
          actualEndDate: '',
          actualDuration: 0,
          level: 2,
          serialNumber: '1.2',
          hasChildren: false,
          children: []
        }
      ]
    }
  ];
};



// 项目计划数据
const planData = ref([]);

// 干系人数据
const stakeholdersData = ref([
  {
    id: 1,
    type: '外部人员',
    department: '项目管理部',
    name: '张三',
    role: '项目经理',
    contact: '135****1234',
  },
  {
    id: 2,
    type: '内部人员',
    department: '技术部',
    name: '李四',
    role: '技术负责人',
    contact: '135****5678',
  }
]);

// 更新干系人数据
const updateStakeholders = (newStakeholders) => {
  stakeholdersData.value = newStakeholders;
};

// 获取计划状态枚举
const loadPlanStatusEnum = async () => {
  try {
    const response = await getPlanStatus();
    if (response && (response.code === 200 || response.code === 0) && response.data) {
      planStatusEnum.value = response.data;
      planStatusLoaded.value = true;
    } else {
      // 使用默认映射
      planStatusEnum.value = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '逾期',
        4: '逾期完成'
      };
      planStatusLoaded.value = true;
    }
  } catch (error) {
    // 使用默认映射
    planStatusEnum.value = {
      0: '未开始',
      1: '进行中',
      2: '已完成',
      3: '逾期',
      4: '逾期完成'
    };
    planStatusLoaded.value = true;
  }
};

// 获取计划状态显示文本
const getPlanStatusDisplay = (statusCode) => {
  // 如果状态码为空或未定义，返回默认状态
  if (statusCode === null || statusCode === undefined || statusCode === '') {
    return '未开始';
  }

  // 尝试多种格式的键查找
  if (planStatusLoaded.value && planStatusEnum.value) {
    // 先尝试原始值（可能是数字或字符串）
    if (planStatusEnum.value[statusCode] !== undefined) {
      return planStatusEnum.value[statusCode];
    }

    // 再尝试字符串格式
    const codeStr = String(statusCode);
    if (planStatusEnum.value[codeStr] !== undefined) {
      return planStatusEnum.value[codeStr];
    }

    // 最后尝试数字格式
    const codeNum = Number(statusCode);
    if (!isNaN(codeNum) && planStatusEnum.value[codeNum] !== undefined) {
      return planStatusEnum.value[codeNum];
    }
  }

  // 如果枚举还没加载完成或找不到对应值，使用默认映射
  const defaultStatusMap = {
    0: '未开始',
    1: '进行中',
    2: '已完成',
    3: '逾期',
    4: '逾期完成',
    '0': '未开始',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期',
    '4': '逾期完成'
  };

  return defaultStatusMap[statusCode] || '未开始'; // 默认返回"未开始"而不是原始码值
};

// 创建状态选项数组
const statusOptions = computed(() => {
  if (planStatusLoaded.value && planStatusEnum.value) {
    return Object.entries(planStatusEnum.value).map(([value, label]) => ({
      label,
      value: label // 使用显示文本作为value，因为前端数据已经转换为显示文本
    }));
  }
  // 默认选项
  return [
    { label: '未开始', value: '未开始' },
    { label: '进行中', value: '进行中' },
    { label: '已完成', value: '已完成' },
    { label: '逾期', value: '逾期' },
    { label: '逾期完成', value: '逾期完成' }
  ];
});

// 处理BasicInfo组件更新项目数据
const handleProjectDataUpdate = (updatedProjectData) => {
};

// 处理项目计划数据刷新
const handleRefreshPlanData = async () => {
  try {
    await getPlanData();
    ElMessage.success('数据刷新成功');
  } catch (error) {
    console.error('刷新项目计划数据失败:', error);
    ElMessage.error('数据刷新失败，请重试');
  }
};

// 监听项目ID或contractNo变化，重新获取数据
watch([() => route.params.id, () => route.query.contractNo], async ([newId, newContractNo], [oldId, oldContractNo]) => {
  if (newId && (newId !== oldId || newContractNo !== oldContractNo)) {
    // 清空之前的数据
    planData.value = [];
    Object.assign(projectData, {
      id: '', // 清空id
      projectName: '',
      projectShortName: '',
      contractNo: '',
      projectState: '',
      projectType: '',
      signDate: '',
      acceptDate: '',
      contractAmount: 0,
      payCountMoney: 0,
      firstPartyName: '',
      provinceName: '',
      cityName: '',
      projectDuration: '',
      projectManagerName: '',
      projectManagerId: '', // 清空projectManagerId
      createTime: '',
      remark: ''
    });
    // 获取新数据
    // 如果字典数据还没加载，先加载字典数据
    if (!dictionaryLoaded.value) {
      await getDictionaryTypesData();
    }
    getProjectData();
    getPlanData();
    // 日报数据由DailyReports组件自己获取
  }
}, { immediate: false });

// 组件挂载时获取数据
onMounted(async () => {
  // 初始化标签页状态
  initializeTab();

  // 先获取字典数据和计划状态枚举，再获取项目数据
  await Promise.all([
    getDictionaryTypesData(),
    loadPlanStatusEnum()
  ]);

  // 等待状态枚举加载完成后再获取项目数据
  await getProjectData();
  await getPlanData();
  // 日报数据由DailyReports组件自己获取
});
</script>

<style scoped>
.project-detail {
  width: 100%;
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.block-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.project-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-tabs-nav {
  padding: 0 20px;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 10px 0 0 0;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 15px;
  padding: 0 20px;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  background-color: #5B7BFA;
}

/* 整个Tab的样式 */
:deep(.el-tabs__content) {
  padding: 0;
  height: calc(100% - 40px);
  overflow: hidden; /* 防止出现外部滚动条 */
}

/* 表格区域样式 */
:deep(.tab-content) {
  padding: 0;
  height: calc(100vh - 200px); /* 减少高度偏移，给表格更多空间 */
  min-height: 600px; /* 增加最小高度 */
  margin-top: 0;
  margin-bottom: 10px; /* 减少底部边距 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止出现外部滚动条 */
}

/* 表格统一样式 */
:deep(.el-table) {
  font-size: 13px;
  border-radius: 8px;
  overflow: hidden;
  --el-table-row-height: 28px;
}

/* 表格单元格样式 */
:deep(.el-table__cell) {
  padding: 0 !important;
}

/* 表格行高 */
:deep(.el-table__row) {
  height: 28px !important;
}

/* 表格内容对齐 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
  padding: 0 5px !important;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  justify-content: center;
  height: 100%;
}

/* 表头样式统一 */
:deep(.el-table__header .cell) {
  padding: 0 5px !important;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  justify-content: center;
  text-align: center;
}

/* 左对齐列 */
:deep(.el-table__cell.is-left .cell) {
  justify-content: flex-start;
  text-align: left;
}

/* 链接按钮统一样式 */
:deep(.el-button.is-link) {
  color: #409EFF;
  font-size: 13px;
  padding: 0 8px !important;
}

/* 删除按钮颜色 */
:deep(.el-button.is-link.el-button--danger) {
  color: #F56C6C;
}
</style> 