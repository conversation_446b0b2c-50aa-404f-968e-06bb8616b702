<template>
  <div class="workbench">
    <!-- 项目总览区块 - 固定在顶部 -->
    <ProjectOverview />

    <!-- 项目执行情况区块 - 新增 -->
    <ProjectExecution />

    <!-- 我负责的项目区块 - 可滚动 -->
    <MyProjects />
  </div>
</template>

<script setup>
import ProjectOverview from './ProjectOverview.vue';
import ProjectExecution from './ProjectExecution.vue';
import MyProjects from './MyProjects.vue';
</script>

<style scoped>
.workbench {
  padding: 0;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}
</style> 