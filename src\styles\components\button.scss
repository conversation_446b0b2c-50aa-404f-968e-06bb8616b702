@import "../variables.scss";
@import "../mixins.scss";

/* 全局按钮样式 - 基于table.html参考样式 */

// 按钮全局样式
.el-button {
  padding: 8px 16px !important;
  background: #ffffff !important;
  border: 1px solid rgba(0,0,0,0.08) !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #495057 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-width: 90px !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02) !important;
  position: relative !important;
  overflow: hidden !important;
  text-decoration: none !important;
  height: auto !important;

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  /* 悬浮效果 */
  &:hover {
    background: #f8f9fa !important;
    border-color: rgba(0,123,255,0.2) !important;
    color: #007bff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0,123,255,0.1) !important;

    &::before {
      opacity: 1;
    }
  }

  /* 点击效果 */
  &:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1) !important;
  }

  /* 图标样式 */
  .el-icon {
    font-size: 14px !important;
    width: 16px !important;
    text-align: center !important;
    transition: transform 0.2s ease !important;
    margin-right: 0 !important;
  }

  &:hover .el-icon {
    color: #007bff !important;
    transform: scale(1.1) !important;
  }

  // 按钮大小 - 统一项目详情页面的按钮尺寸
  &.el-button--small {
    padding: 8px 16px !important;  // 与项目计划页面保持一致
    font-size: 14px !important;    // 与项目计划页面保持一致
    min-width: 90px !important;    // 与项目计划页面保持一致
    border-radius: 8px !important; // 与项目计划页面保持一致
    height: auto !important;       // 自动高度，避免固定高度冲突

    .el-icon {
      font-size: 14px !important;  // 与项目计划页面保持一致
      width: 16px !important;      // 与项目计划页面保持一致
    }
  }

  &.el-button--large {
    padding: 12px 20px !important;
    font-size: 16px !important;
    min-width: 110px !important;
    border-radius: 10px !important;

    .el-icon {
      font-size: 16px !important;
      width: 18px !important;
    }
  }

  // 图标按钮
  &.is-circle {
    border-radius: 50% !important;
    padding: 8px !important;
    min-width: 32px !important;
    width: 32px !important;
    height: 32px !important;
  }

  // 圆角按钮
  &.is-round {
    border-radius: 20px !important;
  }

  /* 主要按钮样式 */
  &.el-button--primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border-color: #007bff !important;
    color: #ffffff !important;

    &::before {
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%) !important;
    }

    &:hover {
      background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
      border-color: #0056b3 !important;
      color: #ffffff !important;
    }

    &:hover .el-icon {
      color: #ffffff !important;
    }
  }

  /* 成功按钮样式 */
  &.el-button--success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    border-color: #28a745 !important;
    color: #ffffff !important;

    &:hover {
      background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
      border-color: #1e7e34 !important;
      color: #ffffff !important;
    }

    &:hover .el-icon {
      color: #ffffff !important;
    }
  }

  /* 警告按钮样式 */
  &.el-button--warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    border-color: #ffc107 !important;
    color: #212529 !important;

    &:hover {
      background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%) !important;
      border-color: #e0a800 !important;
      color: #212529 !important;
    }

    &:hover .el-icon {
      color: #212529 !important;
    }
  }

  /* 危险按钮样式 */
  &.el-button--danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;

    &::before {
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%) !important;
    }

    &:hover {
      background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
      border-color: #c82333 !important;
      color: #ffffff !important;
      box-shadow: 0 4px 12px rgba(220,53,69,0.1) !important;
    }

    &:hover .el-icon {
      color: #ffffff !important;
    }

    // 朴素危险按钮特殊处理
    &.is-plain {
      background: #fff5f5 !important;
      border-color: rgba(220,53,69,0.2) !important;
      color: #dc3545 !important;

      &:hover, &:focus {
        background: #fff5f5 !important;
        color: #dc3545 !important;
        border-color: rgba(220,53,69,0.3) !important;
        box-shadow: 0 4px 12px rgba(220,53,69,0.1) !important;
      }

      &:active {
        background: #ffebee !important;
        color: #c82333 !important;
        border-color: rgba(220,53,69,0.4) !important;
      }

      .el-icon {
        color: #dc3545 !important;
      }
    }
  }

  /* 信息按钮样式 */
  &.el-button--info {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    border-color: #6c757d !important;
    color: #ffffff !important;

    &:hover {
      background: linear-gradient(135deg, #5a6268 0%, #545b62 100%) !important;
      border-color: #5a6268 !important;
      color: #ffffff !important;
    }

    &:hover .el-icon {
      color: #ffffff !important;
    }
  }

  /* 文本按钮样式 */
  &.el-button--text {
    background: transparent !important;
    border: none !important;
    color: #007bff !important;
    min-width: auto !important;
    padding: 4px 8px !important;
    box-shadow: none !important;
    height: auto !important;

    &:hover {
      background: rgba(0,123,255,0.1) !important;
      color: #0056b3 !important;
      transform: none !important;
      box-shadow: none !important;
    }

    &.el-button--small {
      font-size: 12px !important;
      padding: 2px 6px !important;
    }
  }

  /* 链接按钮样式 */
  &.is-link,
  &--text {
    background: transparent !important;
    border: none !important;
    color: #007bff !important;
    min-width: auto !important;
    padding: 0 4px !important;
    box-shadow: none !important;
    text-decoration: none !important;
    height: auto !important;
    font-size: 13px !important;

    &:hover {
      background: rgba(0,123,255,0.1) !important;
      color: #0056b3 !important;
      transform: none !important;
      box-shadow: none !important;
    }

    &.el-button--primary {
      color: #007bff !important;

      &:hover, &:focus {
        color: #0056b3 !important;
      }

      &:active {
        color: #004085 !important;
      }
    }

    &.el-button--success {
      color: #28a745 !important;

      &:hover, &:focus {
        color: #1e7e34 !important;
      }

      &:active {
        color: #155724 !important;
      }
    }

    &.el-button--warning {
      color: #ffc107 !important;

      &:hover, &:focus {
        color: #e0a800 !important;
      }

      &:active {
        color: #d39e00 !important;
      }
    }

    &.el-button--danger {
      color: #dc3545 !important;

      &:hover, &:focus {
        color: #c82333 !important;
      }

      &:active {
        color: #bd2130 !important;
      }
    }
  }

  // 朴素按钮通用样式
  &.is-plain {
    background-color: #ffffff !important;
  }

  /* 禁用状态 */
  &:disabled,
  &.is-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;

    &:hover {
      background: #ffffff !important;
      border-color: rgba(0,0,0,0.08) !important;
      color: #495057 !important;
      transform: none !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.02) !important;
    }
  }

  /* 加载状态 */
  &.is-loading {
    opacity: 0.8 !important;
    pointer-events: none !important;

    .el-icon {
      animation: spin 1s linear infinite !important;
    }
  }
}

/* 按钮组样式 */
.el-button-group {
  .el-button {
    margin-left: 0 !important;
    border-radius: 0 !important;

    &:first-child {
      border-top-left-radius: 8px !important;
      border-bottom-left-radius: 8px !important;
    }

    &:last-child {
      border-top-right-radius: 8px !important;
      border-bottom-right-radius: 8px !important;
    }

    &:not(:first-child) {
      margin-left: -1px !important;
    }
  }
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 操作列按钮特殊样式 - 保持紧凑 */
.operation-column .el-button,
.el-table .operation-column .el-button,
.el-table__body .operation-column .el-button {
  padding: 0 4px !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;
  min-width: auto !important;
  font-size: 13px !important;
  border: none !important;
  background: transparent !important;

  &.el-button--primary {
    color: #409EFF !important;

    &:hover {
      color: #66b1ff !important;
      background: rgba(64, 158, 255, 0.1) !important;
    }
  }

  &.el-button--danger {
    color: #F56C6C !important;

    &:hover {
      color: #f78989 !important;
      background: rgba(245, 108, 108, 0.1) !important;
    }
  }

  .el-icon {
    margin-right: 2px !important;
    font-size: 14px !important;
    width: auto !important;
  }
}

/* 操作按钮类 */
.op-btn {
  padding: 0 4px !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;
  min-width: auto !important;
  font-size: 13px !important;
  border: none !important;
  background: transparent !important;

  &.el-button--primary {
    color: #409EFF !important;

    &:hover {
      color: #66b1ff !important;
      background: rgba(64, 158, 255, 0.1) !important;
    }
  }

  &.el-button--danger {
    color: #F56C6C !important;

    &:hover {
      color: #f78989 !important;
      background: rgba(245, 108, 108, 0.1) !important;
    }
  }

  .el-icon {
    margin-right: 2px !important;
    font-size: 14px !important;
    width: auto !important;
  }
}

/* 上传图片按钮专用样式 */
.upload-image-btn {
  background: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
  color: #606266 !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  min-width: 90px !important;
  height: auto !important;

  &:hover {
    border-color: #c0c4cc !important;
    color: #409eff !important;
    background: #ffffff !important;
  }

  &:focus {
    border-color: #c0c4cc !important;
    color: #409eff !important;
    background: #ffffff !important;
  }

  &:active {
    border-color: #c0c4cc !important;
    color: #409eff !important;
    background: #ffffff !important;
  }

  &:disabled {
    background: #f5f7fa !important;
    border-color: #e4e7ed !important;
    color: #c0c4cc !important;
    cursor: not-allowed !important;
  }

  .el-icon {
    color: inherit !important;
    margin-right: 4px !important;
  }
}

/* 上传图片按钮在不同容器中的样式 */
.upload-area .upload-image-btn,
.upload-trigger .upload-image-btn,
.el-upload .upload-image-btn {
  background: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
  color: #606266 !important;

  &:hover {
    border-color: #c0c4cc !important;
    color: #409eff !important;
    background: #ffffff !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-button {
    padding: 6px 12px !important;
    font-size: 12px !important;
    min-width: 70px !important;

    .el-icon {
      font-size: 12px !important;
      width: 14px !important;
    }
  }

  /* 移动端操作列按钮保持紧凑 */
  .operation-column .el-button,
  .op-btn {
    padding: 0 2px !important;
    font-size: 12px !important;
    height: 20px !important;
  }

  /* 移动端上传按钮 */
  .upload-image-btn {
    padding: 6px 12px !important;
    font-size: 12px !important;
    min-width: 70px !important;
  }
}

/* 自定义按钮类 - 与el-button保持一致的样式 */
.toolbar-btn,
.custom-btn,
.action-btn {
  padding: 8px 16px;
  background: #ffffff;
  border: 1px solid rgba(0,0,0,0.08);
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 90px;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  outline: none;

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  /* 悬浮效果 */
  &:hover {
    background: #f8f9fa;
    border-color: rgba(0,123,255,0.2);
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.1);

    &::before {
      opacity: 1;
    }
  }

  /* 点击效果 */
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
  }

  /* 图标样式 */
  i {
    font-size: 14px;
    width: 16px;
    text-align: center;
    transition: transform 0.2s ease;
  }

  &:hover i {
    color: #007bff;
    transform: scale(1.1);
  }

  /* 禁用状态 */
  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      background: #ffffff;
      border-color: rgba(0,0,0,0.08);
      color: #495057;
      transform: none;
      box-shadow: 0 2px 4px rgba(0,0,0,0.02);
    }
  }
}

/* 提交按钮样式 */
.submit-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  border-color: #007bff !important;
  color: #ffffff !important;

  &::before {
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%) !important;
  }

  &:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    border-color: #0056b3 !important;
    color: #ffffff !important;
  }

  &:hover i {
    color: #ffffff !important;
  }
}

/* 取消按钮样式 */
.cancel-btn {
  background: #fff5f5 !important;
  border-color: rgba(220,53,69,0.2) !important;
  color: #dc3545 !important;

  &:hover {
    background: #fff5f5 !important;
    color: #dc3545 !important;
    border-color: rgba(220,53,69,0.3) !important;
    box-shadow: 0 4px 12px rgba(220,53,69,0.1) !important;
  }

  &:hover i {
    color: #dc3545 !important;
  }
}

/* 删除按钮样式 */
.delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  border-color: #dc3545 !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
    border-color: #c82333 !important;
    color: #ffffff !important;
    box-shadow: 0 4px 12px rgba(220,53,69,0.1) !important;
  }

  &:hover i {
    color: #ffffff !important;
  }
}

/* 成功按钮样式 */
.success-btn {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
  border-color: #28a745 !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
    border-color: #1e7e34 !important;
    color: #ffffff !important;
  }

  &:hover i {
    color: #ffffff !important;
  }
}

/* 警告按钮样式 */
.warning-btn {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  border-color: #ffc107 !important;
  color: #212529 !important;

  &:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%) !important;
    border-color: #e0a800 !important;
    color: #212529 !important;
  }

  &:hover i {
    color: #212529 !important;
  }
}

/* 项目详情页面按钮统一样式 - 覆盖各页面的自定义样式 */
.tab-content {
  .el-button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;

    .el-icon {
      font-size: 14px !important;
      width: 16px !important;
      margin-right: 4px !important;
    }
  }

  // 小尺寸按钮也使用统一尺寸
  .el-button--small {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;

    .el-icon {
      font-size: 14px !important;
      width: 16px !important;
      margin-right: 4px !important;
    }
  }

  // 链接按钮和文本按钮保持紧凑
  .el-button--link,
  .el-button--text {
    padding: 0 4px !important;
    min-width: auto !important;
    height: auto !important;
    font-size: 13px !important;
  }

  // 对话框底部按钮
  .dialog-footer .el-button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    height: auto !important;
  }
}

/* 干系人页面特殊样式覆盖 */
.stakeholders-header {
  .el-button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;
  }

  .el-button--small {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;
  }
}

/* 日报页面特殊样式覆盖 */
.daily-reports-header {
  .el-button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;
  }

  .el-button--small {
    padding: 8px 16px !important;
    font-size: 14px !important;
    min-width: 90px !important;
    border-radius: 8px !important;
    height: auto !important;
  }
}

// 审核弹窗底部按钮样式
.audit-dialog, .dialog-footer {
  .el-button--danger.is-plain {
    background: #fff5f5 !important;
    border-color: rgba(220,53,69,0.2) !important;
    color: #dc3545 !important;
  }
}