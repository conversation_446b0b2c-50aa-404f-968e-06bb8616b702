<template>
  <div class="task-table-wrapper">
    <el-table
      ref="taskTableRef"
      :data="taskData"
      border
      row-key="id"
      :default-expand-all="false"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      table-layout="fixed"
      class="task-table"
      :stripe="stripe"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="tableRowClassName"
      :max-height="maxHeight"
      v-loading="loading"
      element-loading-text="正在加载项目计划..."
      @expand-change="handleExpandChange"
    >
      <el-table-column
        label="序号"
        :width="serialColumnWidth"
        align="left"
        class-name="serial-column"
        fixed="left">
        <template #default="scope">
          <div class="serial-cell">
            <span v-if="scope.row.serialNumber && scope.row.serialNumber.trim() !== ''" class="serial-number-text">{{ scope.row.serialNumber }}</span>
            <span v-else class="stage-indicator">
              ●
            </span>
          </div>
        </template>
      </el-table-column>

      <!-- 工作项名称列 - 只读，不可编辑 -->
      <el-table-column prop="taskName" label="工作项名称" :width="taskNameColumnWidth" align="left" show-overflow-tooltip fixed="left">
        <template #default="scope">
          <div class="task-name-cell readonly">
            <span class="task-name-text" :style="getTaskNameStyle(scope.row)">{{ scope.row.taskName }}</span>
          </div>
        </template>
      </el-table-column>
      
      <!-- 前置工作项列 -->
      <el-table-column prop="parentTask" label="前置工作项" width="95" align="center" show-overflow-tooltip>
        <template #default="scope">
          <div
            class="editable-cell"
            @dblclick="(editEnabled && canEditPlan) ? handleEdit(scope.row, 'parentTask') : null"
          >
            <el-input
              v-if="scope.row.editing === 'parentTask'"
              v-model="scope.row.parentTask"
              size="small"
              @blur="handleSave(scope.row)"
              @keyup.enter="handleSave(scope.row)"
              ref="parentTaskInput"
            />
            <div v-else class="pretask-content">
              <template v-if="scope.row.parentTask">
                <span 
                  v-for="(item, index) in scope.row.parentTask.split('、')" 
                  :key="index" 
                  class="pretask-number"
                  @click.stop="handlePretaskClick(item)"
                >
                  {{ item }}{{ index < scope.row.parentTask.split('、').length - 1 ? '、' : '' }}
                </span>
              </template>
              <template v-else>
                {{ scope.row.parentTask }}
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 责任人列 -->
      <el-table-column prop="responsible" label="责任人" width="120" align="center">
        <template #default="scope">
          <el-select
            v-model="scope.row.responsible"
            @change="handleResponsibleChange(scope.row)"
            placeholder="选择责任人"
            size="small"
            style="width: 100%"
            :disabled="!editEnabled || !canEditPlan"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.label"
            />
          </el-select>
        </template>
      </el-table-column>

      <!-- 执行人列 -->
      <el-table-column prop="executorName" label="执行人" width="120" align="center">
        <template #default="scope">
          <el-select
            v-model="scope.row.executorName"
            @change="handleExecutorChange(scope.row)"
            placeholder="选择执行人"
            size="small"
            style="width: 100%"
            :disabled="!editEnabled || !canEditPlan"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.label"
            />
          </el-select>
        </template>
      </el-table-column>

      <!-- 状态列 -->
      <el-table-column prop="status" label="工作项状态" width="95" align="center" class-name="status-column">
        <template #default="{ row }">
          <el-select
            v-model="row.status"
            placeholder="请选择状态"
            size="small"
            :disabled="!editEnabled || !canEditPlan"
            @change="handleStatusChange(row)"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column prop="planStartDate" label="计划开始时间" width="105" align="center" show-overflow-tooltip />
      <el-table-column prop="planEndDate" label="计划完成时间" width="105" align="center" show-overflow-tooltip />
      <el-table-column prop="planDuration" label="计划工期" width="80" align="center" show-overflow-tooltip />
      <el-table-column prop="note" label="备注" width="60" align="center" show-overflow-tooltip />
      <el-table-column prop="actualStartDate" label="实际开始时间" width="105" align="center" show-overflow-tooltip />
      <el-table-column prop="actualEndDate" label="实际完成时间" width="105" align="center" show-overflow-tooltip />
      <el-table-column prop="actualDuration" label="实际工期" width="80" align="center" show-overflow-tooltip />
      
      <!-- 操作列 - 可选 -->
      <el-table-column v-if="showOperations" label="操作" :width="getOperationColumnWidth" align="center" fixed="right">
        <template #default="{ row }">
          <div class="operation-column">
            <slot name="operations" :row="row">
              <!-- 查看按钮 - 所有角色都可以看到 -->
              <el-button type="primary" link size="small" class="op-btn" @click="emit('view', row)">
                <el-icon><Document /></el-icon>查看
              </el-button>
              <!-- 进展提交按钮 - 只有项目经理可以看到 -->
              <el-button v-if="isProjectManager" type="success" link size="small" class="op-btn" @click="emit('update', row)">
                <el-icon><Edit /></el-icon>进展提交
              </el-button>
            </slot>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { defineComponent, h, ref, defineProps, defineEmits, nextTick, watch, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, Edit, FolderOpened } from '@element-plus/icons-vue';
import { updatePlan, updatePlanMock } from '@/api/projectPlan';
import { getStakeholderListAll } from '@/api/stakeholder';
import { getUserList } from '@/api/user';
import { useUserStore } from '@/store/modules/user';

// 表格引用
const taskTableRef = ref(null);

// 序号列宽度
const serialColumnWidth = ref(100);

// 工作项名称列宽度
const taskNameColumnWidth = ref(200);

// 表格配置
const tableProps = {
  // 使用默认的展开图标
};

// 接收的属性
const props = defineProps({
  taskData: {
    type: Array,
    required: true
  },
  maxHeight: {
    type: [String, Number],
    default: 'calc(100vh - 280px)' /* 调整高度计算，确保表格底部完全显示 */
  },
  defaultExpandAll: {
    type: Boolean,
    default: false
  },
  stripe: {
    type: Boolean,
    default: false
  },
  highlightCurrentRow: {
    type: Boolean,
    default: true
  },
  showOperations: {
    type: Boolean,
    default: true
  },
  editEnabled: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  statusOptions: {
    type: Array,
    default: () => [
      { label: '未开始', value: '未开始' },
      { label: '进行中', value: '进行中' },
      { label: '已完成', value: '已完成' },
      { label: '逾期', value: '逾期' },
      { label: '逾期完成', value: '逾期完成' }
    ]
  },
  stakeholders: {
    type: Array,
    default: () => []
  },
  contractNo: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits(['view', 'update', 'save', 'edit', 'pretask-click']);

// 用户store
const userStore = useUserStore();

// 权限控制：判断是否为项目经理
const isProjectManager = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 3; // 项目经理
});

// 权限控制：判断是否为计划员
const isPlanner = computed(() => {
  const userRoleId = userStore.userRole;
  return userRoleId === 2; // 计划员
});

// 权限控制：判断是否可以编辑计划（项目经理或计划员）
const canEditPlan = computed(() => {
  return isProjectManager.value || isPlanner.value;
});

// 动态计算操作列宽度
const getOperationColumnWidth = computed(() => {
  if (isProjectManager.value) {
    return 170; // 项目经理：查看 + 进展提交
  } else {
    return 80; // 其他角色：只有查看
  }
});

// 创建对编辑的引用
const taskNameInput = ref(null);
const parentTaskInput = ref(null);
const responsibleInput = ref(null);

// 用户选项数据
const userOptions = ref([
  { label: '危慧慧', value: '45' }, // 默认用户，防止API失败时有数据可用
]);

// 序号到ID的映射表
const serialToIdMap = ref(new Map());
const lastDataHash = ref('');

// 防重复成功消息
const lastSuccessTime = ref(0);
const showSuccessMessage = (message) => {
  const now = Date.now();
  // 如果距离上次成功消息不到1秒，则不显示
  if (now - lastSuccessTime.value > 1000) {
    ElMessage.success(message);
    lastSuccessTime.value = now;
  }
};

// 将序号字符串转换为数字（如 "1.1" -> 1.1, "1" -> 1）
const convertSerialToNumber = (serialStr) => {
  if (!serialStr) return null;
  const num = parseFloat(serialStr);
  return isNaN(num) ? null : num;
};

// 构建序号到ID的映射表（显示序号 -> ID 和 存储序号 -> ID）
const buildSerialToIdMap = (data) => {
  // 计算数据哈希，避免重复构建
  const dataHash = JSON.stringify(data.map(item => ({
    id: item.originalId,
    serial: item.serialNumber,
    seriNum: item.originalData?.seriNum
  })));

  if (dataHash === lastDataHash.value) {
    return; // 数据没有变化，跳过重建
  }

  lastDataHash.value = dataHash;
  const map = new Map();
  const displayToStorageMap = new Map(); // 显示序号到存储序号的映射

  const traverse = (items) => {
    if (!Array.isArray(items)) return;

    items.forEach(item => {
      // 使用 seriNum 作为显示序号，如果没有则使用 serialNumber
      const displaySerial = item.originalData?.seriNum || item.serialNumber;

      if (displaySerial && item.originalId) {
        // 显示序号 -> ID 的映射
        map.set(displaySerial, item.originalId);

        // 显示序号 -> 存储序号 的映射（现在显示序号就是存储序号）
        if (item.originalData?.seriNum !== undefined) {
          displayToStorageMap.set(displaySerial, item.originalData.seriNum);
        }
      }

      if (item.children && item.children.length > 0) {
        traverse(item.children);
      }
    });
  };

  traverse(data);
  serialToIdMap.value = map;



  // 将存储序号映射保存到全局变量
  window.displayToStorageMap = displayToStorageMap;
};

// 从干系人接口加载用户选项
const loadUserOptions = async () => {
  try {
    // 优先使用props中的contractNo，如果没有则从sessionStorage获取
    const contractNo = props.contractNo || sessionStorage.getItem('currentContractNo');

    if (!contractNo) {
      console.warn('合同号不存在，无法获取干系人列表');
      // 如果没有合同号，使用默认数据
      userOptions.value = [
        { label: '危慧慧', value: '45' }
      ];
      return;
    }

    const response = await getStakeholderListAll({ contractNo: contractNo });

    if (response && response.data && response.data.length > 0) {
      const users = response.data.map(stakeholder => ({
        label: stakeholder.stakeholderUserName || stakeholder.name,
        value: String(stakeholder.id)
      }));
      userOptions.value = users;
    } else {
      // 如果没有干系人数据，使用默认数据
      userOptions.value = [
        { label: '危慧慧', value: '45' }
      ];
    }
  } catch (error) {
    console.error('加载干系人选项失败:', error);
    ElMessage.warning('加载干系人选项失败，使用默认数据');
    userOptions.value = [
      { label: '危慧慧', value: '45' }
    ];
  }
};

// 通过stakeholderUserName查询用户ID
const getUserIdByName = async (stakeholderUserName) => {
  try {
    if (!stakeholderUserName) {
      return null;
    }

    // 调用用户列表接口，使用stakeholderUserName作为realName查询
    const response = await getUserList({
      pageNum: 1,
      pageSize: 100,
      realName: stakeholderUserName
    });

    if (response && response.data && response.data.length > 0) {
      // 查找完全匹配的用户
      const matchedUser = response.data.find(user =>
        user.realName === stakeholderUserName
      );

      if (matchedUser) {
        return matchedUser.id;
      }
    }

    console.warn(`未找到用户: ${stakeholderUserName}`);
    return null;
  } catch (error) {
    console.error('查询用户ID失败:', error);
    return null;
  }
};

// 行样式设置
function tableRowClassName({ row }) {
  const hasChildren = row.hasChildren ? 'has-children' : 'no-children';
  const level = getLevelFromSerialNumber(row.serialNumber || '');
  const levelClass = `level-${level}`;
  return `tree-row tree-row-${row.id} ${hasChildren} ${levelClass}`;
}

// 处理双击编辑
const handleEdit = (row, field) => {
  if (!props.editEnabled) return;

  // 权限检查：只有项目经理和计划员才能编辑
  if (!canEditPlan.value) {
    ElMessage.error('您没有权限编辑项目计划');
    return;
  }

  // 设置当前编辑的字段
  row.editing = field;
  
  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    // 根据不同字段聚焦对应的输入框
    if (field === 'taskName' && taskNameInput.value) {
      taskNameInput.value.focus();
    } else if (field === 'parentTask' && parentTaskInput.value) {
      parentTaskInput.value.focus();
    }
    // 责任人现在是下拉框，不需要聚焦逻辑
  });
  
  // 触发编辑事件
  emit('edit', { row, field });
};

// 状态显示文本到状态码的映射
const getStatusCode = (statusText) => {
  const statusMap = {
    '未开始': '0',
    '进行中': '1',
    '已完成': '2',
    '逾期': '3',
    '逾期完成': '4'
  };
  return statusMap[statusText] || '0';
};

// 保存编辑后的值
const handleSave = async (row) => {
  // 清除编辑状态
  const editedField = row.editing;
  row.editing = '';

  // 调用API更新数据
  await updatePlanData(row, editedField);

  // 触发保存事件
  emit('save', { row, field: editedField });
};

// 更新计划数据
const updatePlanData = async (row, field) => {
  if (!row.originalId || !row.originalData) {
    ElMessage.error('缺少必要的数据，无法更新');
    return;
  }

  try {
    // 数据验证和转换
    const originalId = row.originalId;
    if (!originalId) {
      ElMessage.error('无效的计划ID');
      return;
    }

    // 准备更新数据 - 保持字符串格式避免大整数精度丢失
    const updateData = {
      id: originalId, // 保持字符串格式
      planStatus: "1", // 固定使用状态"1"
      prePlanId: row.originalData.prePlanId || null, // 保持字符串格式
      seriNum: row.originalData.seriNum || row.serialNumber || "",
      preSeriNum: row.originalData.preSeriNum || ""
    };

    // 始终包含ownerId和executorId，确保数据完整性
    updateData.ownerId = row.originalData.ownerId;
    updateData.executorId = row.originalData.executorId;

    // 只有在有stageId时才添加，避免发送null值
    if (row.originalData.stageId || row.originalData.parentPlanId) {
      updateData.stageId = row.originalData.stageId || row.originalData.parentPlanId;
    }



    // 根据编辑的字段更新对应的值
    if (field === 'status') {
      updateData.planStatus = getStatusCode(row.status);
    } else if (field === 'responsible') {
      // 责任人更新：通过stakeholderUserName查询用户ID
      const userId = await getUserIdByName(row.responsible);
      if (userId) {
        updateData.ownerId = userId;
      } else {
        ElMessage.error('未找到对应的责任人用户ID');
        return;
      }
    } else if (field === 'executorName') {
      // 执行人更新：通过stakeholderUserName查询用户ID
      const userId = await getUserIdByName(row.executorName);
      if (userId) {
        updateData.executorId = userId;
      } else {
        ElMessage.error('未找到对应的执行人用户ID');
        return;
      }
    } else if (field === 'parentTask') {
      // 前置任务更新：处理序号到ID的映射
      const preSeriNums = row.parentTask ? row.parentTask.split('、').map(s => s.trim()).filter(s => s) : [];

      if (preSeriNums.length > 0) {
        // 查找第一个前置任务的ID（目前API只支持单个前置任务）
        const firstPreSeriNum = preSeriNums[0];
        const prePlanId = serialToIdMap.value.get(firstPreSeriNum);

        if (prePlanId) {
          // 直接使用用户输入的序号作为前置序号（seriNum）
          // 不需要进行任何转换，因为现在使用的是真实的seriNum
          updateData.preSeriNum = firstPreSeriNum;
          updateData.prePlanId = prePlanId; // 保持字符串格式

        } else {
          // 如果找不到ID映射，只设置序号，不设置ID
          updateData.preSeriNum = firstPreSeriNum;
          updateData.prePlanId = null;
        }
      } else {
        // 清空前置任务
        updateData.preSeriNum = "";
        updateData.prePlanId = null;
      }
    }

    // 调用API
    let response;
    try {
      response = await updatePlan(updateData);

      if (response && (response.code === 200 || response.code === 0)) {
        // 检查业务逻辑是否成功
        if (response.success === false || response.data === false) {
          ElMessage.warning('更新失败：' + (response.msg || '业务验证失败，请检查数据是否正确'));
          return;
        }

        // 只在用户主动编辑时显示成功消息，避免页面加载时的干扰
        if (field && (field === 'parentTask' || field === 'responsible' || field === 'executorName' || field === 'status')) {
          showSuccessMessage('更新成功');
        }
        addSuccessFeedback(row);

        // 更新本地数据，确保界面显示最新状态
        if (field === 'parentTask') {
          row.originalData.preSeriNum = updateData.preSeriNum;
          row.originalData.prePlanId = updateData.prePlanId;
        } else if (field === 'responsible') {
          row.originalData.ownerId = updateData.ownerId;
        } else if (field === 'executorName') {
          row.originalData.executorId = updateData.executorId;
        } else if (field === 'status') {
          row.originalData.planStatus = updateData.planStatus;
        }

        return;
      } else {
        ElMessage.error('API更新失败：' + (response?.msg || response?.message || '未知错误'));
        return;
      }
    } catch (realApiError) {
      ElMessage.error('API调用失败：' + (realApiError.message || '网络错误'));
      return;

      // 注释掉Mock fallback，确保我们看到真实的错误
      // try {
      //   response = await updatePlanMock(updateData);
      //   if (response && (response.code === 200 || response.code === 0)) {
      //     ElMessage.success('Mock更新成功');
      //     addSuccessFeedback(row);
      //   } else {
      //     ElMessage.error('更新失败：' + (response?.msg || '未知错误'));
      //   }
      // } catch (mockError) {
      //   ElMessage.error('更新失败：' + (mockError.message || '网络错误'));
      // }
    }
  } catch (error) {
    ElMessage.error('更新失败：' + (error.message || '网络错误'));
  }
};

// 添加成功反馈
const addSuccessFeedback = (row) => {
  // 简单的成功反馈：临时添加CSS类
  nextTick(() => {
    const tableRows = document.querySelectorAll('.task-table .el-table__row');
    tableRows.forEach(tr => {
      const cells = tr.querySelectorAll('td');
      if (cells.length > 0) {
        const firstCell = cells[0];
        if (firstCell.textContent.includes(row.serialNumber)) {
          tr.classList.add('update-success');
          setTimeout(() => {
            tr.classList.remove('update-success');
          }, 2000);
        }
      }
    });
  });
};

// 处理状态变化
const handleStatusChange = async (row) => {
  if (!props.editEnabled) return;

  // 权限检查：只有项目经理和计划员才能修改状态
  if (!canEditPlan.value) {
    ElMessage.error('您没有权限修改工作项状态');
    return;
  }

  // 直接调用更新API
  await updatePlanData(row, 'status');
};

// 处理责任人变化
const handleResponsibleChange = async (row) => {
  if (!props.editEnabled) return;

  // 权限检查：只有项目经理和计划员才能修改责任人
  if (!canEditPlan.value) {
    ElMessage.error('您没有权限修改责任人');
    return;
  }

  // 直接调用更新API
  await updatePlanData(row, 'responsible');
};

// 处理执行人变化
const handleExecutorChange = async (row) => {
  if (!props.editEnabled) return;

  // 权限检查：只有项目经理和计划员才能修改执行人
  if (!canEditPlan.value) {
    ElMessage.error('您没有权限修改执行人');
    return;
  }

  // 直接调用更新API
  await updatePlanData(row, 'executorName');
};

// 监听合同号变化，重新加载用户选项
watch(() => props.contractNo, (newContractNo) => {
  if (newContractNo) {
    loadUserOptions();
  }
}, { immediate: true });

// 组件挂载时初始化
onMounted(() => {
  loadUserOptions();
});

// 处理前置任务点击 - 已在下方定义，删除重复

// 处理展开/收起状态变化
const handleExpandChange = (row, expanded) => {
  // 检查行是否有真正的子项
  if (!hasRealChildren(row)) {
    return;
  }
};

// 检查行是否有真正的子项
const hasRealChildren = (row) => {
  return row.children && Array.isArray(row.children) && row.children.length > 0;
};

// 计算序号列宽度
const calculateSerialColumnWidth = (data) => {
  if (!data || data.length === 0) return 100;

  let maxLength = 0;

  // 递归查找最长的序号
  const findMaxSerialLength = (items) => {
    items.forEach(item => {
      if (item.serialNumber) {
        maxLength = Math.max(maxLength, item.serialNumber.length);
      }
      if (item.children && item.children.length > 0) {
        findMaxSerialLength(item.children);
      }
    });
  };

  findMaxSerialLength(data);

  // 基础宽度 + 每个字符的宽度 + 展开图标宽度 + 内边距
  // 中文字符约14px，数字和点约8px，展开图标约20px，内边距约20px
  const baseWidth = 60; // 基础宽度
  const charWidth = 12; // 平均字符宽度
  const expandIconWidth = 20; // 展开图标宽度
  const padding = 20; // 内边距

  const calculatedWidth = baseWidth + (maxLength * charWidth) + expandIconWidth + padding;

  // 设置最小宽度100px，最大宽度200px
  const finalWidth = Math.max(100, Math.min(200, calculatedWidth));
  return finalWidth;
};

// 计算工作项名称列宽度
const calculateTaskNameColumnWidth = (data) => {
  if (!data || data.length === 0) return 200;

  let maxLength = 0;
  let maxLevel = 0;

  // 递归查找最长的工作项名称和最大层级
  const findMaxTaskNameLength = (items, level = 0) => {
    items.forEach(item => {
      if (item.taskName) {
        // 计算实际显示长度（考虑中文字符）
        const displayLength = getStringDisplayLength(item.taskName);
        maxLength = Math.max(maxLength, displayLength);
        maxLevel = Math.max(maxLevel, level);
      }
      if (item.children && item.children.length > 0) {
        findMaxTaskNameLength(item.children, level + 1);
      }
    });
  };

  findMaxTaskNameLength(data);

  // 基础宽度 + 字符宽度 + 层级缩进 + 内边距
  const baseWidth = 80; // 基础宽度
  const charWidth = 14; // 中文字符宽度约14px
  const levelIndent = maxLevel * 12; // 每级缩进12px
  const padding = 30; // 内边距

  const calculatedWidth = baseWidth + (maxLength * charWidth) + levelIndent + padding;

  // 设置最小宽度180px，最大宽度400px
  const finalWidth = Math.max(180, Math.min(400, calculatedWidth));
  return finalWidth;
};

// 计算字符串显示长度（中文字符算1，英文字符算0.5）
const getStringDisplayLength = (str) => {
  if (!str) return 0;
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i);
    // 判断是否为中文字符
    if (/[\u4e00-\u9fa5]/.test(char)) {
      length += 1; // 中文字符
    } else {
      length += 0.6; // 英文字符和数字
    }
  }
  return Math.ceil(length);
};

// 递归收集所有有子项的行ID
const collectExpandableRowIds = (data) => {
  let ids = [];
  data.forEach(row => {
    if (hasRealChildren(row)) {
      ids.push(row.id);
      // 递归处理子项
      ids = ids.concat(collectExpandableRowIds(row.children));
    }
  });
  return ids;
};

// 初始化展开状态
const initializeExpandState = () => {
  // Element Plus的default-expand-all属性会自动处理初始展开状态
  // 这里不需要手动处理
};

// 监听数据变化，重新初始化展开状态
watch(() => props.taskData, (newData) => {
  if (newData && newData.length > 0) {
    // 更新序号列宽度
    serialColumnWidth.value = calculateSerialColumnWidth(newData);
    // 更新工作项名称列宽度
    taskNameColumnWidth.value = calculateTaskNameColumnWidth(newData);
    initializeExpandState();
    // 构建序号到ID的映射表
    buildSerialToIdMap(newData);
  }
}, { immediate: true, deep: true });

// 递归展开所有行
const expandAllRows = (data) => {
  data.forEach(row => {
    if (hasRealChildren(row)) {
      taskTableRef.value.toggleRowExpansion(row, true);
      if (row.children && row.children.length > 0) {
        expandAllRows(row.children);
      }
    }
  });
};

// 递归收起所有行
const collapseAllRows = (data) => {
  data.forEach(row => {
    if (hasRealChildren(row)) {
      taskTableRef.value.toggleRowExpansion(row, false);
      if (row.children && row.children.length > 0) {
        collapseAllRows(row.children);
      }
    }
  });
};

// 暴露给父组件的方法
const expandAll = () => {
  if (taskTableRef.value && props.taskData && props.taskData.length > 0) {
    nextTick(() => {
      expandAllRows(props.taskData);
    });
  }
};

const collapseAll = () => {
  if (taskTableRef.value && props.taskData && props.taskData.length > 0) {
    nextTick(() => {
      collapseAllRows(props.taskData);
    });
  }
};

const toggleRow = (rowId) => {
  if (taskTableRef.value) {
    const row = findRowById(props.taskData, rowId);
    if (row) {
      taskTableRef.value.toggleRowExpansion(row);
    }
  }
};

// 根据ID查找行数据
const findRowById = (data, id) => {
  for (const row of data) {
    if (row.id === id) {
      return row;
    }
    if (row.children && row.children.length > 0) {
      const found = findRowById(row.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 暴露方法给父组件
defineExpose({
  expandAll,
  collapseAll,
  toggleRow
});

// 计算工作项名称的缩进样式
const getTaskNameStyle = (row) => {
  // 根据序号计算层级深度
  const serialNumber = row.serialNumber || '';
  const level = getLevelFromSerialNumber(serialNumber);
  
  // 工作项名称的缩进：基础缩进8px，每级增加12px
  const paddingLeft = level * 12 + 8;
  
  return {
    paddingLeft: `${paddingLeft}px`,
    display: 'inline-block',
    width: '100%'
  };
};

// 根据序号计算层级深度
const getLevelFromSerialNumber = (serialNumber) => {
  if (!serialNumber) return 0;
  
  // 计算序号中的点号数量来确定层级
  const dots = (serialNumber.match(/\./g) || []).length;
  return dots;
};

// 处理前置工作项点击
const handlePretaskClick = (serialNumber) => {
  // 触发前置任务点击事件，让父组件处理高亮逻辑
  emit('pretask-click', serialNumber);
  
  // 获取所有表格行
  const tableRows = document.querySelectorAll('.task-table .el-table__row');
  let targetRow = null;

  // 遍历所有行查找匹配的序号
  for (const row of tableRows) {
    const serialCell = row.querySelector('.serial-cell span');
    if (serialCell && serialCell.textContent.trim() === serialNumber.trim()) {
      targetRow = row;
      break;
    }
  }

  if (targetRow) {
    // 先移除所有之前的高亮
    document.querySelectorAll('.el-table__row.highlighted').forEach(row => {
      row.classList.remove('highlighted');
    });
    
    // 添加高亮
    targetRow.classList.add('highlighted');
    
    // 滚动到目标行
    targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // 2秒后移除高亮
    setTimeout(() => {
      targetRow.classList.remove('highlighted');
    }, 2000);
  } else {
    ElMessage.warning(`未找到工作项序号: ${serialNumber}`);
  }
};
</script>

<style lang="scss" scoped>
/* 表格外包装 */
.task-table-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible; /* 改为visible，确保表格底部边缘可见 */
  min-height: 400px; /* 设置最小高度确保表格有足够空间 */
}

/* 确保表格滚动条可见 */
:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  /* 确保底部内容可见 */
  padding-bottom: 2px;
}

:deep(.el-table__header-wrapper) {
  overflow: hidden !important;
}

/* 确保表头和表体滚动同步 */
:deep(.el-table__body-wrapper),
:deep(.el-table__header-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

/* 修复表头滚动同步问题 */
:deep(.el-table) {
  overflow: hidden !important;
}

:deep(.el-table .el-table__header-wrapper) {
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}

:deep(.el-table .el-table__body-wrapper) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 表格样式 */
:deep(.task-table) {
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  /* 移除强制白色背景 */
  
  /* 确保序号列有足够空间 */
  .serial-column {
    min-width: 100px !important;
    max-width: 120px !important;
  }
  
  /* Element Plus表格变量 */
  --el-table-border-color: #cbd5e1;
  --el-table-header-bg-color: #f1f5f9;
  --el-table-row-hover-bg-color: #f8fafc;
  --el-table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  --el-table-tr-bg-color: transparent;
  
  /* 表格行高 */
  td.el-table__cell,
  th.el-table__cell {
    height: 32px;
    padding: 0;
  }
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  --el-table-border-color: #cbd5e1;
  --el-table-header-bg-color: #f1f5f9;
  --el-table-row-hover-bg-color: #f8fafc;
  font-size: 13px;
  color: #333;
}

/* 表头样式 */
:deep(.el-table__header-wrapper th) {
  background-color: #f1f5f9;
  color: #333;
  font-weight: 500;
  height: 40px;
  padding: 8px 0;
  border-bottom: 1px solid #cbd5e1;
  border-right: 1px solid #cbd5e1;
}

:deep(.el-table__header-wrapper th.el-table__cell) {
  background-color: #f1f5f9;
}

:deep(.el-table__header-wrapper th.el-table__cell .cell) {
  font-weight: 500;
  color: #333;
  padding: 0 4px;
}

/* 行样式 */
:deep(.el-table__row) {
  height: 32px !important;
}

/* 确保所有行高度一致 */
:deep(.el-table__cell) {
  padding: 0 !important;
  height: 32px !important;
  line-height: 32px !important;
}

/* 表格单元格边框 */
:deep(.el-table td) {
  border-right: 1px solid #cbd5e1 !important;
  border-bottom: 1px solid #cbd5e1 !important;
}

/* 表格单元格内容样式 */
:deep(.el-table .cell) {
  padding: 0 4px !important;
  line-height: 32px !important;
}

/* 状态列样式优化 */
:deep(.status-column .cell) {
  padding: 0 2px !important;
}

:deep(.status-column .el-select) {
  max-width: 90px;
  width: 90px;
  margin: 0 auto;
}

/* 前置工作项样式 */
.pretask-content {
  display: inline-block;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  line-height: 1.5;
}

.pretask-number {
  cursor: pointer;
  color: #409EFF;
  text-decoration: underline;
  transition: color 0.3s;
  
  &:hover {
    color: #337ecc;
  }
}

/* 可编辑单元格样式 */
.editable-cell {
  width: 100%;
  height: 100%;
  cursor: pointer;
  padding: 0 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f0f9ff;
  }
  
  .el-input {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px #409EFF inset;
      padding: 0 6px;
    }
    
    .el-input__inner {
      height: 24px;
      line-height: 24px;
      font-size: 12px;
    }
  }
}

/* 序号列样式 */
.serial-cell {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0; /* 取消padding，紧贴表格边缘 */
}

.serial-number-text {
  font-size: 12px;
  line-height: 32px;
  white-space: nowrap;
  /* 移除overflow和text-overflow，让序号完整显示 */
  /* overflow: hidden; */
  /* text-overflow: ellipsis; */
  transition: all 0.3s ease;
  font-weight: 500;
  color: #333;
  padding-left: 0 !important; /* 确保左对齐 */

  &:hover {
    color: #409EFF;
  }
}

.stage-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding-left: 0 !important;
  font-size: 12px;
  color: #409EFF;
  font-weight: bold;
}

/* 序号列特殊样式 */
:deep(.serial-column) {
  .cell {
    padding: 0 !important; /* 去掉所有padding */
    text-align: left;
  }
}

/* 树形表格展开图标样式 */
:deep(.el-table__expand-icon) {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  transition: transform 0.3s ease;
  
  .el-table__expand-icon-arrow {
    width: 8px;
    height: 8px;
    border: 1px solid #909399;
    border-left: none;
    border-bottom: none;
    transform: rotate(45deg);
    transition: transform 0.3s ease;
  }
  
  &.el-table__expand-icon--expanded .el-table__expand-icon-arrow {
    transform: rotate(225deg);
  }
}

/* 确保展开图标可见且可点击 */
:deep(.el-table__expand-icon) {
  display: inline-block !important;
  cursor: pointer !important;
  pointer-events: auto !important;
}

/* 隐藏没有子项的行展开图标 */
:deep(.el-table__row.no-children .el-table__expand-icon) {
  visibility: hidden !important;
}

/* 显示有子项的行展开图标 */
:deep(.el-table__row.has-children .el-table__expand-icon) {
  visibility: visible !important;
}

/* 层级背景色样式 - 低饱和度灰色系 */
:deep(.el-table__row.level-0 td) {
  background-color: #e2e8f0 !important; /* 顶级节点 - 中等灰色 */
}

:deep(.el-table__row.level-1 td) {
  background-color: #f1f5f9 !important; /* 二级节点 - 浅灰色 */
}

:deep(.el-table__row.level-2 td) {
  background-color: #f8fafc !important; /* 三级节点 - 极浅灰色 */
}

:deep(.el-table__row.level-3 td),
:deep(.el-table__row.level-4 td),
:deep(.el-table__row.level-5 td) {
  background-color: #ffffff !important; /* 四级及以下节点 - 白色 */
}

/* 悬停效果 - 在层级背景色基础上稍微调深 */
:deep(.el-table__row.level-0:hover td) {
  background-color: #cbd5e1 !important; /* 比#e2e8f0稍深的灰色 */
}

:deep(.el-table__row.level-1:hover td) {
  background-color: #e2e8f0 !important; /* 比#f1f5f9稍深的灰色 */
}

:deep(.el-table__row.level-2:hover td) {
  background-color: #f1f5f9 !important; /* 比#f8fafc稍深的灰色 */
}

:deep(.el-table__row.level-3:hover td),
:deep(.el-table__row.level-4:hover td),
:deep(.el-table__row.level-5:hover td) {
  background-color: #f8fafc !important; /* 浅灰色悬停 */
}

/* 工作项名称列样式 */
.task-name-cell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.task-name-cell.readonly {
  cursor: default;
  /* 移除灰色背景遮挡 */
  opacity: 0.8;
}

/* 移除悬停时的灰色背景样式 */

.task-name-text {
  font-size: 12px;
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  
  &:hover {
    color: #409EFF;
  }
}

/* 高亮样式 */
:deep(.el-table__row.highlighted) {
  background-color: #ecf5ff !important;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% { background-color: #ecf5ff; }
  50% { background-color: #d9ecff; }
  100% { background-color: #ecf5ff; }
}

/* 操作列样式优化 */
.operation-column {
  display: flex;
  justify-content: center;
  gap: 4px;
  /* 移除强制白色背景 */
  width: 100% !important;
  height: 100% !important;
  position: relative;
  z-index: 2;
}

.operation-column .el-button {
  padding: 2px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  line-height: 1;
}

.operation-column .el-button .el-icon {
  margin-right: 2px;
  font-size: 14px;
}

/* 操作按钮特定样式 */
.operation-column .op-btn {
  padding: 0 4px !important;
  margin: 0 !important;
  height: 24px !important;
  line-height: 1 !important;
}

/* 确保图标和文本之间的间距合适 */
.operation-column .op-btn .el-icon {
  margin-right: 2px !important;
  font-size: 14px !important;
}

/* 确保按钮之间没有额外的间距 */
.operation-column .op-btn + .op-btn {
  margin-left: 0 !important;
}

/* 移除固定列强制背景色 */
:deep(.el-table__fixed-right) {
  height: 100% !important;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

/* 移除所有强制白色背景设置 */

/* 确保固定列在鼠标悬停时有正确的背景色 */
:deep(.el-table__fixed-right tr.el-table__row:hover td) {
  background-color: var(--el-table-row-hover-bg-color) !important;
}

:deep(.el-table__fixed-right tr.el-table__row:hover .cell) {
  background-color: var(--el-table-row-hover-bg-color) !important;
}

/* 移除斑马纹样式，改用层级背景色 */

/* 移除固定列包装器强制背景色 */

:deep(.el-button--small) {
  font-size: 12px;
}

/* 移除操作列强制背景色 */

/* 更新成功反馈样式 */
:deep(.el-table__row.update-success) {
  background-color: #f0f9ff !important;
  transition: background-color 0.3s ease;
}

:deep(.el-table__row.update-success td) {
  background-color: #f0f9ff !important;
}

/* 前置任务点击样式 */
.pretask-number {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
  margin-right: 2px;
}

.pretask-number:hover {
  color: #66b1ff;
}
</style> 