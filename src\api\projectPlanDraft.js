import request from '@/utils/request'

// 查询项目下的计划草稿树结构
export function getProjectWorkItemDraftTree(projectId) {
  return request({
    url: `/project-plan/draft/planTree/${projectId}`,
    method: 'get'
  })
}

// 查询计划下的子级计划草稿树状结构
export function getChildWorkItemsDraft(data) {
  return request({
    url: '/project-plan/draft/childPlans',
    method: 'get',
    params: data
  })
}

// 查询计划草稿列表
export function getWorkItemDraftList(data) {
  return request({
    url: '/project-plan/draft/list',
    method: 'post',
    data
  })
}

// 新增计划草稿
export function createWorkItemDraft(data) {
  return request({
    url: '/project-plan/draft/create',
    method: 'post',
    data
  })
}

// Mock数据 - 新增计划草稿
export function createWorkItemDraftMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      // 模拟创建成功，返回新创建的计划数据
      const newPlan = {
        id: Date.now(), // 模拟生成的ID
        planName: data.planName || '新子任务',
        planStatus: data.planStatus || '0',
        seriNum: data.seriNum,
        planStartTime: data.planStartTime || '',
        planEndTime: data.planEndTime || '',
        ownerId: data.ownerId || null,
        prePlanId: data.prePlanId || null,
        preSeriNum: data.preSeriNum || '',
        actualStartTime: '',
        actualEndTime: '',
        parentPlanId: data.parentPlanId,
        projectId: data.projectId,
        changeType: '1', // CREATE - 新增
        changeRemark: '新增子任务',
        version: 1
      };

      resolve({
        code: 200,
        msg: '创建成功',
        data: newPlan
      });
    }, 500);
  });
}

// 编辑计划草稿
export function updateWorkItemDraft(data) {
  return request({
    url: '/project-plan/draft/update',
    method: 'put',
    data
  })
}

// 批量编辑计划草稿并自动提交审核
export function batchUpdateWorkItemDraft(data) {
  return request({
    url: '/project-plan/draft/batchUpdate',
    method: 'put',
    data
  })
}

// 删除计划草稿（标记删除）
export function deleteWorkItemDraft(data) {
  return request({
    url: '/project-plan/draft/delete',
    method: 'delete',
    data
  })
}

// Mock数据 - 删除计划草稿
export function deleteWorkItemDraftMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '删除成功',
        data: true
      });
    }, 500);
  });
}

// 清除计划变更标识（草稿表）
export function clearChangeMarks(projectId) {
  return request({
    url: `/project-plan/draft/clearChangeMarks/${projectId}`,
    method: 'post'
  })
}

// 保存计划树形结构到草稿表
export function saveWorkItemTreeToDraft(data) {
  return request({
    url: '/project-plan/draft/saveToDraft',
    method: 'post',
    data
  })
}

// 提交项目计划审核
export function submitProjectWorkItemReview(projectId) {
  return request({
    url: `/project-plan/review/submit/${projectId}`,
    method: 'post'
  })
}

// 批量审核计划
export function batchReviewWorkItems(data) {
  return request({
    url: '/project-plan/review/batchReview',
    method: 'post',
    data
  })
}

// 获取项目计划审核列表
export function getProjectWorkItemReviewList(projectId) {
  return request({
    url: `/project-plan/review/reviewList/${projectId}`,
    method: 'get'
  })
}

// 获取项目计划审核列表（正确接口）
export function getProjectPlanReviewList(projectId) {
  return request({
    url: `/project-plan/review/reviewList/${projectId}`,
    method: 'get'
  })
}

// TODO: 临时Mock数据用于开发测试 - 获取项目计划审核列表
export function getProjectPlanReviewListMock(projectId) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockReviewData = [
        {
          draftId: 1,
          reviewId: 101,
          planName: '准备阶段',
          ownerName: '张三',
          planStartTime: '2022-01-01 09:00:00',
          planEndTime: '2022-01-01 18:00:00',
          changeType: '3', // DELETE - 删除
          changeRemark: '已组会讨论，延期2天',
          reviewOpinion: '',
          version: 1
        },
        {
          draftId: 2,
          reviewId: 102,
          planName: '项目任务计划1',
          ownerName: '张三',
          planStartTime: '2022-01-01 09:00:00',
          planEndTime: '2022-01-01 18:00:00',
          changeType: '1', // ADD - 新增
          changeRemark: '-',
          reviewOpinion: '',
          version: 1
        },
        {
          draftId: 3,
          reviewId: 103,
          planName: '项目任务计划2',
          ownerName: '张三',
          planStartTime: '2022-01-01 09:00:00',
          planEndTime: '2022-01-01 18:00:00',
          changeType: '2', // UPDATE - 编辑
          changeRemark: '已组会讨论，延期3天',
          reviewOpinion: '',
          version: 1
        },
        {
          draftId: 4,
          reviewId: 104,
          planName: '项目任务计划3',
          ownerName: '张三',
          planStartTime: '2022-01-01 09:00:00',
          planEndTime: '2022-01-01 18:00:00',
          changeType: '2', // UPDATE - 编辑
          changeRemark: '已组会讨论，延期3天',
          reviewOpinion: '',
          version: 1
        }
      ];

      resolve({
        code: 0,
        msg: '成功',
        data: mockReviewData
      });
    }, 500);
  });
}

// 批量审核项目计划（正确接口）
export function batchReviewProjectPlan(data) {
  return request({
    url: '/project-plan/review/batchReview',
    method: 'post',
    data
  })
}

// TODO: 临时Mock数据用于开发测试 - 批量审核项目计划
export function batchReviewProjectPlanMock(data) {
  return new Promise(resolve => {
    setTimeout(() => {


      // 根据审核结果返回不同的消息
      let message = '审核成功';
      if (data.reviewResult === 'APPROVED') {
        message = '审核通过成功';
      } else if (data.reviewResult === 'REJECTED') {
        message = '审核驳回成功';
      }

      resolve({
        code: 0,
        msg: message,
        data: null
      });
    }, 1000);
  });
}

// 提交项目计划审核（正确接口）
export function submitProjectPlanReview(projectId) {
  return request({
    url: `/project-plan/review/submit/${projectId}`,
    method: 'post'
  })
}

// 生成单个雪花算法ID
export function generatePlanId() {
  return request({
    url: '/project-plan/draft/generateId',
    method: 'get'
  })
}

// 批量生成雪花算法ID（固定生成10个）
export function generatePlanIds() {
  return request({
    url: '/project-plan/draft/generateIds',
    method: 'get'
  })
}

// 从Excel导入计划数据
export function importFromExcel(projectId, file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: `/project-plan/draft/importFromExcel?projectId=${projectId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 1分钟超时，Excel导入可能需要较长时间
  })
}

