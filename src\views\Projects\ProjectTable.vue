<template>
  <div class="custom-table-wrapper">
    <el-table
      :data="data"
      border
      style="width: 100%"
      v-loading="loading"

      class="custom-table">
      <el-table-column label="序号" width="60" align="center">
        <template #default="scope">
          <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="项目名称" min-width="180" align="left" show-overflow-tooltip class-name="project-name-cell">
        <template #default="{ row }">
          <!-- 所有角色都可以点击项目名称跳转 -->
          <span
            class="project-name-link"
            @click="handleProjectClick(row)"
          >
            {{ row.projectName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="contractNo" label="项目编号" min-width="120" align="left" show-overflow-tooltip class-name="contract-no-cell" />
      <el-table-column prop="projectType" label="项目类型" width="90" align="center">
        <template #default="{ row }">
          {{ getProjectTypeDisplay ? getProjectTypeDisplay(row.projectType) : row.projectType }}
        </template>
      </el-table-column>
      <el-table-column prop="firstPartyName" label="甲方单位" min-width="140" align="left" show-overflow-tooltip class-name="first-party-cell" />
      <el-table-column prop="signDate" label="合同签订时间" width="110" align="center">
        <template #default="{ row }">
          {{ formatDate(row.signDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="acceptDate" label="合同完成时间" width="110" align="center">
        <template #default="{ row }">
          {{ formatDate(row.acceptDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="contractAmount" label="合同金额" width="90" align="center" />
      <el-table-column prop="payCountMoney" label="收款总额" width="90" align="center" />
      <el-table-column prop="projectState" label="项目状态" width="90" align="center">
        <template #default="{ row }">
          {{ getProjectStateDisplay ? getProjectStateDisplay(row.projectState) : row.projectState }}
        </template>
      </el-table-column>
      <el-table-column prop="projectManagerName" label="项目经理" width="80" align="center" />
      <el-table-column prop="createTime" label="创建时间" width="100" align="center">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="getOperationColumnWidth" align="center" fixed="right">
        <template #default="{ row }">
          <!-- 查看按钮 - 所有角色都可以看到 -->
          <el-button type="primary" link size="small" class="op-btn" @click="$emit('view', row)">
            <el-icon><View /></el-icon>查看
          </el-button>
          <!-- 编辑按钮 - 只有计划员可以看到 -->
          <el-button
            v-if="isPlannerRole"
            type="primary"
            link
            size="small"
            class="op-btn"
            @click="$emit('edit', row)"
          >
            <el-icon><Edit /></el-icon>编辑
          </el-button>
          <!-- 任务生成(AI)按钮 - 只有计划员可以看到，且只有草稿状态才可点击 -->
          <el-button
            v-if="isPlannerRole"
            :type="getAiButtonType(row.aiGenerationStatus, row.reviewStatus)"
            link
            size="small"
            class="op-btn ai-generate-btn"
            :class="getAiButtonClass(row.aiGenerationStatus, row.reviewStatus)"
            @click="handleAiButtonClick(row)"
            :disabled="!canClickAiButton(row.reviewStatus)"
          >
            <el-icon><magic-stick /></el-icon>{{ getAiButtonText(row.aiGenerationStatus, row.reviewStatus) }}
          </el-button>
          <!-- 关注/取消关注按钮 - 只有项目领导可以看到 -->
          <el-button
            v-if="isProjectLeader"
            :type="row.followed ? 'warning' : 'success'"
            link
            size="small"
            class="op-btn follow-btn"
            @click="$emit('toggleFollow', row)"
          >
            <el-icon><Star v-if="!row.followed" /><StarFilled v-else /></el-icon>{{ row.followed ? '取消关注' : '关注' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { View, Edit, MagicStick, Star, StarFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

import { isPlanner, canFollowProject } from '@/utils/permissions';

const props = defineProps({
  data: Array,
  loading: Boolean,
  currentPage: Number,
  pageSize: Number,

  getProjectTypeDisplay: Function,
  getProjectStateDisplay: Function
});



const emit = defineEmits(['view', 'edit', 'generate-task', 'toggleFollow']);

const router = useRouter();

// 权限控制：使用全局权限系统
const isPlannerRole = computed(() => isPlanner());
const isProjectLeader = computed(() => canFollowProject());

// 动态计算操作列宽度
const getOperationColumnWidth = computed(() => {
  if (isPlannerRole.value && isProjectLeader.value) {
    return 320; // 计划员+项目领导：查看 + 编辑 + 任务生成(AI) + 关注/取消关注
  } else if (isPlannerRole.value) {
    return 240; // 计划员：查看 + 编辑 + 任务生成(AI)
  } else if (isProjectLeader.value) {
    return 160; // 项目领导：查看 + 关注/取消关注
  } else {
    return 80; // 其他角色：只有查看
  }
});

// 处理项目名称点击事件 - 所有角色都可以跳转
const handleProjectClick = (row) => {
  // 跳转到项目详情页面，同时传递contractNo作为查询参数以优化性能
  router.push({
    path: `/project-detail/${row.id}`,
    query: {
      contractNo: row.contractNo
    }
  });
};

// 判断AI生成按钮是否可以点击（只有草稿状态才可以点击）
const canClickAiButton = (reviewStatus) => {
  return reviewStatus === '0' // 只有草稿状态(0)才可以点击
};

// 处理AI按钮点击事件
const handleAiButtonClick = (row) => {
  if (canClickAiButton(row.reviewStatus)) {
    emit('generate-task', row)
  }
};

// 根据AI生成状态和审核状态获取按钮类型
const getAiButtonType = (aiStatus, reviewStatus) => {
  // 如果不是草稿状态，显示为禁用样式
  if (reviewStatus !== '0') {
    return 'info'
  }

  switch (aiStatus) {
    case '0': // NOT_STARTED - 未开始
      return 'primary'
    case '1': // GENERATING - 生成中
      return 'warning'
    case '2': // COMPLETED - 已完成
      return 'success'
    case '3': // FAILED - 失败
      return 'danger'
    default:
      return 'primary'
  }
};

// 根据AI生成状态和审核状态获取按钮文字
const getAiButtonText = (aiStatus, reviewStatus) => {
  // 如果不是草稿状态，显示禁用文字
  if (reviewStatus !== '0') {
    const reviewStatusMap = {
      '1': '待审核',
      '2': '生效中',
      '3': '已驳回'
    }
    return `${reviewStatusMap[reviewStatus] || '不可用'}`
  }

  switch (aiStatus) {
    case '0': // NOT_STARTED - 未开始
      return '任务生成(AI)'
    case '1': // GENERATING - 生成中
      return '计划生成中'
    case '2': // COMPLETED - 已完成
      return '重新生成'
    case '3': // FAILED - 失败
      return '重试生成'
    default:
      return '任务生成(AI)'
  }
};

// 根据AI生成状态和审核状态获取按钮额外样式类
const getAiButtonClass = (aiStatus, reviewStatus) => {
  // 如果不是草稿状态，添加禁用样式
  if (reviewStatus !== '0') {
    return 'disabled-status'
  }

  switch (aiStatus) {
    case '1': // GENERATING - 生成中
      return 'generating'
    case '2': // COMPLETED - 已完成
      return 'completed'
    case '3': // FAILED - 失败
      return 'failed'
    default:
      return ''
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  // 处理不同的日期格式
  if (dateString.includes('T')) {
    // ISO格式：2020-01-01T00:00:00
    return dateString.split('T')[0];
  } else if (dateString.includes(' ')) {
    // 带时间格式：2020-01-01 00:00:00
    return dateString.split(' ')[0];
  }
  // 已经是日期格式：2020-01-01
  return dateString;
};
</script>

<style>
/* 全局强制覆盖Element Plus表格样式 - 统一行高 */
.el-table__row {
  height: 26px !important;
}

.el-table__header-row {
  height: 26px !important;
}

.el-table .cell {
  line-height: 1.4 !important;
  padding: 4px 8px !important;
}

.el-table--border .el-table__cell {
  padding: 4px 8px !important;
}

.el-table__header-wrapper th.el-table__cell {
  padding: 4px 8px !important;
}

.el-table {
  font-size: 11px !important;
}

/* 强制左对齐特定列 */
.el-table .project-name-cell .cell,
.el-table .first-party-cell .cell {
  text-align: left !important;
}

/* 强制左对齐特定列 */
:deep(.project-name-cell .cell),
:deep(.first-party-cell .cell) {
  text-align: left !important;
  padding-left: 8px !important;
}
</style>

<style scoped>
.custom-table-wrapper {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 自定义表格样式 */
:deep(.custom-table) {
  width: 100% !important;
  table-layout: fixed;
  border-radius: 8px;
  overflow: hidden;
  font-size: 11px !important; /* 动态调整字体大小 */
  --el-table-border-color: #e0e0e0;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f5f7fa;
  --el-table-fixed-box-shadow: none;
}

/* 表格自适应高度优化 */
:deep(.custom-table .el-table__cell) {
  line-height: 1.4 !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
}

/* 表头样式 */
:deep(.custom-table .el-table__header-wrapper th.el-table__cell) {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 500;
  padding: 2px 8px !important;
  height: 26px !important;
  line-height: 1.2 !important;
  font-size: 11px !important;
}

/* 表格行样式 */
:deep(.custom-table .el-table__row) {
  height: 26px !important;
}

/* 表格单元格样式 */
:deep(.custom-table .el-table__cell) {
  padding: 2px 8px !important;
  height: 26px !important;
  line-height: 1.2 !important;
}

/* 单元格内容样式 */
:deep(.custom-table .cell) {
  padding: 0 4px !important;
  line-height: 1.2 !important;
  height: 26px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 使用block布局确保text-overflow正常工作 */
  display: block;
  text-align: center;
  /* 垂直居中 */
  line-height: 26px !important;
}

/* 操作按钮样式 - 适应25px行高 */
:deep(.custom-table .op-btn) {
  padding: 0 3px !important;   /* 减少内边距 */
  height: 18px !important;     /* 减小按钮高度 */
  line-height: 18px !important; /* 调整行高 */
  font-size: 10px !important;  /* 减小字体 */
  margin: 0 1px !important;
}

/* 表格边框样式 */
:deep(.custom-table .el-table__inner-wrapper::before),
:deep(.custom-table::before),
:deep(.custom-table::after) {
  display: none !important;
}

/* 表格底部边框修复 */
:deep(.custom-table .el-table__border-bottom-patch) {
  display: none !important;
}

/* 固定列样式 */
:deep(.custom-table .el-table__fixed-right) {
  height: auto !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

:deep(.custom-table .el-table__fixed-right .el-table__fixed-header-wrapper) {
  background-color: #f5f7fa !important;
}

:deep(.custom-table .el-table__fixed-right .el-table__fixed-body-wrapper) {
  background-color: #ffffff !important;
}

/* 斑马纹样式 */
:deep(.custom-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: #fafafa !important;
}

/* 去除表格垂直滚动条，保留水平滚动条 */
:deep(.custom-table .el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 表格行高亮 */
:deep(.custom-table .el-table__body tr.hover-row > td.el-table__cell) {
  background-color: #f0f7ff !important;
}

/* 调整表格行间距 */
:deep(.custom-table .el-table__body tr) {
  border-spacing: 0 !important;
}

/* 操作列按钮容器样式 - 使用flex布局 */
:deep(.custom-table .el-table__cell:last-child .cell) {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 1px;
  height: 100%;
}

/* AI生成按钮特殊样式 */
:deep(.ai-generate-btn.generating) {
  animation: pulse 1.5s infinite;
}

:deep(.ai-generate-btn.completed) {
  position: relative;
}

:deep(.ai-generate-btn.completed::after) {
  content: '✓';
  position: absolute;
  right: -8px;
  top: -1px;
  font-size: 10px;
  color: #67C23A;
  font-weight: bold;
}

:deep(.ai-generate-btn.failed) {
  position: relative;
}

:deep(.ai-generate-btn.failed::after) {
  content: '!';
  position: absolute;
  right: -8px;
  top: -1px;
  font-size: 10px;
  color: #F56C6C;
  font-weight: bold;
}

/* 非草稿状态的禁用样式 */
:deep(.ai-generate-btn.disabled-status) {
  opacity: 0.6;
  cursor: not-allowed;
}

:deep(.ai-generate-btn.disabled-status:hover) {
  opacity: 0.6 !important;
}

/* 生成中的脉冲动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 强制设置表格行高变量 - 统一为26px */
:deep(.custom-table) {
  --el-table-row-height: 26px !important;
  --el-table-header-row-height: 26px !important;
}

/* 修复表格底部空隙问题 - 移除重复设置，使用全局样式 */

:deep(.custom-table .el-table__body) {
  border-bottom: none !important;
}

:deep(.custom-table .el-table__body tr:last-child td) {
  border-bottom: 1px solid var(--el-table-border-color) !important;
}

/* 确保表格容器高度计算正确 */
.custom-table-wrapper {
  width: 100%;
  /* 让Element Plus表格自己管理高度 */
}

:deep(.custom-table) {
  width: 100% !important;
  border-radius: 4px !important;
  overflow: hidden !important;
}

/* 精确控制表格内部结构 */
:deep(.custom-table .el-table__inner-wrapper) {
  border: none !important;
  height: 100% !important;
}

:deep(.custom-table .el-table__header-wrapper) {
  border-bottom: 1px solid #EBEEF5 !important;
  flex-shrink: 0 !important;
}

:deep(.custom-table .el-table__body-wrapper) {
  border: none !important;
  flex: 1 !important;
  overflow-y: auto !important;
  /* 确保滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #C0C4CC #F5F7FA;
}

/* 确保表格行高一致 - 26px */
:deep(.custom-table .el-table__row) {
  height: 26px !important;
}

:deep(.custom-table .el-table__header .el-table__cell) {
  height: 26px !important;
  padding: 2px 8px !important;
  line-height: 1.2 !important;
}

:deep(.custom-table .el-table__body .el-table__cell) {
  height: 26px !important;
  padding: 2px 8px !important;
  line-height: 1.2 !important;
}

:deep(.custom-table .el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.custom-table .el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #F5F7FA;
  border-radius: 4px;
}

:deep(.custom-table .el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #C0C4CC;
  border-radius: 4px;
}

:deep(.custom-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #A8ABB2;
}

:deep(.custom-table .el-table__inner-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

:deep(.custom-table .el-table__body-wrapper) {
  flex: 1;
  min-height: 0 !important; /* 允许收缩 */
}

:deep(.custom-table .el-table__header-wrapper) {
  height: 26px !important; /* 表头1行 - 统一使用26px */
}

/* 移除表格底部多余的空白 */
:deep(.custom-table .el-table__body-wrapper::after) {
  display: none !important;
}

:deep(.custom-table .el-table__body-wrapper::before) {
  display: none !important;
}

/* 精确控制表格行高度，消除空隙 - 统一使用26px */
:deep(.custom-table .el-table__body tr) {
  height: 26px !important;
  min-height: 26px !important;
  max-height: 26px !important;
}

:deep(.custom-table .el-table__body td) {
  height: 26px !important;
  min-height: 26px !important;
  max-height: 26px !important;
  padding: 2px 8px !important;
  border-bottom: 1px solid var(--el-table-border-color) !important;
}

/* 确保最后一行与表格底部完全对齐 */
:deep(.custom-table .el-table__body tr:last-child td) {
  border-bottom: none !important; /* 移除最后一行的底边框，使用容器边框 */
}

/* 确保最后一行完整显示并紧贴底部 */
:deep(.custom-table .el-table__body tr:last-child) {
  height: 26px !important;
  min-height: 26px !important;
  max-height: 26px !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  position: relative;
}

/* 最后一行底部紧贴表格容器底部 */
:deep(.custom-table .el-table__body tr:last-child td) {
  border-bottom: none !important;
  position: relative;
}

/* 恢复表格容器原始设置，保持最外层容器大小不变 */
:deep(.custom-table) {
  position: relative;
  border: 1px solid var(--el-table-border-color) !important;
  box-sizing: border-box;
  overflow-y: hidden !important; /* 隐藏垂直滚动 */
  overflow-x: auto !important; /* 允许水平滚动 */
}

/* 精确计算表格内部容器高度，使边框贴合最下边缘 */
:deep(.custom-table .el-table) {
  height: 100% !important;
  border: none !important;
}

:deep(.custom-table .el-table__inner-wrapper) {
  height: 100% !important;
  border: none !important;
}

/* 表头高度固定 */
:deep(.custom-table .el-table__header-wrapper) {
  height: 26px !important;
}

/* 关键：精确计算表格内容区域高度，消除底部空白 */
:deep(.custom-table .el-table__body-wrapper) {
  height: calc(100% - 26px) !important; /* 总高度减去表头高度 */
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 表格内容填充整个可用空间 */
:deep(.custom-table .el-table__body) {
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 滚动条控制 */

/* 确保表格容器垂直滚动控制，保留水平滚动 */
:deep(.custom-table .el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 保留水平滚动条以便查看所有列 */
:deep(.custom-table .el-scrollbar__bar.is-horizontal) {
  display: block !important;
}

/* 移除重复的样式设置，已在上面统一处理 */

/* 项目名称可点击样式 */
.project-name-link {
  color: #409eff !important;
  cursor: pointer !important;
  text-decoration: none;
  transition: color 0.3s ease;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-name-link:hover {
  color: #66b1ff !important;
  text-decoration: underline;
}

/* 项目名称普通文本样式 */
.project-name-text {
  color: #303133 !important;
  cursor: default !important;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 项目名称列左对齐 */
:deep(.project-name-cell .cell) {
  text-align: left !important;
  padding-left: 8px !important;
}

/* 项目编号列左对齐 */
:deep(.contract-no-cell .cell) {
  text-align: left !important;
  padding-left: 8px !important;
}

/* 关注按钮样式 */
:deep(.custom-table .follow-btn) {
  padding: 0 4px !important;
  height: 21px !important;
  line-height: 21px !important;
  font-size: 11px !important;
  margin-left: 1px !important;
}

:deep(.custom-table .follow-btn.el-button--success) {
  color: #67c23a !important;
}

:deep(.custom-table .follow-btn.el-button--warning) {
  color: #e6a23c !important;
}

:deep(.custom-table .follow-btn:hover) {
  background-color: transparent !important;
}
</style>