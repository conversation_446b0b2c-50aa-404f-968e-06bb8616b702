<template>
  <div class="user-management">
    <!-- 搜索筛选表单 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="人员姓名:">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入"
            clearable
            style="width: 180px"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>重置
          </el-button>
        </el-form-item>
        
        <!-- 角色管理按钮放在表单右侧 -->
        <div class="form-right-actions">
          <el-button type="primary" @click="handleRoleManagement">
            角色管理
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 用户列表表格 -->
    <div class="table-container">
      <el-table 
        v-loading="loading"
        :data="users"
        stripe
        style="width: 100%"
        :row-style="{ height: '48px' }"
        :cell-style="{ padding: '4px 0' }"
      >
        <el-table-column type="selection" width="50" align="center" />
        
        <el-table-column label="序号" width="70" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        
        <el-table-column prop="realName" label="人员姓名" min-width="120" align="center" />
        
        <el-table-column prop="username" label="账号" min-width="130" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="username-cell">
              {{ row.username }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="phone" label="手机号" min-width="140" align="center" />
        
        <el-table-column prop="areaName" label="区域" min-width="120" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="area-cell">
              {{ row.areaName }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="positionName" label="岗位" min-width="140" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="position-cell">
              {{ row.positionName }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="roleName" label="角色" min-width="120" align="center" />
        
        <el-table-column prop="createdTime" label="创建时间" min-width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="100" fixed="right" align="center">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleEditUser(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共 {{ total }} 条
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
  
  <!-- 编辑用户抽屉 -->
  <el-drawer
    v-model="editDialogVisible"
    size="520px"
    direction="rtl"
    destroy-on-close
    :close-on-click-modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :show-close="false"
    class="user-edit-drawer"
  >
    <template #header>
      <div class="custom-drawer-header">
        <div class="drawer-close-btn" @click="editDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        <div class="drawer-title">
          编辑用户 - {{ editUserForm.realName || '用户信息' }}
        </div>
      </div>
    </template>
    <div class="user-edit-content drawer-optimized">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><User /></el-icon>
            <span class="section-title">基本信息</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <!-- 账号 -->
              <div class="field-item">
                <label class="field-label">账号</label>
                <div class="field-value">
                  <el-input
                    v-model="editUserForm.username"
                    readonly
                    class="readonly-input"
                    placeholder=""
                  >
                    <template #prefix>
                      <el-icon class="readonly-icon"><User /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 姓名 -->
              <div class="field-item">
                <label class="field-label">姓名</label>
                <div class="field-value">
                  <el-input
                    v-model="editUserForm.realName"
                    readonly
                    class="readonly-input"
                    placeholder=""
                  >
                    <template #prefix>
                      <el-icon class="readonly-icon"><Avatar /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 手机号码 -->
              <div class="field-item">
                <label class="field-label">手机号码</label>
                <div class="field-value">
                  <el-input
                    v-model="editUserForm.phone"
                    readonly
                    class="readonly-input"
                    placeholder=""
                  >
                    <template #prefix>
                      <el-icon class="readonly-icon"><Phone /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 邮箱 -->
              <div class="field-item">
                <label class="field-label">邮箱</label>
                <div class="field-value">
                  <el-input
                    v-model="editUserForm.email"
                    readonly
                    class="readonly-input"
                    placeholder=""
                  >
                    <template #prefix>
                      <el-icon class="readonly-icon"><Message /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 岗位 -->
              <div class="field-item">
                <label class="field-label">岗位</label>
                <div class="field-value">
                  <el-input
                    v-model="editUserForm.positionName"
                    readonly
                    class="readonly-input"
                    placeholder=""
                  >
                    <template #prefix>
                      <el-icon class="readonly-icon"><Briefcase /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限设置区域 -->
      <div class="form-section">
        <div class="section-layout">
          <div class="section-header">
            <el-icon class="section-icon"><Setting /></el-icon>
            <span class="section-title">权限设置</span>
          </div>
          <div class="section-content">
            <div class="form-fields">
              <!-- 用户角色 -->
              <div class="field-item">
                <label class="field-label">用户角色</label>
                <div class="field-value">
                  <el-select
                    v-model="editUserForm.roleId"
                    placeholder="请选择角色"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="role in roleOptions"
                      :key="role.id"
                      :label="role.name"
                      :value="role.id"
                    />
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <el-button type="primary" @click="saveUser">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  User,
  Check,
  Close,
  Search,
  RefreshLeft,
  Setting,
  Avatar,
  Phone,
  Message,
  Briefcase
} from '@element-plus/icons-vue';
import { getUserList, editUser, getAllRoles } from '@/api/user';

const router = useRouter();

// 加载状态
const loading = ref(false);

// 编辑用户弹窗相关
const editDialogVisible = ref(false);
const editUserForm = reactive({
  id: '',
  username: '',
  number: '', // 添加工号字段
  realName: '',
  phone: '',
  email: '',
  position: '',
  positionName: '',
  roleId: null,
  areaIds: []
});

// 角色下拉选项
const roleOptions = ref([]);

// 搜索表单
const searchForm = reactive({
  realName: '',
  pageNum: 1,
  pageSize: 10 // 初始值，会被动态计算覆盖
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10); // 保持响应式，将动态计算
const total = ref(0);

// 动态计算分页大小 - 针对用户管理页面优化
const calculateOptimalPageSize = () => {
  try {
    // 获取视口高度
    const viewportHeight = window.innerHeight

    // 用户管理页面的预留空间
    // 减去：顶部导航(60px) + 页面标题(50px) + 搜索区域(80px) + 表格头部(40px) + 分页器(60px) + 安全边距(60px)
    const reservedHeight = 350
    const availableHeight = Math.max(viewportHeight - reservedHeight, 200)

    // 每行高度约26px（与其他页面保持一致）
    const rowHeight = 26

    // 计算可显示的行数，减少10%作为缓冲
    const maxRows = Math.floor(availableHeight / rowHeight * 0.9)

    // 设置合理的分页大小范围
    let optimalPageSize = Math.max(10, Math.min(maxRows, 25))

    // 根据屏幕尺寸调整 - 适合用户管理页面
    if (viewportHeight <= 700) {
      // 小屏幕
      optimalPageSize = Math.min(optimalPageSize, 10)
    } else if (viewportHeight <= 800) {
      // 中小屏幕
      optimalPageSize = Math.min(optimalPageSize, 12)
    } else if (viewportHeight <= 1000) {
      // 中等屏幕
      optimalPageSize = Math.min(optimalPageSize, 15)
    } else {
      // 大屏幕 - 显示15条
      optimalPageSize = Math.min(optimalPageSize, 15)
    }

    // 确保最小值为10
    return Math.max(10, optimalPageSize)
  } catch (error) {
    console.warn('计算分页大小失败，使用默认值:', error)
    return 10 // 默认值
  }
}

// 窗口大小变化处理
const handleResize = () => {
  const newPageSize = calculateOptimalPageSize()
  if (newPageSize !== pageSize.value) {
    pageSize.value = newPageSize
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(total.value / pageSize.value)
    if (currentPage.value > maxPage && maxPage > 0) {
      currentPage.value = maxPage
    }
    // 重新获取数据
    fetchUserList()
  }
}

// 防抖处理的窗口大小变化
let resizeTimer = null
const debouncedHandleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(handleResize, 300)
}

// 用户列表数据
const users = ref([]);

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  return dateString.replace('T', ' ').substring(0, 19);
};

// 获取用户列表
const getUserListData = async () => {
  loading.value = true;
  
  try {
    const params = {
      realName: searchForm.realName || undefined,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const response = await getUserList(params);
    
    if (response && response.data) {
      users.value = response.data;
      total.value = response.total || 0;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败：' + (error.message || '网络错误'));
  } finally {
    loading.value = false;
  }
};

// 获取所有角色
const getAllRolesData = async () => {
  try {
    const response = await getAllRoles();
    
    if (response && response.data) {
      roleOptions.value = response.data.map(role => ({
        id: role.id,
        name: role.roleName
      }));
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    ElMessage.error('获取角色列表失败：' + (error.message || '网络错误'));
  }
};

// 查询按钮点击事件
const handleSearch = () => {
  currentPage.value = 1;
  getUserListData();
};

// 重置按钮点击事件
const handleReset = () => {
  searchForm.realName = '';
  handleSearch();
};

// 页码变更事件
const handlePageChange = (page) => {
  currentPage.value = page;
  getUserListData();
};

// 编辑用户事件
const handleEditUser = (user) => {
  editUserForm.id = user.id;
  editUserForm.username = user.username;
  editUserForm.number = user.number; // 添加工号字段
  editUserForm.realName = user.realName;
  editUserForm.phone = user.phone;
  editUserForm.email = user.email || '';
  editUserForm.position = user.positionId;
  editUserForm.positionName = user.positionName;
  
  // 设置用户的角色ID和区域ID，确保是数字类型
  editUserForm.roleId = user.roleId ? parseInt(user.roleId) : null;
  editUserForm.areaIds = user.areaId ? [parseInt(user.areaId)] : [];
  
  editDialogVisible.value = true;
};

// 保存用户信息
const saveUser = async () => {
  if (!editUserForm.roleId) {
    ElMessage.warning('请选择角色');
    return;
  }
  
  try {
    loading.value = true;
    
    const userData = {
      id: parseInt(editUserForm.id),
      username: editUserForm.username,
      number: editUserForm.number || '', // 使用表单中的工号字段
      realName: editUserForm.realName,
      phone: editUserForm.phone,
      positionId: editUserForm.position,
      status: 1, // 添加状态字段，默认为1（正常）
      roleIds: [parseInt(editUserForm.roleId)], // 将单个角色ID转换为数组
      areaIds: (editUserForm.areaIds || []).filter(id => id != null).map(id => parseInt(id))
    };
    
    const response = await editUser(userData);
      
      ElMessage.success('用户更新成功');
      editDialogVisible.value = false;
    
    // 刷新用户列表
    getUserListData();
  } catch (error) {
    console.error('保存用户失败:', error);
    console.error('错误详情:', error.response?.data);
    ElMessage.error('保存失败：' + (error.message || '网络错误'));
  } finally {
    loading.value = false;
  }
};

// 跳转到角色管理页面
const handleRoleManagement = () => {
  router.push('/users/role');
};

// 页面加载时获取数据
onMounted(() => {
  // 初始化分页大小
  pageSize.value = calculateOptimalPageSize();

  getUserListData();
  getAllRolesData();

  // 监听窗口大小变化
  window.addEventListener('resize', debouncedHandleResize);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 清理窗口大小变化监听
  window.removeEventListener('resize', debouncedHandleResize);

  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
});
</script>

<style scoped>
.user-management {
  padding: 16px;
  background: #ffffff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 8px;
  overflow: hidden;
}

.search-section {
  margin-bottom: 16px;
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

.form-right-actions {
  margin-left: auto;
}

.table-container {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0 8px 0;
  gap: 16px;
}

.pagination-info {
  color: #606266;
  font-size: 14px;
}

/* 编辑用户抽屉样式 */
.user-edit-drawer {
  /* 覆盖 Element Plus 抽屉默认样式 */
  :deep(.el-drawer__header) {
    display: none; /* 隐藏默认头部，使用自定义头部 */
  }

  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 24px;
    border-top: 1px solid #EBEEF5;
    background-color: #FAFAFA;
    margin-top: auto;
  }
}

/* 自定义抽屉头部样式 */
.custom-drawer-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
}

.drawer-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
  background-color: transparent;
  margin-right: 12px;
  flex-shrink: 0;
}

.drawer-close-btn:hover {
  background-color: #f5f7fa;
  color: #606266;
}

.drawer-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

/* 抽屉内容样式 */
.user-edit-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #ffffff;
}

.drawer-optimized {
  display: flex;
  flex-direction: column;
}

.form-section {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-layout {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px 8px 24px;
  background-color: #ffffff;
  border-bottom: none;
  margin-bottom: 8px;
}

.section-icon {
  color: #409EFF;
  font-size: 14px;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  line-height: 1;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-content {
  padding: 0 24px 16px 24px;
}

/* 表单字段样式 */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 28px;
  margin-bottom: 12px;
}

.field-label {
  font-size: 14px;
  color: #666666;
  font-weight: 400;
  line-height: 1.4;
  min-width: 80px;
  text-align: left;
  flex-shrink: 0;
}

.field-value {
  flex: 1;
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
}

/* 只读输入框样式 */
.readonly-input :deep(.el-input__wrapper) {
  background-color: #f0f2f5 !important;
  border: 1px solid #d9d9d9 !important;
  box-shadow: none !important;
  cursor: default;
  border-radius: 8px;
}

.readonly-input :deep(.el-input__inner) {
  color: #8c8c8c !important;
  cursor: default;
  background-color: transparent !important;
  font-weight: 400;
}

.readonly-input :deep(.el-input__wrapper:hover) {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
  background-color: #f0f2f5 !important;
}

.readonly-input :deep(.el-input__wrapper.is-focus) {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
  background-color: #f0f2f5 !important;
}

/* 只读输入框图标样式 */
.readonly-icon {
  color: #bfbfbf;
  font-size: 14px;
}

/* 可编辑字段样式 */
.field-value :deep(.el-select) {
  width: 100%;
}

.field-value :deep(.el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  background-color: #ffffff;
}

.field-value :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.field-value :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 抽屉底部按钮 */
.drawer-footer {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}

:deep(.el-button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: #f5f7fa;
  color: #303133;
}

/* 账号列样式 - 不换行并显示省略号 */
.username-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 岗位列样式 - 不换行并显示省略号 */
.position-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 区域列样式 - 不换行并显示省略号 */
.area-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
</style> 