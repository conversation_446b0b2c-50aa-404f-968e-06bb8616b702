import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
// 导入自定义全局样式（放在ElementPlus样式之后，便于覆盖）
import './styles/index.scss'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()
// 使用持久化插件
pinia.use(piniaPluginPersistedstate)

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 自定义ElementPlus中文配置
const customZhCn = {
  ...zhCn,
  el: {
    ...zhCn.el,
    pagination: {
      ...zhCn.el.pagination,
      total: '共 {total} 条'
    }
  }
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: customZhCn
})

// 挂载应用
app.mount('#app') 