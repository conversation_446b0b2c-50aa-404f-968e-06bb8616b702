import axios from 'axios'
import { ElMessage } from 'element-plus'

// 处理大整数ID的函数 - 在JSON解析前处理
const preprocessLargeIntegers = (jsonString) => {
  // 匹配ID字段的大整数值，将其转换为字符串
  // 匹配模式：\"id\": 数字 或 \"xxxId\": 数字 或 \"xxxID\": 数字
  // 处理超过15位的数字（可能是大整数）
  const idPattern = /"(id|[a-zA-Z]+Id|[a-zA-Z]+ID)"\s*:\s*(\d{15,})/g;

  let processedString = jsonString.replace(idPattern, (match, fieldName, value) => {
    // 检查是否是大整数（超过JavaScript安全整数范围）
    if (value.length > 15) { // 长度超过15位的数字肯定会有精度问题
      return `"${fieldName}": "${value}"`;
    }
    return match;
  });

  return processedString;
};

// 创建axios实例
const service = axios.create({
  baseURL: '/api',
  timeout: 30000, // 增加超时时间到30秒
  transformResponse: [function (data) {
    // 在JSON解析前处理大整数ID
    if (typeof data === 'string') {
      try {
        const processedData = preprocessLargeIntegers(data);
        return JSON.parse(processedData);
      } catch (e) {
        return data;
      }
    }
    return data;
  }]
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加JWT认证信息 - 从Pinia持久化存储中获取
    const userStore = JSON.parse(localStorage.getItem('user') || '{}')
    const token = userStore.token

    if (token) {
      config.headers['Authorization'] = `${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)



// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 统一处理返回格式：{code, msg/message, data}
    if (res.code !== 200 && res.code !== 0) {
      // 不在响应拦截器中显示错误消息，让业务代码自己处理
      // 这样可以避免业务代码使用Mock数据成功后，仍然显示错误消息的问题

      // 创建错误对象并保留原始响应信息
      const error = new Error(res.msg || res.message || '系统错误')
      error.response = { data: res }
      error.code = res.code
      error.isApiError = true // 标记为API错误，用于静默处理
      return Promise.reject(error)
    } else {
      return res // 返回完整响应，让业务代码自己处理
    }
  },
  error => {
    // 根据不同的错误类型给出更友好的提示
    let errorMessage = '网络请求失败'

    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接或稍后重试'
    } else if (error.code === 'ERR_NETWORK') {
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status
      switch (status) {
        case 401:
          errorMessage = '认证失败，请重新登录'
          break
        case 403:
          errorMessage = '权限不足，无法访问'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = error.response.data?.message || error.response.data?.msg || `请求失败 (${status})`
      }
    } else if (error.message) {
      errorMessage = error.message
    }

    // 只有非API错误才显示错误消息（避免双重API调用时的重复提示）
    if (!error.isApiError) {
      ElMessage.error(errorMessage)
    }
    return Promise.reject(error)
  }
)

export default service 