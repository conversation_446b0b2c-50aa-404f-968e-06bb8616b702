import request from '@/utils/request'
import { createApiFunction } from '@/config/api'

// TODO: 替换为真实API - Mock数据用于开发测试
function getDictionaryTypesMock() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data: {
          "201": "项目干系人类型",
          "203": "项目类型", 
          "205": "项目状态"
        },
        success: true
      });
    }, 200);
  });
}

// TODO: 替换为真实API - Mock数据用于开发测试
function getDictionaryMapMock(type) {
  return new Promise(resolve => {
    setTimeout(() => {
      let mockData = {};
      if (type === '205') {
        // 项目状态字典
        mockData = {
        "xmzt07": "项目暂停",
        "xmzt18": "取消",
        "xmzt05": "已结算",
        "xmzt06": "维护",
        "xmzt03": "未结算",
        "xmzt14": "正在实施",
        "xmzt04": "正在结算",
        "xmzt01": "待验收",
        "xmzt12": "未实施",
        "xmzt02": "已验收"
        };
      } else if (type === '203') {
        // 项目类型字典
        mockData = {
          "xmlx01": "软件开发",
          "xmlx02": "系统集成",
          "xmlx03": "技术咨询",
          "xmlx04": "运维服务",
          "xmlx05": "数据分析",
          "xmlx06": "移动应用",
          "xmlx07": "网站建设",
          "xmlx08": "云计算",
          "xmlx09": "人工智能",
          "xmlx10": "物联网"
        };
      }
      resolve({
        code: 200,
        msg: '操作成功',
        data: mockData,
        success: true
      });
    }, 200);
  });
}

// 获取所有字典类型 - 真实API
function getDictionaryTypesReal() {
  return request({
    url: '/dictionary/types',
    method: 'get'
  })
}

// 根据类型获取字典映射 - 真实API
function getDictionaryMapReal(type) {
  return request({
    url: '/dictionary/map',
    method: 'get',
    params: { type }
  })
}

// 导出的API函数，根据配置自动选择真实API或Mock API
export const getDictionaryTypes = createApiFunction(getDictionaryTypesReal, getDictionaryTypesMock)
export const getDictionaryMap = createApiFunction(getDictionaryMapReal, getDictionaryMapMock)

// 同时导出Mock函数供直接使用
export { getDictionaryTypesMock, getDictionaryMapMock }
