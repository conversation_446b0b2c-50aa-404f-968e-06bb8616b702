<template>
  <div class="tab-content">
    <div class="block-container">
      <div class="project-plan-review">
        <div class="review-header">
          <h3><el-icon><Document /></el-icon> 计划变更审核</h3>
          <div class="header-buttons">
            <el-button plain @click="goBack">
              <el-icon><ArrowLeft /></el-icon> 返回
            </el-button>
            <el-button type="success" @click="approveAll" :loading="reviewLoading">
              <el-icon><Check /></el-icon> 全部通过
            </el-button>
            <el-button type="danger" @click="rejectAll" :loading="reviewLoading">
              <el-icon><Close /></el-icon> 全部驳回
            </el-button>
          </div>
        </div>
        
        <div class="review-content" v-loading="loading" element-loading-text="加载审核数据中...">
          <div v-if="reviewList.length === 0 && !loading" class="empty-state">
            <el-empty description="暂无待审核的计划变更" />
          </div>
          
          <div v-else class="review-list">
            <div 
              v-for="item in reviewList" 
              :key="item.id" 
              class="review-item"
              :class="{ 'changed': item.changeType }"
            >
              <div class="item-header">
                <div class="item-info">
                  <span class="item-name">{{ item.planName }}</span>
                  <el-tag 
                    v-if="item.changeType" 
                    :type="getChangeTypeColor(item.changeType)" 
                    size="small"
                    class="change-tag"
                  >
                    {{ getChangeTypeText(item.changeType) }}
                  </el-tag>
                </div>
                <div class="item-actions">
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="approveItem(item)"
                    :loading="item.approving"
                  >
                    通过
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="rejectItem(item)"
                    :loading="item.rejecting"
                  >
                    驳回
                  </el-button>
                </div>
              </div>
              
              <div class="item-details">
                <div class="detail-row">
                  <span class="label">序号：</span>
                  <span class="value">{{ item.seriNum }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">负责人：</span>
                  <span class="value">{{ item.ownerName }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">计划时间：</span>
                  <span class="value">
                    {{ formatDate(item.planStartTime) }} ~ {{ formatDate(item.planEndTime) }}
                  </span>
                </div>
                <div v-if="item.changeRemark" class="detail-row">
                  <span class="label">变更说明：</span>
                  <span class="value change-remark">{{ item.changeRemark }}</span>
                </div>
              </div>
              
              <div class="review-opinion">
                <el-input
                  v-model="item.reviewOpinion"
                  type="textarea"
                  placeholder="请输入审核意见（可选）"
                  :rows="2"
                  maxlength="200"
                  show-word-limit
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, ArrowLeft, Check, Close } from '@element-plus/icons-vue'
import { 
  getProjectWorkItemReviewList,
  batchReviewWorkItems
} from '@/api/projectPlanDraft'

const route = useRoute()
const router = useRouter()

// 获取项目ID
const projectId = route.params.projectId

// 数据状态
const loading = ref(false)
const reviewLoading = ref(false)
const reviewList = ref([])

// 获取审核列表
const loadReviewList = async () => {
  if (!projectId) {
    ElMessage.error('项目ID不能为空')
    return
  }
  
  loading.value = true
  try {
    const response = await getProjectWorkItemReviewList(projectId)
    
    if (response && response.data) {
      // 为每个项目添加审核意见和loading状态
      reviewList.value = response.data.map(item => ({
        ...item,
        reviewOpinion: '',
        approving: false,
        rejecting: false
      }))
    } else {
      reviewList.value = []
    }
  } catch (error) {
    console.error('获取审核列表失败:', error)
    ElMessage.error('获取审核列表失败')
    reviewList.value = []
  } finally {
    loading.value = false
  }
}

// 获取变更类型颜色
const getChangeTypeColor = (changeType) => {
  const colorMap = {
    'CREATE': 'success',
    'UPDATE': 'warning', 
    'DELETE': 'danger'
  }
  return colorMap[changeType] || 'info'
}

// 获取变更类型文本
const getChangeTypeText = (changeType) => {
  const textMap = {
    'CREATE': '新增',
    'UPDATE': '修改',
    'DELETE': '删除'
  }
  return textMap[changeType] || changeType
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dateStr.split(' ')[0]
}

// 返回上一页
const goBack = () => {
  const from = route.query.from
  if (from === 'projectDetail') {
    router.push({
      name: 'ProjectDetail',
      params: { id: projectId }
    })
  } else {
    router.back()
  }
}

// 审核单个项目
const reviewSingleItem = async (item, reviewResult) => {
  const loadingKey = reviewResult === '1' ? 'approving' : 'rejecting'
  item[loadingKey] = true
  
  try {
    const reviewData = {
      projectId,
      reviewResult, // '1': 通过, '2': 驳回
      reviewInfoList: [{
        draftId: item.id,
        reviewOpinion: item.reviewOpinion || '',
        version: item.version || 1
      }]
    }
    
    await batchReviewWorkItems(reviewData)
    
    ElMessage.success(reviewResult === '1' ? '审核通过' : '审核驳回')
    
    // 从列表中移除已审核的项目
    const index = reviewList.value.findIndex(review => review.id === item.id)
    if (index !== -1) {
      reviewList.value.splice(index, 1)
    }
    
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败: ' + (error.message || '未知错误'))
  } finally {
    item[loadingKey] = false
  }
}

// 通过单个项目
const approveItem = (item) => {
  reviewSingleItem(item, '1')
}

// 驳回单个项目
const rejectItem = (item) => {
  reviewSingleItem(item, '2')
}

// 批量审核
const batchReview = async (reviewResult) => {
  if (reviewList.value.length === 0) {
    ElMessage.warning('没有待审核的项目')
    return
  }
  
  const actionText = reviewResult === '1' ? '通过' : '驳回'
  
  try {
    await ElMessageBox.confirm(
      `确认${actionText}所有待审核的计划变更？`,
      `批量${actionText}`,
      {
        type: 'warning',
        confirmButtonText: `确认${actionText}`,
        cancelButtonText: '取消'
      }
    )
    
    reviewLoading.value = true
    
    const reviewData = {
      projectId,
      reviewResult,
      reviewInfoList: reviewList.value.map(item => ({
        draftId: item.id,
        reviewOpinion: item.reviewOpinion || '',
        version: item.version || 1
      }))
    }
    
    await batchReviewWorkItems(reviewData)
    
    ElMessage.success(`批量${actionText}成功`)
    
    // 清空审核列表
    reviewList.value = []
    
  } catch (error) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error(`批量${actionText}失败:`, error)
    ElMessage.error(`批量${actionText}失败: ` + (error.message || '未知错误'))
  } finally {
    reviewLoading.value = false
  }
}

// 全部通过
const approveAll = () => {
  batchReview('1')
}

// 全部驳回
const rejectAll = () => {
  batchReview('2')
}

// 组件挂载时加载数据
onMounted(() => {
  loadReviewList()
})
</script>

<style lang="scss" scoped>
/* tab内容容器 */
.tab-content {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
}

/* 白色圆角背景区块 */
.block-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin: 0;
  padding-bottom: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  box-sizing: border-box;
}

/* 项目计划审核容器 */
.project-plan-review {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  padding: 20px 20px 0;
  height: 100%;
}

/* 审核头部 */
.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.review-header h3 {
  font-size: 16px;
  margin: 0;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.review-header h3 .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

/* 审核内容 */
.review-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 审核列表 */
.review-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
  transition: all 0.3s ease;
  
  &.changed {
    border-color: #f56c6c;
    background-color: #fef0f0;
  }
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.change-tag {
  font-size: 12px;
}

.item-actions {
  display: flex;
  gap: 8px;
}

/* 项目详情 */
.item-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 13px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.label {
  color: #909399;
  margin-right: 8px;
  min-width: 60px;
}

.value {
  color: #303133;
  flex: 1;
}

.change-remark {
  grid-column: 1 / -1;
  background-color: #fff3cd;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
}

/* 审核意见 */
.review-opinion {
  margin-top: 12px;
}

/* 滚动条样式 */
.review-content::-webkit-scrollbar {
  width: 6px;
}

.review-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.review-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.review-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 