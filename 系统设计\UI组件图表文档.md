# 交付部项目管理系统 - UI组件图表文档

## 1. 工作台图表组件

### 1.1 项目状态分布图

**功能描述**：显示不同状态项目的数量分布。

**图表类型**：饼图

**数据结构**：
```javascript
[
  { name: '在建', value: 20, itemStyle: { color: '#2A6AF2' } },
  { name: '已完成', value: 15, itemStyle: { color: '#52C41A' } },
  { name: '未开工', value: 10, itemStyle: { color: '#FAAD14' } },
  { name: '超期', value: 5, itemStyle: { color: '#F5222D' } }
]
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    data: ['在建', '已完成', '未开工', '超期']
  },
  series: [
    {
      name: '项目状态',
      type: 'pie',
      radius: ['50%', '70%'], // 环形图
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: // 上述数据结构
    }
  ]
}
```

### 1.2 项目进度趋势图

**功能描述**：显示项目完成率随时间的变化趋势。

**图表类型**：折线图

**数据结构**：
```javascript
{
  xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
  seriesData: [30, 45, 52, 60, 75, 85]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: xAxisData // 上述数据的xAxisData
  },
  yAxis: {
    type: 'value',
    name: '完成率(%)',
    max: 100,
    min: 0
  },
  series: [
    {
      name: '项目完成率',
      type: 'line',
      data: seriesData, // 上述数据的seriesData
      smooth: true,
      areaStyle: {
        opacity: 0.3
      },
      itemStyle: {
        color: '#2A6AF2'
      }
    }
  ]
}
```

### 1.3 合同金额与回款统计图

**功能描述**：对比项目合同金额与实际回款金额。

**图表类型**：柱状图

**数据结构**：
```javascript
{
  xAxisData: ['项目A', '项目B', '项目C', '项目D', '项目E'],
  contractAmounts: [500, 800, 600, 400, 700],
  paymentAmounts: [300, 750, 400, 300, 650]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: ['合同金额', '回款金额']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: xAxisData, // 上述数据的xAxisData
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '金额(万元)'
    }
  ],
  series: [
    {
      name: '合同金额',
      type: 'bar',
      barWidth: '40%',
      data: contractAmounts, // 上述数据的contractAmounts
      itemStyle: {
        color: '#2A6AF2'
      }
    },
    {
      name: '回款金额',
      type: 'bar',
      barWidth: '40%',
      data: paymentAmounts, // 上述数据的paymentAmounts
      itemStyle: {
        color: '#52C41A'
      }
    }
  ]
}
```

## 2. 项目详情图表组件

### 2.1 项目进度甘特图

**功能描述**：以甘特图形式展示项目工作项的计划与实际进度。

**图表类型**：甘特图

**数据结构**：
```javascript
{
  tasks: [
    {
      name: '需求分析',
      start: '2023-01-01',
      end: '2023-01-15',
      completed: {
        start: '2023-01-01',
        end: '2023-01-13'
      }
    },
    {
      name: '设计',
      start: '2023-01-16',
      end: '2023-01-30',
      completed: {
        start: '2023-01-16',
        end: '2023-01-28'
      },
      dependencies: '需求分析'
    },
    {
      name: '开发',
      start: '2023-01-31',
      end: '2023-02-28',
      completed: {
        start: '2023-01-31',
        end: '2023-02-20' // 进行中
      },
      dependencies: '设计'
    },
    {
      name: '测试',
      start: '2023-03-01',
      end: '2023-03-15',
      dependencies: '开发'
    },
    {
      name: '部署上线',
      start: '2023-03-16',
      end: '2023-03-30',
      dependencies: '测试'
    }
  ]
}
```

**配置参数**：
使用自定义甘特图组件，渲染上述数据结构，支持如下功能：
- 显示计划进度条和实际进度条
- 任务之间的依赖关系连线
- 鼠标悬停显示详情
- 时间轴缩放
- 里程碑标记

### 2.2 资源分配图

**功能描述**：展示项目中人力资源分配情况。

**图表类型**：堆叠条形图

**数据结构**：
```javascript
{
  yAxisData: ['UI设计', '前端开发', '后端开发', '测试', '产品'],
  seriesData: [
    {
      name: '已分配',
      data: [3, 5, 4, 3, 2]
    },
    {
      name: '计划分配',
      data: [5, 7, 6, 4, 3]
    }
  ]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: ['已分配', '计划分配']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '人数'
  },
  yAxis: {
    type: 'category',
    data: yAxisData // 上述数据的yAxisData
  },
  series: [
    {
      name: '已分配',
      type: 'bar',
      stack: '总量',
      data: seriesData[0].data // 上述数据的已分配
    },
    {
      name: '计划分配',
      type: 'bar',
      stack: '总量',
      data: seriesData[1].data.map((item, index) => 
        item - seriesData[0].data[index] // 计算差值
      ),
      itemStyle: {
        color: '#FAAD14',
        opacity: 0.6
      }
    }
  ]
}
```

### 2.3 项目风险雷达图

**功能描述**：展示项目在不同维度的风险评估。

**图表类型**：雷达图

**数据结构**：
```javascript
{
  indicator: [
    { name: '进度风险', max: 100 },
    { name: '成本风险', max: 100 },
    { name: '质量风险', max: 100 },
    { name: '资源风险', max: 100 },
    { name: '范围风险', max: 100 },
    { name: '技术风险', max: 100 }
  ],
  seriesData: [
    {
      value: [70, 50, 30, 40, 60, 80],
      name: '当前风险'
    },
    {
      value: [50, 30, 20, 30, 40, 60],
      name: '上月风险'
    }
  ]
}
```

**配置参数**：
```javascript
{
  tooltip: {},
  legend: {
    data: ['当前风险', '上月风险']
  },
  radar: {
    indicator: indicator // 上述数据的indicator
  },
  series: [{
    type: 'radar',
    data: [
      {
        value: seriesData[0].value,
        name: '当前风险',
        itemStyle: {
          color: '#F5222D'
        },
        areaStyle: {
          color: 'rgba(245, 34, 45, 0.3)'
        }
      },
      {
        value: seriesData[1].value,
        name: '上月风险',
        itemStyle: {
          color: '#FAAD14'
        },
        areaStyle: {
          color: 'rgba(250, 173, 20, 0.3)'
        }
      }
    ]
  }]
}
```

## 3. 项目工作项图表组件

### 3.1 工作项状态分布图

**功能描述**：展示工作项在不同状态的分布情况。

**图表类型**：环形图

**数据结构**：
```javascript
[
  { name: '待处理', value: 15, itemStyle: { color: '#FAAD14' } },
  { name: '进行中', value: 25, itemStyle: { color: '#2A6AF2' } },
  { name: '已完成', value: 30, itemStyle: { color: '#52C41A' } },
  { name: '已验收', value: 20, itemStyle: { color: '#1B2A4B' } },
  { name: '已延期', value: 10, itemStyle: { color: '#F5222D' } }
]
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: 10,
    data: ['待处理', '进行中', '已完成', '已验收', '已延期']
  },
  series: [
    {
      name: '工作项状态',
      type: 'pie',
      radius: ['40%', '60%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {c}'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true
      },
      data: // 上述数据结构
    }
  ]
}
```

### 3.2 工作项完成趋势图

**功能描述**：展示项目工作项完成情况随时间的变化。

**图表类型**：折线图+柱状图

**数据结构**：
```javascript
{
  xAxisData: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周'],
  completedTasks: [5, 12, 18, 23, 35, 42],
  addedTasks: [15, 10, 8, 12, 15, 8],
  totalTasks: [15, 25, 33, 45, 60, 68]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['已完成工作项', '新增工作项', '工作项总数']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: xAxisData, // 上述数据的xAxisData
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '工作项数量',
      min: 0
    }
  ],
  series: [
    {
      name: '已完成工作项',
      type: 'bar',
      barWidth: '30%',
      data: completedTasks, // 上述数据的completedTasks
      itemStyle: {
        color: '#52C41A'
      }
    },
    {
      name: '新增工作项',
      type: 'bar',
      barWidth: '30%',
      data: addedTasks, // 上述数据的addedTasks
      itemStyle: {
        color: '#2A6AF2'
      }
    },
    {
      name: '工作项总数',
      type: 'line',
      data: totalTasks, // 上述数据的totalTasks
      itemStyle: {
        color: '#1B2A4B'
      }
    }
  ]
}
```

### 3.3 工作项优先级分布图

**功能描述**：展示不同优先级工作项的分布情况。

**图表类型**：漏斗图

**数据结构**：
```javascript
[
  { name: '紧急', value: 5, itemStyle: { color: '#F5222D' } },
  { name: '高', value: 15, itemStyle: { color: '#FAAD14' } },
  { name: '中', value: 30, itemStyle: { color: '#2A6AF2' } },
  { name: '低', value: 50, itemStyle: { color: '#52C41A' } }
]
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}'
  },
  legend: {
    data: ['紧急', '高', '中', '低']
  },
  series: [
    {
      name: '工作项优先级',
      type: 'funnel',
      left: '10%',
      top: 60,
      bottom: 60,
      width: '80%',
      min: 0,
      max: 100,
      minSize: '0%',
      maxSize: '100%',
      sort: 'descending',
      gap: 2,
      label: {
        show: true,
        position: 'inside'
      },
      labelLine: {
        length: 10,
        lineStyle: {
          width: 1,
          type: 'solid'
        }
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1
      },
      emphasis: {
        label: {
          fontSize: 14
        }
      },
      data: // 上述数据结构
    }
  ]
}
```

## 4. 资源管理图表组件

### 4.1 人力资源负载图

**功能描述**：展示团队成员工作负载情况。

**图表类型**：条形图

**数据结构**：
```javascript
{
  yAxisData: ['张三', '李四', '王五', '赵六', '钱七'],
  seriesData: [
    {
      name: '已分配工时',
      data: [35, 40, 28, 32, 25]
    },
    {
      name: '最大工时',
      data: [40, 40, 40, 40, 40]
    }
  ]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params) {
      const assigned = params[0].value;
      const max = params[1] ? params[1].value : 40;
      const percentage = Math.round(assigned / max * 100);
      return `${params[0].name}<br/>已分配: ${assigned}小时<br/>负载率: ${percentage}%`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '工时',
    max: 45
  },
  yAxis: {
    type: 'category',
    data: yAxisData // 上述数据的yAxisData
  },
  series: [
    {
      name: '已分配工时',
      type: 'bar',
      data: seriesData[0].data, // 上述数据的已分配工时
      itemStyle: function(params) {
        // 根据负载比例显示不同颜色
        const value = params.value;
        const max = 40;
        const ratio = value / max;
        if (ratio > 0.9) return { color: '#F5222D' };
        if (ratio > 0.7) return { color: '#FAAD14' };
        return { color: '#52C41A' };
      }
    },
    {
      name: '最大工时',
      type: 'pictorialBar',
      symbol: 'rect',
      itemStyle: {
        color: 'none',
        borderColor: '#1B2A4B',
        borderWidth: 1
      },
      symbolRepeat: true,
      symbolSize: [10, 4],
      symbolMargin: 1,
      symbolPosition: 'end',
      data: seriesData[1].data, // 上述数据的最大工时
      z: 10
    }
  ]
}
```

### 4.2 工作项分配热力图

**功能描述**：通过热力图展示团队成员工作项分配密度。

**图表类型**：热力图

**数据结构**：
```javascript
{
  xAxisData: ['周一', '周二', '周三', '周四', '周五'],
  yAxisData: ['张三', '李四', '王五', '赵六', '钱七'],
  seriesData: [
    [0, 0, 5],
    [0, 1, 3],
    [0, 2, 6],
    [0, 3, 2],
    [0, 4, 4],
    [1, 0, 7],
    [1, 1, 8],
    [1, 2, 2],
    [1, 3, 3],
    [1, 4, 1],
    // ...更多数据点
  ]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    position: 'top',
    formatter: function (params) {
      return `${params.marker} ${yAxisData[params.data[1]]} ${xAxisData[params.data[0]]}<br/>工作项: ${params.data[2]}个`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: xAxisData, // 上述数据的xAxisData
    splitArea: {
      show: true
    }
  },
  yAxis: {
    type: 'category',
    data: yAxisData, // 上述数据的yAxisData
    splitArea: {
      show: true
    }
  },
  visualMap: {
    min: 0,
    max: 10,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '0%',
    inRange: {
      color: ['#e6f7ff', '#096dd9']
    }
  },
  series: [{
    name: '工作项分配',
    type: 'heatmap',
    data: seriesData, // 上述数据的seriesData
    label: {
      show: true
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}
```

## 5. 财务图表组件

### 5.1 项目收支情况图

**功能描述**：展示项目收入、支出以及利润情况。

**图表类型**：瀑布图

**数据结构**：
```javascript
{
  xAxisData: ['合同收入', '材料成本', '人工成本', '其他支出', '利润'],
  values: [1000, -300, -400, -100, 200],
  itemStyles: [
    { color: '#2A6AF2' },
    { color: '#F5222D' },
    { color: '#F5222D' },
    { color: '#F5222D' },
    { color: '#52C41A' }
  ]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params) {
      const value = params[0].value;
      return `${params[0].name}: ${Math.abs(value)}万元`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: xAxisData // 上述数据的xAxisData
  },
  yAxis: {
    type: 'value',
    name: '金额(万元)'
  },
  series: {
    name: '项目收支',
    type: 'bar',
    stack: '总量',
    label: {
      show: true,
      position: 'top',
      formatter: function(params) {
        return Math.abs(params.value) + '万';
      }
    },
    data: values.map((value, index) => ({
      value: value,
      itemStyle: itemStyles[index]
    }))
  }
}
```

### 5.2 回款计划与实际对比图

**功能描述**：对比项目回款计划与实际回款情况。

**图表类型**：折线图+柱状图

**数据结构**：
```javascript
{
  xAxisData: ['Q1', 'Q2', 'Q3', 'Q4'],
  plannedPayments: [100, 300, 200, 400],
  actualPayments: [80, 250, 180, 0], // Q4尚未到来
  cumulativePlanned: [100, 400, 600, 1000],
  cumulativeActual: [80, 330, 510, 510]
}
```

**配置参数**：
```javascript
{
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['计划回款', '实际回款', '计划累计', '实际累计']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: xAxisData, // 上述数据的xAxisData
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '金额(万元)',
      min: 0
    }
  ],
  series: [
    {
      name: '计划回款',
      type: 'bar',
      barWidth: '40%',
      data: plannedPayments, // 上述数据的plannedPayments
      itemStyle: {
        color: '#2A6AF2'
      },
      z: 10
    },
    {
      name: '实际回款',
      type: 'bar',
      barWidth: '40%',
      data: actualPayments, // 上述数据的actualPayments
      itemStyle: {
        color: '#52C41A'
      },
      z: 10
    },
    {
      name: '计划累计',
      type: 'line',
      smooth: true,
      data: cumulativePlanned, // 上述数据的cumulativePlanned
      itemStyle: {
        color: '#1B2A4B'
      },
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(0,0,0,0.3)',
        shadowBlur: 10,
        shadowOffsetY: 8
      }
    },
    {
      name: '实际累计',
      type: 'line',
      smooth: true,
      data: cumulativeActual, // 上述数据的cumulativeActual
      itemStyle: {
        color: '#FAAD14'
      },
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(0,0,0,0.3)',
        shadowBlur: 10,
        shadowOffsetY: 8
      }
    }
  ]
}
```

## 6. 图表组件封装设计

为了提高开发效率和代码复用性，系统将基于ECharts封装一系列可复用的图表组件。

### 6.1 基础图表组件 (BaseChart.vue)

```vue
<template>
  <div class="chart-container" :style="{ height: height, width: width }">
    <div v-if="loading" class="chart-loading">
      <el-spinner />
    </div>
    <div v-else-if="error" class="chart-error">
      <el-alert
        title="图表加载失败"
        type="error"
        :description="error"
        show-icon
      />
    </div>
    <div v-else-if="isEmpty" class="chart-empty">
      <el-empty description="暂无数据" />
    </div>
    <div v-else ref="chartRef" class="chart-instance"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts/core';
import { debounce } from 'lodash-es';

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  height: {
    type: String,
    default: '350px'
  },
  width: {
    type: String,
    default: '100%'
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  autoResize: {
    type: Boolean,
    default: true
  },
  theme: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['chartInit', 'chartClick', 'chartUpdated']);

const chartRef = ref(null);
const chart = ref(null);
const isEmpty = ref(false);

// 检查数据是否为空
const checkIsEmpty = (options) => {
  if (!options || !options.series) return true;
  
  return options.series.every(s => {
    if (!s.data) return true;
    if (Array.isArray(s.data)) return s.data.length === 0;
    return Object.keys(s.data).length === 0;
  });
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  isEmpty.value = checkIsEmpty(props.options);
  if (isEmpty.value) return;
  
  chart.value = echarts.init(chartRef.value, props.theme);
  chart.value.setOption(props.options);
  
  chart.value.on('click', (params) => {
    emit('chartClick', params);
  });
  
  emit('chartInit', chart.value);
};

// 窗口大小变化时重新调整图表
const handleResize = debounce(() => {
  if (chart.value) {
    chart.value.resize();
  }
}, 100);

// 监听options变化更新图表
watch(
  () => props.options,
  (newOptions) => {
    isEmpty.value = checkIsEmpty(newOptions);
    
    if (chart.value && !isEmpty.value) {
      chart.value.setOption(newOptions, true);
      emit('chartUpdated');
    } else if (!chart.value && !isEmpty.value) {
      initChart();
    }
  },
  { deep: true }
);

// 监听主题变化
watch(
  () => props.theme,
  (newTheme) => {
    if (chart.value) {
      chart.value.dispose();
      chart.value = echarts.init(chartRef.value, newTheme);
      chart.value.setOption(props.options);
    }
  }
);

onMounted(() => {
  initChart();
  
  if (props.autoResize) {
    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
  
  if (props.autoResize) {
    window.removeEventListener('resize', handleResize);
  }
});

// 导出方法给父组件调用
defineExpose({
  getChart: () => chart.value,
  resize: handleResize
});
</script>

<style scoped>
.chart-container {
  position: relative;
}

.chart-instance {
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-error,
.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
</style>
```

### 6.2 业务图表组件示例 (ProjectStatusChart.vue)

```vue
<template>
  <base-chart
    :options="chartOptions"
    :height="height"
    :loading="loading"
    :error="error"
    @chart-click="handleChartClick"
  />
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import BaseChart from './BaseChart.vue';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  height: {
    type: String,
    default: '300px'
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['itemClick']);

const chartOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: props.data.map(item => item.name),
      show: props.showLegend
    },
    series: [
      {
        name: '项目状态',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data
      }
    ]
  };
});

const handleChartClick = (params) => {
  emit('itemClick', {
    name: params.name,
    value: params.value,
    index: params.dataIndex
  });
};
</script>
```

通过这种方式，可以封装各类图表组件，实现高效复用，同时统一处理加载状态、错误状态和空数据状态。 