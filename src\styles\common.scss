@import "./variables.scss";
@import "./mixins.scss";

/* 全局通用样式 */

// 重置一些基础元素样式
html, body {
  margin: 0;
  padding: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-size: $font-size-base;
  color: $text-primary;
  background-color: $background-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
}

* {
  box-sizing: border-box;
}

// 主应用容器样式
.app-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// tab内容容器样式
.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 页面内容区样式
.page-content {
  padding: $spacing-base;
  flex: 1;
  overflow: auto;
  min-height: 0;
}

// 页头通用样式
.page-header,
.plan-header,
.daily-header,
.stakeholders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: none;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  margin-left: 5px;
  margin-right: 5px;
  box-shadow: $box-shadow-header;
}

// 页面标题通用样式
.page-title,
.page-header h3,
.plan-header h3,
.daily-header h3,
.stakeholders-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 6px;
    font-size: 16px;
    color: $primary-color;
  }
}

// 搜索区域通用样式
.search-area,
.daily-search {
  display: flex;
  align-items: center;
  gap: 8px;

  .label {
    font-size: 14px;
    color: $text-regular;
  }

  .search-item {
    display: flex;
    align-items: center;
  }

  .el-button + .el-button {
    margin-left: 0;
  }
}

// 工具栏通用样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-small;
}

// 表格容器通用样式
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid $border-color;
  border-radius: 4px;
  background-color: $background-color-white;
  margin: 0 5px 5px 5px;
  overflow: hidden;
  min-height: 0;
}

// 表格包装器通用样式
.table-wrapper {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

// 项目计划、日报、干系人通用样式
.project-plan,
.daily-report,
.stakeholders-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  overflow: hidden;
}

// 卡片通用样式
.card {
  background-color: $background-color-white;
  border-radius: $border-radius-medium;
  box-shadow: $box-shadow-light;
  padding: $spacing-base;
  margin-bottom: $spacing-base;
}

// 序号列通用样式
.serial-column {
  background-color: $background-color-base !important;
}

// 上传组件通用样式
.upload-box {
  position: relative;
  width: 120px;
  height: 120px;
  border: 1px dashed $border-color;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $background-color-base;
  transition: all 0.3s;
  cursor: pointer;
  margin-right: $spacing-small;
  margin-bottom: $spacing-small;

  &:hover {
    border-color: $primary-color;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.upload-trigger {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: transparent;

  .el-icon {
    font-size: 28px;
    color: $text-secondary;
    margin-bottom: $spacing-mini;
  }

  span {
    color: $text-secondary;
    font-size: $font-size-small;
  }
}

.upload-tip {
  margin-top: $spacing-mini;
  font-size: $font-size-small;
  color: $text-secondary;
  line-height: 1.5;
}

.upload-tip-inline {
  margin-left: 20px;
  color: #909399;
  font-size: 13px;
}

// 上传提示区域样式
.upload-notice {
  padding: 10px 16px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid $primary-color;
  margin-top: 5px;
}

.upload-list-title {
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
}

.numbering {
  font-weight: bold;
  color: $primary-color;
  margin-right: 4px;
}

// 图片预览区样式
.image-preview-area,
.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  margin-bottom: 15px;
}

.image-thumb-box {
  position: relative;
  width: 120px;
  height: 120px;
  margin-right: $spacing-small;
  margin-bottom: $spacing-small;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-base;
  overflow: hidden;

  .image-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
  }

  .image-thumb:hover {
    transform: scale(1.05);
  }

  .image-thumb-empty {
    font-size: 12px;
    color: #909399;
    text-align: center;
    line-height: 120px;
  }

  .image-delete,
  .delete-icon {
    position: absolute;
    top: 2px;
    right: 2px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.7);
    color: #f56c6c;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.9);
      transform: scale(1.1);
    }
  }

  .thumb-delete {
    font-size: 16px;
  }
  
  .file-name-thumb {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 12px;
    text-align: center;
    padding: 2px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    z-index: 9;
  }
}

// 上传容器样式
.upload-container {
  padding: 20px;
  position: relative;
}

// 对话框底部按钮区样式
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 0;
}

// 预览容器样式
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
} 