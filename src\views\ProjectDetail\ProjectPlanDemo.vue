<template>
  <div class="plan-demo-container">
    <div class="demo-header">
      <h2>项目计划详情功能演示</h2>
      <p>演示如何从项目计划页面跳转到计划详情页面</p>
    </div>

    <div class="demo-content">
      <!-- 项目信息 -->
      <el-card class="demo-card">
        <template #header>
          <span>项目信息</span>
        </template>
        
        <el-form :model="projectInfo" label-width="120px">
          <el-form-item label="项目ID">
            <el-input v-model="projectInfo.projectId" placeholder="请输入项目ID" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model="projectInfo.projectName" placeholder="请输入项目名称" />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 计划列表 -->
      <el-card class="demo-card">
        <template #header>
          <span>计划列表</span>
        </template>
        
        <el-table :data="planList" border stripe>
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="计划ID" prop="id" width="80" />
          <el-table-column label="计划名称" prop="taskName" />
          <el-table-column label="责任人" prop="responsible" width="100" />
          <el-table-column label="状态" prop="status" width="100" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                type="primary" 
                link 
                size="small" 
                @click="viewPlanDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 跳转结果 -->
      <el-card class="demo-card" v-if="jumpResult">
        <template #header>
          <span>跳转结果</span>
        </template>
        
        <div class="jump-result">
          <p><strong>跳转路径：</strong>{{ jumpResult.path }}</p>
          <p><strong>项目ID：</strong>{{ jumpResult.projectId }}</p>
          <p><strong>计划ID：</strong>{{ jumpResult.planId }}</p>
          <p><strong>计划名称：</strong>{{ jumpResult.planName }}</p>
        </div>
        
        <el-button type="primary" @click="goToPlanDetail" :disabled="!jumpResult">
          确认跳转到计划详情页面
        </el-button>
      </el-card>

      <!-- 序号列样式测试 -->
      <el-card class="demo-card">
        <template #header>
          <span>序号列样式测试</span>
        </template>
        
        <div class="style-test">
          <p>测试不同层级的序号显示效果：</p>
          <div class="test-items">
            <div class="test-item" style="padding-left: 4px;">1</div>
            <div class="test-item" style="padding-left: 10px;">1.1</div>
            <div class="test-item" style="padding-left: 16px;">1.1.1</div>
            <div class="test-item" style="padding-left: 22px;">1.1.1.1</div>
            <div class="test-item" style="padding-left: 10px;">1.2</div>
            <div class="test-item" style="padding-left: 16px;">1.2.1</div>
            <div class="test-item" style="padding-left: 4px;">2</div>
            <div class="test-item" style="padding-left: 10px;">2.1</div>
          </div>
        </div>
      </el-card>

      <!-- 树形表格展开收起测试 -->
      <div class="test-section">
        <h3>树形表格展开收起测试</h3>
        <p>测试说明：点击展开/收起按钮，应该只影响当前节点及其子节点，不会影响其他节点</p>
        
        <el-table 
          :data="treeTestData" 
          border
          row-key="id"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          class="test-table"
          @expand-change="handleTestExpandChange"
        >
          <el-table-column label="序号" width="100" align="left">
            <template #default="scope">
              <span>{{ scope.row.serialNumber }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="名称" min-width="200">
            <template #default="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <span>{{ scope.row.status }}</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="test-info">
          <p>当前展开的行ID: {{ testExpandedRows.join(', ') || '无' }}</p>
          <el-button @click="resetTestExpanded">重置展开状态</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const router = useRouter();

// 项目信息
const projectInfo = reactive({
  projectId: '1',
  projectName: '满洲里市互贸区出入境查验设施改造项目'
});

// 模拟计划列表数据
const planList = ref([
  {
    id: 1,
    taskName: '启动阶段-项目启动',
    responsible: '张三',
    status: '进行中'
  },
  {
    id: 2,
    taskName: '需求分析阶段',
    responsible: '李四',
    status: '已完成'
  },
  {
    id: 3,
    taskName: '设计阶段',
    responsible: '王五',
    status: '进行中'
  },
  {
    id: 4,
    taskName: '开发阶段',
    responsible: '赵六',
    status: '未开始'
  }
]);

// 跳转结果
const jumpResult = ref(null);

// 树形表格测试数据
const treeTestData = ref([
  {
    id: 1,
    serialNumber: '1',
    name: '项目启动',
    status: '进行中',
    level: 1,
    hasChildren: true,
    children: [
      {
        id: 11,
        serialNumber: '1.1',
        name: '需求分析',
        status: '已完成',
        level: 2,
        hasChildren: true,
        children: [
          {
            id: 111,
            serialNumber: '1.1.1',
            name: '用户需求调研',
            status: '已完成',
            level: 3,
            hasChildren: false,
            children: []
          }
        ]
      },
      {
        id: 12,
        serialNumber: '1.2',
        name: '系统设计',
        status: '进行中',
        level: 2,
        hasChildren: false,
        children: []
      }
    ]
  },
  {
    id: 2,
    serialNumber: '2',
    name: '开发阶段',
    status: '未开始',
    level: 1,
    hasChildren: true,
    children: [
      {
        id: 21,
        serialNumber: '2.1',
        name: '前端开发',
        status: '未开始',
        level: 2,
        hasChildren: false,
        children: []
      }
    ]
  },
  {
    id: 3,
    serialNumber: '3',
    name: '测试阶段',
    status: '未开始',
    level: 1,
    hasChildren: false,
    children: []
  }
]);

// 测试展开收起状态
const testExpandedRows = ref([]);

// 查看计划详情
function viewPlanDetail(row) {
  jumpResult.value = {
    path: `/project-plan/${projectInfo.projectId}/${row.id}`,
    projectId: projectInfo.projectId,
    planId: row.id,
    planName: row.taskName
  };
  
  ElMessage.success(`准备跳转到计划详情：${row.taskName}`);
}

// 跳转到计划详情页面
function goToPlanDetail() {
  if (!jumpResult.value) {
    ElMessage.warning('请先选择要查看的计划');
    return;
  }
  
  router.push({
    name: 'ProjectPlanDetail',
    params: {
      projectId: jumpResult.value.projectId,
      planId: jumpResult.value.planId
    },
    query: {
      projectName: projectInfo.projectName
    }
  });
}

// 处理树形表格展开/收起变化
function handleTestExpandChange(row, expanded) {
  // Element Plus 自动管理展开收起
}

// 重置树形表格展开状态
function resetTestExpanded() {
  testExpandedRows.value = [];
}
</script>

<style scoped>
.plan-demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #303133;
  margin-bottom: 10px;
}

.demo-header p {
  color: #606266;
  font-size: 14px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.jump-result {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.jump-result p {
  margin: 5px 0;
  color: #606266;
}

.jump-result strong {
  color: #303133;
}

.style-test {
  margin-top: 15px;
}

.test-items {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
}

.test-item {
  font-size: 12px;
  line-height: 32px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.test-section {
  margin-top: 15px;
}

.test-section h3 {
  color: #303133;
  margin-bottom: 10px;
}

.test-section p {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
}

.test-table {
  width: 100%;
}

.test-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-info p {
  margin: 5px 0;
  color: #606266;
}
</style> 