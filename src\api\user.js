import request from '@/utils/request'

// 角色ID常量
// 注意：这些ID需要根据实际系统的角色配置进行调整
export const ROLE_IDS = {
  SALES: '3', // 销售角色ID，请根据实际系统中的角色ID进行调整
}

// 查询所有销售用户（已废弃，使用 getUsersByRole 替代）
export function getSalesUsers() {
  return request({
    url: '/users/sales',
    method: 'get'
  })
}

// 根据角色ID查询用户
export function getUsersByRole(roleId) {
  return request({
    url: '/users/by-role',
    method: 'get',
    params: {
      roleId
    }
  })
}

// 获取销售用户（使用新接口）
export function getSalesUsersByRole(roleId = ROLE_IDS.SALES) {
  return getUsersByRole(roleId)
}

// 用户列表查询（包含区域和角色信息）
export function getUserList(params) {
  return request({
    url: '/users/list',
    method: 'get',
    params
  })
}

// 用户信息编辑
export function editUser(data) {
  return request({
    url: '/users/edit',
    method: 'put',
    data
  })
}

// 查询当前登录用户信息
export function getCurrentUser() {
  return request({
    url: '/users/me',
    method: 'get'
  })
}

// ================= 角色管理 =================

// 角色列表查询
export function getRoleList(params) {
  return request({
    url: '/role/list',
    method: 'get',
    params
  })
}

// 获取所有角色
export function getAllRoles() {
  return request({
    url: '/role/all',
    method: 'get'
  })
}

// 更新角色
export function updateRole(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data
  })
}

// 根据角色ID获取菜单ID列表
export function getMenuIdsByRole(roleId) {
  return request({
    url: '/menu/ids-by-role',
    method: 'get',
    params: { roleId }
  })
}

// 获取菜单树
export function getMenuTree(name) {
  return request({
    url: '/menu/tree',
    method: 'get',
    params: name ? { name } : {}
  })
}



// ================= 区域管理 =================

// 获取区域列表
export function getAreaList() {
  return request({
    url: '/area/list',
    method: 'get'
  })
}

